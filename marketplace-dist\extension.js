!async function(){for(;!Spicetify.React||!Spicetify.ReactDOM;)await new Promise(e=>setTimeout(e,10));(()=>{var _,M=e=>"string"==typeof e,U=()=>{let r,a;var e=new Promise((e,t)=>{r=e,a=t});return e.resolve=r,e.reject=a,e},F=e=>null==e?"":""+e,T=(e,t,r)=>{e.forEach(e=>{t[e]&&(r[e]=t[e])})},V=/###/g,B=e=>e&&-1<e.indexOf("###")?e.replace(V,"."):e,D=e=>!e||M(e),K=(e,t,r)=>{var a=M(t)?t.split("."):t;let s=0;for(;s<a.length-1;){if(D(e))return{};var n=B(a[s]);!e[n]&&r&&(e[n]=new r),e=Object.prototype.hasOwnProperty.call(e,n)?e[n]:{},++s}return D(e)?{}:{obj:e,k:B(a[s])}},q=(a,s,n)=>{var{obj:e,k:t}=K(a,s,Object);if(void 0!==e||1===s.length)e[t]=n;else{let e=s[s.length-1],t=s.slice(0,s.length-1),r=K(a,t,Object);for(;void 0===r.obj&&t.length;)e=t[t.length-1]+"."+e,t=t.slice(0,t.length-1),(r=K(a,t,Object))?.obj&&void 0!==r.obj[r.k+"."+e]&&(r.obj=void 0);r.obj[r.k+"."+e]=n}},z=(e,t,r,a)=>{var{obj:e,k:t}=K(e,t,Object);e[t]=e[t]||[],e[t].push(r)},W=(e,t)=>{var{obj:e,k:t}=K(e,t);if(e&&Object.prototype.hasOwnProperty.call(e,t))return e[t]},H=(e,t,r)=>{e=W(e,r);return void 0!==e?e:W(t,r)},Y=(e,t,r)=>{for(const a in t)"__proto__"!==a&&"constructor"!==a&&(a in e?M(e[a])||e[a]instanceof String||M(t[a])||t[a]instanceof String?r&&(e[a]=t[a]):Y(e[a],t[a],r):e[a]=t[a]);return e},b=e=>e.replace(/[\-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g,"\\$&"),G={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;","/":"&#x2F;"},J=e=>M(e)?e.replace(/[&<>"'\/]/g,e=>G[e]):e,X=[" ",",","?","!",";"],Z=new class{constructor(e){this.capacity=e,this.regExpMap=new Map,this.regExpQueue=[]}getRegExp(e){var t=this.regExpMap.get(e);return void 0!==t||(t=new RegExp(e),this.regExpQueue.length===this.capacity&&this.regExpMap.delete(this.regExpQueue.shift()),this.regExpMap.set(e,t),this.regExpQueue.push(e)),t}}(20),Q=(e,t,r)=>{t=t||"",r=r||"";var a=X.filter(e=>t.indexOf(e)<0&&r.indexOf(e)<0);if(0===a.length)return!0;var s,a=Z.getRegExp(`(${a.map(e=>"?"===e?"\\?":e).join("|")})`);let n=!a.test(e);return n||0<(s=e.indexOf(r))&&!a.test(e.substring(0,s))&&(n=!0),n},ee=(e,t,n=".")=>{if(e){if(e[t])return Object.prototype.hasOwnProperty.call(e,t)?e[t]:void 0;var i=t.split(n);let s=e;for(let a=0;a<i.length;){if(!s||"object"!=typeof s)return;let t,r="";for(let e=a;e<i.length;++e)if(e!==a&&(r+=n),r+=i[e],void 0!==(t=s[r])&&!(-1<["string","number","boolean"].indexOf(typeof t)&&e<i.length-1)){a+=e-a+1;break}s=t}return s}},te=e=>e?.replace("_","-"),re={type:"logger",log(e){this.output("log",e)},warn(e){this.output("warn",e)},error(e){this.output("error",e)},output(e,t){console?.[e]?.apply?.(console,t)}},ae=class{constructor(e,t={}){this.init(e,t)}init(e,t={}){this.prefix=t.prefix||"i18next:",this.logger=e||re,this.options=t,this.debug=t.debug}log(...e){return this.forward(e,"log","",!0)}warn(...e){return this.forward(e,"warn","",!0)}error(...e){return this.forward(e,"error","")}deprecate(...e){return this.forward(e,"warn","WARNING DEPRECATED: ",!0)}forward(e,t,r,a){return a&&!this.debug?null:(M(e[0])&&(e[0]=""+r+this.prefix+" "+e[0]),this.logger[t](e))}create(e){return new ae(this.logger,{prefix:this.prefix+`:${e}:`,...this.options})}clone(e){return(e=e||this.options).prefix=e.prefix||this.prefix,new ae(this.logger,e)}},o=new ae,e=class{constructor(){this.observers={}}on(e,r){return e.split(" ").forEach(e=>{this.observers[e]||(this.observers[e]=new Map);var t=this.observers[e].get(r)||0;this.observers[e].set(r,t+1)}),this}off(e,t){this.observers[e]&&(t?this.observers[e].delete(t):delete this.observers[e])}emit(a,...s){this.observers[a]&&Array.from(this.observers[a].entries()).forEach(([t,r])=>{for(let e=0;e<r;e++)t(...s)}),this.observers["*"]&&Array.from(this.observers["*"].entries()).forEach(([t,r])=>{for(let e=0;e<r;e++)t.apply(t,[a,...s])})}},se=class extends e{constructor(e,t={ns:["translation"],defaultNS:"translation"}){super(),this.data=e||{},this.options=t,void 0===this.options.keySeparator&&(this.options.keySeparator="."),void 0===this.options.ignoreJSONStructure&&(this.options.ignoreJSONStructure=!0)}addNamespaces(e){this.options.ns.indexOf(e)<0&&this.options.ns.push(e)}removeNamespaces(e){e=this.options.ns.indexOf(e);-1<e&&this.options.ns.splice(e,1)}getResource(e,t,r,a={}){var s=(void 0!==a.keySeparator?a:this.options).keySeparator,a=(void 0!==a.ignoreJSONStructure?a:this.options).ignoreJSONStructure;let n;-1<e.indexOf(".")?n=e.split("."):(n=[e,t],r&&(Array.isArray(r)?n.push(...r):M(r)&&s?n.push(...r.split(s)):n.push(r)));var i=W(this.data,n);return!i&&!t&&!r&&-1<e.indexOf(".")&&(e=n[0],t=n[1],r=n.slice(2).join(".")),!i&&a&&M(r)?ee(this.data?.[e]?.[t],r,s):i}addResource(e,t,r,a,s={silent:!1}){var n=(void 0!==s.keySeparator?s:this.options).keySeparator;let i=[e,t];r&&(i=i.concat(n?r.split(n):r)),-1<e.indexOf(".")&&(a=t,t=(i=e.split("."))[1]),this.addNamespaces(t),q(this.data,i,a),s.silent||this.emit("added",e,t,r,a)}addResources(e,t,r,a={silent:!1}){for(const s in r)(M(r[s])||Array.isArray(r[s]))&&this.addResource(e,t,s,r[s],{silent:!0});a.silent||this.emit("added",e,t,r)}addResourceBundle(e,t,r,a,s,n={silent:!1,skipCopy:!1}){let i=[e,t],o=(-1<e.indexOf(".")&&(a=r,r=t,t=(i=e.split("."))[1]),this.addNamespaces(t),W(this.data,i)||{});n.skipCopy||(r=JSON.parse(JSON.stringify(r))),a?Y(o,r,s):o={...o,...r},q(this.data,i,o),n.silent||this.emit("added",e,t,r)}removeResourceBundle(e,t){this.hasResourceBundle(e,t)&&delete this.data[e][t],this.removeNamespaces(t),this.emit("removed",e,t)}hasResourceBundle(e,t){return void 0!==this.getResource(e,t)}getResourceBundle(e,t){return t=t||this.options.defaultNS,this.getResource(e,t)}getDataByLanguage(e){return this.data[e]}hasLanguageSomeTranslations(e){const t=this.getDataByLanguage(e);return!!(t&&Object.keys(t)||[]).find(e=>t[e]&&0<Object.keys(t[e]).length)}toJSON(){return this.data}},ne={processors:{},addPostProcessor(e){this.processors[e.name]=e},handle(e,t,r,a,s){return e.forEach(e=>{t=this.processors[e]?.process(t,r,a,s)??t}),t}},ie={},oe=e=>!M(e)&&"boolean"!=typeof e&&"number"!=typeof e,le=class extends e{constructor(e,t={}){super(),T(["resourceStore","languageUtils","pluralResolver","interpolator","backendConnector","i18nFormat","utils"],e,this),this.options=t,void 0===this.options.keySeparator&&(this.options.keySeparator="."),this.logger=o.create("translator")}changeLanguage(e){e&&(this.language=e)}exists(e,t={interpolation:{}}){t={...t};return null!=e&&void 0!==this.resolve(e,t)?.res}extractFromKey(e,t){let r=(void 0!==t.nsSeparator?t:this.options).nsSeparator;void 0===r&&(r=":");var a=(void 0!==t.keySeparator?t:this.options).keySeparator;let s=t.ns||this.options.defaultNS||[];var n=r&&-1<e.indexOf(r),t=!(this.options.userDefinedKeySeparator||t.keySeparator||this.options.userDefinedNsSeparator||t.nsSeparator||Q(e,r,a));if(n&&!t){n=e.match(this.interpolator.nestingRegexp);if(n&&0<n.length)return{key:e,namespaces:M(s)?[s]:s};t=e.split(r);(r!==a||r===a&&-1<this.options.ns.indexOf(t[0]))&&(s=t.shift()),e=t.join(a)}return{key:e,namespaces:M(s)?[s]:s}}translate(r,e,a){let s="object"==typeof e?{...e}:e;if("object"!=typeof s&&this.options.overloadTranslationOptionHandler&&(s=this.options.overloadTranslationOptionHandler(arguments)),s=(s="object"==typeof options?{...s}:s)||{},null==r)return"";Array.isArray(r)||(r=[String(r)]);var e=(void 0!==s.returnDetails?s:this.options).returnDetails,n=(void 0!==s.keySeparator?s:this.options).keySeparator;const{key:i,namespaces:t}=this.extractFromKey(r[r.length-1],s),o=t[t.length-1];let l=(void 0!==s.nsSeparator?s:this.options).nsSeparator;void 0===l&&(l=":");var c=s.lng||this.language,u=s.appendNamespaceToCIMode||this.options.appendNamespaceToCIMode;if("cimode"===c?.toLowerCase())return u?e?{res:""+o+l+i,usedKey:i,exactUsedKey:i,usedLng:c,usedNS:o,usedParams:this.getUsedParamsDetails(s)}:""+o+l+i:e?{res:i,usedKey:i,exactUsedKey:i,usedLng:c,usedNS:o,usedParams:this.getUsedParamsDetails(s)}:i;u=this.resolve(r,s);let h=u?.res;var f=u?.usedKey||i,d=u?.exactUsedKey||i,p=(void 0!==s.joinArrays?s:this.options).joinArrays,g=!this.i18nFormat||this.i18nFormat.handleAsObject,m=void 0!==s.count&&!M(s.count);const b=le.hasDefaultValue(s);var v=m?this.pluralResolver.getSuffix(c,s.count,s):"",y=s.ordinal&&m?this.pluralResolver.getSuffix(c,s.count,{ordinal:!1}):"";const w=m&&!s.ordinal&&0===s.count,k=w&&s[`defaultValue${this.options.pluralSeparator}zero`]||s["defaultValue"+v]||s["defaultValue"+y]||s.defaultValue;let x=h;g&&!h&&b&&(x=k);var v=oe(x),y=Object.prototype.toString.apply(x);if(!(g&&x&&v&&["[object Number]","[object Function]","[object RegExp]"].indexOf(y)<0)||M(p)&&Array.isArray(x))if(g&&M(p)&&Array.isArray(h))h=(h=h.join(p))&&this.extendTranslation(h,r,s,a);else{let e=!1,t=!1;!this.isValidLookup(h)&&b&&(e=!0,h=k),this.isValidLookup(h)||(t=!0,h=i);const N=(s.missingKeyNoValueFallbackToKey||this.options.missingKeyNoValueFallbackToKey)&&t?void 0:h,C=b&&k!==h&&this.options.updateMissing;if(t||e||C){this.logger.log(C?"updateKey":"missingKey",c,o,i,C?k:h),n&&(v=this.resolve(i,{...s,keySeparator:!1}))&&v.res&&this.logger.warn("Seems the loaded translations were in flat JSON format instead of nested. Either set keySeparator: false on init or make sure your translations are published in nested format.");let t=[];var S=this.languageUtils.getFallbackCodes(this.options.fallbackLng,s.lng||this.language);if("fallback"===this.options.saveMissingTo&&S&&S[0])for(let e=0;e<S.length;e++)t.push(S[e]);else"all"===this.options.saveMissingTo?t=this.languageUtils.toResolveHierarchy(s.lng||this.language):t.push(s.lng||this.language);const j=(e,t,r)=>{r=b&&r!==h?r:N;this.options.missingKeyHandler?this.options.missingKeyHandler(e,o,t,r,C,s):this.backendConnector?.saveMissing&&this.backendConnector.saveMissing(e,o,t,r,C,s),this.emit("missingKey",e,o,t,h)};this.options.saveMissing&&(this.options.saveMissingPlurals&&m?t.forEach(t=>{var e=this.pluralResolver.getSuffixes(t,s);w&&s[`defaultValue${this.options.pluralSeparator}zero`]&&e.indexOf(this.options.pluralSeparator+"zero")<0&&e.push(this.options.pluralSeparator+"zero"),e.forEach(e=>{j([t],i+e,s["defaultValue"+e]||k)})}):j(t,i,k))}h=this.extendTranslation(h,r,s,u,a),t&&h===i&&this.options.appendNamespaceToMissingKey&&(h=""+o+l+i),(t||e)&&this.options.parseMissingKeyHandler&&(h=this.options.parseMissingKeyHandler(this.options.appendNamespaceToMissingKey?""+o+l+i:i,e?h:void 0,s))}else{if(!s.returnObjects&&!this.options.returnObjects)return this.options.returnedObjectHandler||this.logger.warn("accessing an object - but returnObjects options is not enabled!"),y=this.options.returnedObjectHandler?this.options.returnedObjectHandler(f,x,{...s,ns:t}):`key '${i} (${this.language})' returned an object instead of string.`,e?(u.res=y,u.usedParams=this.getUsedParamsDetails(s),u):y;if(n){var L,g=Array.isArray(x),O=g?[]:{},R=g?d:f;for(const P in x)Object.prototype.hasOwnProperty.call(x,P)&&(L=""+R+n+P,b&&!h?O[P]=this.translate(L,{...s,defaultValue:oe(k)?k[P]:void 0,joinArrays:!1,ns:t}):O[P]=this.translate(L,{...s,joinArrays:!1,ns:t}),O[P]===L)&&(O[P]=x[P]);h=O}}return e?(u.res=h,u.usedParams=this.getUsedParamsDetails(s),u):h}extendTranslation(r,a,s,n,i){if(this.i18nFormat?.parse)r=this.i18nFormat.parse(r,{...this.options.interpolation.defaultVariables,...s},s.lng||this.language||n.usedLng,n.usedNS,n.usedKey,{resolved:n});else if(!s.skipInterpolation){s.interpolation&&this.interpolator.init({...s,interpolation:{...this.options.interpolation,...s.interpolation}});var o=M(r)&&(void 0!==s?.interpolation?.skipOnVariables?s:this.options).interpolation.skipOnVariables;let e,t=(o&&(l=r.match(this.interpolator.nestingRegexp),e=l&&l.length),s.replace&&!M(s.replace)?s.replace:s);this.options.interpolation.defaultVariables&&(t={...this.options.interpolation.defaultVariables,...t}),r=this.interpolator.interpolate(r,t,s.lng||this.language||n.usedLng,s),o&&(o=(l=r.match(this.interpolator.nestingRegexp))&&l.length,e<o)&&(s.nest=!1),!s.lng&&n&&n.res&&(s.lng=this.language||n.usedLng),!1!==s.nest&&(r=this.interpolator.nest(r,(...e)=>i?.[0]!==e[0]||s.context?this.translate(...e,a):(this.logger.warn(`It seems you are nesting recursively key: ${e[0]} in key: `+a[0]),null),s)),s.interpolation&&this.interpolator.reset()}var l=s.postProcess||this.options.postProcess,o=M(l)?[l]:l;return r=null!=r&&o?.length&&!1!==s.applyPostProcessor?ne.handle(o,r,a,this.options&&this.options.postProcessPassResolved?{i18nResolved:{...n,usedParams:this.getUsedParamsDetails(s)},...s}:s,this):r}resolve(e,h={}){let f,a,d,p,s;return(e=M(e)?[e]:e).forEach(t=>{if(!this.isValidLookup(f)){t=this.extractFromKey(t,h);const o=t.key;a=o;let e=t.namespaces;this.options.fallbackNS&&(e=e.concat(this.options.fallbackNS));const l=void 0!==h.count&&!M(h.count),c=l&&!h.ordinal&&0===h.count,u=void 0!==h.context&&(M(h.context)||"number"==typeof h.context)&&""!==h.context,r=h.lngs||this.languageUtils.toResolveHierarchy(h.lng||this.language,h.fallbackLng);e.forEach(i=>{this.isValidLookup(f)||(s=i,ie[r[0]+"-"+i]||!this.utils?.hasLoadedNamespace||this.utils?.hasLoadedNamespace(s)||(ie[r[0]+"-"+i]=!0,this.logger.warn(`key "${a}" for languages "${r.join(", ")}" won't get resolved as namespace "${s}" was not yet loaded`,"This means something IS WRONG in your setup. You access the t function before i18next.init / i18next.loadNamespace / i18next.changeLanguage was done. Wait for the callback or Promise to resolve before accessing it!!!")),r.forEach(t=>{if(!this.isValidLookup(f)){p=t;var e,r=[o];if(this.i18nFormat?.addLookupKeys)this.i18nFormat.addLookupKeys(r,o,t,i,h);else{let e;l&&(e=this.pluralResolver.getSuffix(t,h.count,h));var a,s=this.options.pluralSeparator+"zero",n=this.options.pluralSeparator+"ordinal"+this.options.pluralSeparator;l&&(r.push(o+e),h.ordinal&&0===e.indexOf(n)&&r.push(o+e.replace(n,this.options.pluralSeparator)),c)&&r.push(o+s),u&&(a=""+o+this.options.contextSeparator+h.context,r.push(a),l)&&(r.push(a+e),h.ordinal&&0===e.indexOf(n)&&r.push(a+e.replace(n,this.options.pluralSeparator)),c)&&r.push(a+s)}for(;e=r.pop();)this.isValidLookup(f)||(d=e,f=this.getResource(t,i,e,h))}}))})}}),{res:f,usedKey:a,exactUsedKey:d,usedLng:p,usedNS:s}}isValidLookup(e){return!(void 0===e||!this.options.returnNull&&null===e||!this.options.returnEmptyString&&""===e)}getResource(e,t,r,a={}){return(this.i18nFormat?.getResource?this.i18nFormat:this.resourceStore).getResource(e,t,r,a)}getUsedParamsDetails(e={}){var t=e.replace&&!M(e.replace);let r=t?e.replace:e;if(t&&void 0!==e.count&&(r.count=e.count),this.options.interpolation.defaultVariables&&(r={...this.options.interpolation.defaultVariables,...r}),!t){r={...r};for(const a of["defaultValue","ordinal","context","replace","lng","lngs","fallbackLng","ns","keySeparator","nsSeparator","returnObjects","returnDetails","joinArrays","postProcess","interpolation"])delete r[a]}return r}static hasDefaultValue(e){var t="defaultValue";for(const r in e)if(Object.prototype.hasOwnProperty.call(e,r)&&t===r.substring(0,t.length)&&void 0!==e[r])return!0;return!1}},ce=class{constructor(e){this.options=e,this.supportedLngs=this.options.supportedLngs||!1,this.logger=o.create("languageUtils")}getScriptPartFromCode(e){return!(e=te(e))||e.indexOf("-")<0||2===(e=e.split("-")).length||(e.pop(),"x"===e[e.length-1].toLowerCase())?null:this.formatLanguageCode(e.join("-"))}getLanguagePartFromCode(e){return!(e=te(e))||e.indexOf("-")<0?e:(e=e.split("-"),this.formatLanguageCode(e[0]))}formatLanguageCode(t){if(M(t)&&-1<t.indexOf("-")){let e;try{e=Intl.getCanonicalLocales(t)[0]}catch(e){}return(e=e&&this.options.lowerCaseLng?e.toLowerCase():e)?e:this.options.lowerCaseLng?t.toLowerCase():t}return this.options.cleanCode||this.options.lowerCaseLng?t.toLowerCase():t}isSupportedCode(e){return"languageOnly"!==this.options.load&&!this.options.nonExplicitSupportedLngs||(e=this.getLanguagePartFromCode(e)),!this.supportedLngs||!this.supportedLngs.length||-1<this.supportedLngs.indexOf(e)}getBestMatchFromCodes(e){if(!e)return null;let a;return e.forEach(e=>{a||(e=this.formatLanguageCode(e),this.options.supportedLngs&&!this.isSupportedCode(e))||(a=e)}),!a&&this.options.supportedLngs&&e.forEach(e=>{if(!a){var t=this.getScriptPartFromCode(e);if(this.isSupportedCode(t))return a=t;const r=this.getLanguagePartFromCode(e);if(this.isSupportedCode(r))return a=r;a=this.options.supportedLngs.find(e=>e===r||!(e.indexOf("-")<0&&r.indexOf("-")<0)&&(0<e.indexOf("-")&&r.indexOf("-")<0&&e.substring(0,e.indexOf("-"))===r||0===e.indexOf(r)&&1<r.length)?e:void 0)}}),a=a||this.getFallbackCodes(this.options.fallbackLng)[0]}getFallbackCodes(e,t){if(!e)return[];if("function"==typeof e&&(e=e(t)),M(e)&&(e=[e]),Array.isArray(e))return e;if(!t)return e.default||[];let r=e[t];return(r=(r=(r=(r=r||e[this.getScriptPartFromCode(t)])||e[this.formatLanguageCode(t)])||e[this.getLanguagePartFromCode(t)])||e.default)||[]}toResolveHierarchy(e,t){t=this.getFallbackCodes((!1===t?[]:t)||this.options.fallbackLng||[],e);const r=[],a=e=>{e&&(this.isSupportedCode(e)?r.push(e):this.logger.warn("rejecting language code not found in supportedLngs: "+e))};return M(e)&&(-1<e.indexOf("-")||-1<e.indexOf("_"))?("languageOnly"!==this.options.load&&a(this.formatLanguageCode(e)),"languageOnly"!==this.options.load&&"currentOnly"!==this.options.load&&a(this.getScriptPartFromCode(e)),"currentOnly"!==this.options.load&&a(this.getLanguagePartFromCode(e))):M(e)&&a(this.formatLanguageCode(e)),t.forEach(e=>{r.indexOf(e)<0&&a(this.formatLanguageCode(e))}),r}},ue={zero:0,one:1,two:2,few:3,many:4,other:5},he={select:e=>1===e?"one":"other",resolvedOptions:()=>({pluralCategories:["one","other"]})},fe=class{constructor(e,t={}){this.languageUtils=e,this.options=t,this.logger=o.create("pluralResolver"),this.pluralRulesCache={}}addRule(e,t){this.rules[e]=t}clearCache(){this.pluralRulesCache={}}getRule(t,r={}){var a=te("dev"===t?"en":t),e=r.ordinal?"ordinal":"cardinal",s=JSON.stringify({cleanedCode:a,type:e});if(s in this.pluralRulesCache)return this.pluralRulesCache[s];let n;try{n=new Intl.PluralRules(a,{type:e})}catch(e){if(!Intl)return this.logger.error("No Intl support, please use an Intl polyfill!"),he;if(!t.match(/-|_/))return he;a=this.languageUtils.getLanguagePartFromCode(t);n=this.getRule(a,r)}return this.pluralRulesCache[s]=n}needsPlural(e,t={}){let r=this.getRule(e,t);return 1<(r=r||this.getRule("dev",t))?.resolvedOptions().pluralCategories.length}getPluralFormsOfKey(e,t,r={}){return this.getSuffixes(e,r).map(e=>""+t+e)}getSuffixes(e,t={}){let r=this.getRule(e,t);return(r=r||this.getRule("dev",t))?r.resolvedOptions().pluralCategories.sort((e,t)=>ue[e]-ue[t]).map(e=>""+this.options.prepend+(t.ordinal?"ordinal"+this.options.prepend:"")+e):[]}getSuffix(e,t,r={}){var a=this.getRule(e,r);return a?""+this.options.prepend+(r.ordinal?"ordinal"+this.options.prepend:"")+a.select(t):(this.logger.warn("no plural rule found for: "+e),this.getSuffix("dev",t,r))}},de=(e,t,r,a=".",s=!0)=>{let n=H(e,t,r);return n=!n&&s&&M(r)&&void 0===(n=ee(e,r,a))?ee(t,r,a):n},pe=e=>e.replace(/\$/g,"$$$$"),ge=class{constructor(e={}){this.logger=o.create("interpolator"),this.options=e,this.format=e?.interpolation?.format||(e=>e),this.init(e)}init(e={}){e.interpolation||(e.interpolation={escapeValue:!0});var{escape:e,escapeValue:t,useRawValueToEscape:r,prefix:a,prefixEscaped:s,suffix:n,suffixEscaped:i,formatSeparator:o,unescapeSuffix:l,unescapePrefix:c,nestingPrefix:u,nestingPrefixEscaped:h,nestingSuffix:f,nestingSuffixEscaped:d,nestingOptionsSeparator:p,maxReplaces:g,alwaysFormat:m}=e.interpolation;this.escape=void 0!==e?e:J,this.escapeValue=void 0===t||t,this.useRawValueToEscape=void 0!==r&&r,this.prefix=a?b(a):s||"{{",this.suffix=n?b(n):i||"}}",this.formatSeparator=o||",",this.unescapePrefix=l?"":c||"-",this.unescapeSuffix=!this.unescapePrefix&&l||"",this.nestingPrefix=u?b(u):h||b("$t("),this.nestingSuffix=f?b(f):d||b(")"),this.nestingOptionsSeparator=p||",",this.maxReplaces=g||1e3,this.alwaysFormat=void 0!==m&&m,this.resetRegExp()}reset(){this.options&&this.init(this.options)}resetRegExp(){var e=(e,t)=>e?.source===t?(e.lastIndex=0,e):new RegExp(t,"g");this.regexp=e(this.regexp,this.prefix+"(.+?)"+this.suffix),this.regexpUnescape=e(this.regexpUnescape,""+this.prefix+this.unescapePrefix+"(.+?)"+this.unescapeSuffix+this.suffix),this.nestingRegexp=e(this.nestingRegexp,this.nestingPrefix+"(.+?)"+this.nestingSuffix)}interpolate(a,r,s,n){let i,o,l;const c=this.options&&this.options.interpolation&&this.options.interpolation.defaultVariables||{},u=e=>{var t;return e.indexOf(this.formatSeparator)<0?(t=de(r,c,e,this.options.keySeparator,this.options.ignoreJSONStructure),this.alwaysFormat?this.format(t,void 0,s,{...n,...r,interpolationkey:e}):t):(e=(t=e.split(this.formatSeparator)).shift().trim(),t=t.join(this.formatSeparator).trim(),this.format(de(r,c,e,this.options.keySeparator,this.options.ignoreJSONStructure),t,s,{...n,...r,interpolationkey:e}))},h=(this.resetRegExp(),n?.missingInterpolationHandler||this.options.missingInterpolationHandler),f=(void 0!==n?.interpolation?.skipOnVariables?n:this.options).interpolation.skipOnVariables;return[{regex:this.regexpUnescape,safeValue:e=>pe(e)},{regex:this.regexp,safeValue:e=>this.escapeValue?pe(this.escape(e)):pe(e)}].forEach(e=>{for(l=0;i=e.regex.exec(a);){var t=i[1].trim();if(void 0===(o=u(t)))if("function"==typeof h){var r=h(a,i,n);o=M(r)?r:""}else{if(!n||!Object.prototype.hasOwnProperty.call(n,t)){if(f){o=i[0];continue}this.logger.warn(`missed to pass in variable ${t} for interpolating `+a)}o=""}else M(o)||this.useRawValueToEscape||(o=F(o));r=e.safeValue(o);if(a=a.replace(i[0],r),f?(e.regex.lastIndex+=o.length,e.regex.lastIndex-=i[0].length):e.regex.lastIndex=0,++l>=this.maxReplaces)break}}),a}nest(r,a,s={}){let n,i,o;for(var l,c=(r,e)=>{var a=this.nestingOptionsSeparator;if(!(r.indexOf(a)<0)){var s=r.split(new RegExp(a+"[ ]*{"));let t="{"+s[1];r=s[0];var s=(t=this.interpolate(t,o)).match(/'/g),n=t.match(/"/g);((s?.length??0)%2!=0||n)&&n.length%2==0||(t=t.replace(/'/g,'"'));try{o=JSON.parse(t),e&&(o={...e,...o})}catch(e){return this.logger.warn("failed parsing options string in nesting for key "+r,e),""+r+a+t}o.defaultValue&&-1<o.defaultValue.indexOf(this.prefix)&&delete o.defaultValue}return r};n=this.nestingRegexp.exec(r);){let e=[],t=((o=(o={...s}).replace&&!M(o.replace)?o.replace:o).applyPostProcessor=!1,delete o.defaultValue,!1);if(-1===n[0].indexOf(this.formatSeparator)||/{.*}/.test(n[1])||(l=n[1].split(this.formatSeparator).map(e=>e.trim()),n[1]=l.shift(),e=l,t=!0),(i=a(c.call(this,n[1].trim(),o),o))&&n[0]===r&&!M(i))return i;(i=M(i)?i:F(i))||(this.logger.warn(`missed to resolve ${n[1]} for nesting `+r),i=""),t&&(i=e.reduce((e,t)=>this.format(e,t,s.lng,{...s,interpolationkey:n[1].trim()}),i.trim())),r=r.replace(n[0],i),this.regexp.lastIndex=0}return r}},me=e=>{let t=e.toLowerCase().trim();const r={};return-1<e.indexOf("(")&&(e=e.split("("),t=e[0].toLowerCase().trim(),e=e[1].substring(0,e[1].length-1),"currency"===t&&e.indexOf(":")<0?r.currency||(r.currency=e.trim()):"relativetime"===t&&e.indexOf(":")<0?r.range||(r.range=e.trim()):e.split(";").forEach(e=>{var t;e&&([e,...t]=e.split(":"),t=t.join(":").trim().replace(/^'+|'+$/g,""),e=e.trim(),r[e]||(r[e]=t),"false"===t&&(r[e]=!1),"true"===t&&(r[e]=!0),isNaN(t)||(r[e]=parseInt(t,10)))})),{formatName:t,formatOptions:r}},be=i=>{const o={};return(e,t,r)=>{let a=r;r&&r.interpolationkey&&r.formatParams&&r.formatParams[r.interpolationkey]&&r[r.interpolationkey]&&(a={...a,[r.interpolationkey]:void 0});var s=t+JSON.stringify(a);let n=o[s];return n||(n=i(te(t),r),o[s]=n),n(e)}},ve=a=>(e,t,r)=>a(te(t),r)(e),ye=class{constructor(e={}){this.logger=o.create("formatter"),this.options=e,this.init(e)}init(e,t={interpolation:{}}){this.formatSeparator=t.interpolation.formatSeparator||",";t=t.cacheInBuiltFormats?be:ve;this.formats={number:t((e,t)=>{const r=new Intl.NumberFormat(e,{...t});return e=>r.format(e)}),currency:t((e,t)=>{const r=new Intl.NumberFormat(e,{...t,style:"currency"});return e=>r.format(e)}),datetime:t((e,t)=>{const r=new Intl.DateTimeFormat(e,{...t});return e=>r.format(e)}),relativetime:t((e,t)=>{const r=new Intl.RelativeTimeFormat(e,{...t});return e=>r.format(e,t.range||"day")}),list:t((e,t)=>{const r=new Intl.ListFormat(e,{...t});return e=>r.format(e)})}}add(e,t){this.formats[e.toLowerCase().trim()]=t}addCached(e,t){this.formats[e.toLowerCase().trim()]=be(t)}format(e,t,i,o={}){var t=t.split(this.formatSeparator),r=(1<t.length&&1<t[0].indexOf("(")&&t[0].indexOf(")")<0&&t.find(e=>-1<e.indexOf(")"))&&(r=t.findIndex(e=>-1<e.indexOf(")")),t[0]=[t[0],...t.splice(1,r)].join(this.formatSeparator)),t.reduce((t,r)=>{var{formatName:r,formatOptions:a}=me(r);if(this.formats[r]){let e=t;try{var s=o?.formatParams?.[o.interpolationkey]||{},n=s.locale||s.lng||o.locale||o.lng||i;e=this.formats[r](t,n,{...a,...o,...s})}catch(e){this.logger.warn(e)}return e}return this.logger.warn("there was no format function for "+r),t},e));return r}},we=(e,t)=>{void 0!==e.pending[t]&&(delete e.pending[t],e.pendingCount--)},ke=class extends e{constructor(e,t,r,a={}){super(),this.backend=e,this.store=t,this.services=r,this.languageUtils=r.languageUtils,this.options=a,this.logger=o.create("backendConnector"),this.waitingReads=[],this.maxParallelReads=a.maxParallelReads||10,this.readingCalls=0,this.maxRetries=0<=a.maxRetries?a.maxRetries:5,this.retryTimeout=1<=a.retryTimeout?a.retryTimeout:350,this.state={},this.queue=[],this.backend?.init?.(r,a.backend,a)}queueLoad(e,t,s,r){const n={},i={},o={},l={};return e.forEach(r=>{let a=!0;t.forEach(e=>{var t=r+"|"+e;!s.reload&&this.store.hasResourceBundle(r,e)?this.state[t]=2:this.state[t]<0||(1===this.state[t]?void 0===i[t]&&(i[t]=!0):(this.state[t]=1,a=!1,void 0===i[t]&&(i[t]=!0),void 0===n[t]&&(n[t]=!0),void 0===l[e]&&(l[e]=!0)))}),a||(o[r]=!0)}),(Object.keys(n).length||Object.keys(i).length)&&this.queue.push({pending:i,pendingCount:Object.keys(i).length,loaded:{},errors:[],callback:r}),{toLoad:Object.keys(n),pending:Object.keys(i),toLoadLanguages:Object.keys(o),toLoadNamespaces:Object.keys(l)}}loaded(e,t,r){var a=e.split("|");const s=a[0],n=a[1],i=(t&&this.emit("failedLoading",s,n,t),!t&&r&&this.store.addResourceBundle(s,n,r,void 0,void 0,{skipCopy:!0}),this.state[e]=t?-1:2,t&&r&&(this.state[e]=0),{});this.queue.forEach(r=>{z(r.loaded,[s],n),we(r,e),t&&r.errors.push(t),0!==r.pendingCount||r.done||(Object.keys(r.loaded).forEach(t=>{i[t]||(i[t]={});var e=r.loaded[t];e.length&&e.forEach(e=>{void 0===i[t][e]&&(i[t][e]=!0)})}),r.done=!0,r.errors.length?r.callback(r.errors):r.callback())}),this.emit("loaded",i),this.queue=this.queue.filter(e=>!e.done)}read(a,s,n,i=0,o=this.retryTimeout,l){if(!a.length)return l(null,{});if(this.readingCalls>=this.maxParallelReads)this.waitingReads.push({lng:a,ns:s,fcName:n,tried:i,wait:o,callback:l});else{this.readingCalls++;const r=(e,t)=>{var r;this.readingCalls--,0<this.waitingReads.length&&(r=this.waitingReads.shift(),this.read(r.lng,r.ns,r.fcName,r.tried,r.wait,r.callback)),e&&t&&i<this.maxRetries?setTimeout(()=>{this.read.call(this,a,s,n,i+1,2*o,l)},o):l(e,t)};var e=this.backend[n].bind(this.backend);if(2!==e.length)return e(a,s,r);try{var t=e(a,s);t&&"function"==typeof t.then?t.then(e=>r(null,e)).catch(r):r(null,t)}catch(e){r(e)}}}prepareLoading(e,t,r={},a){if(!this.backend)return this.logger.warn("No backend was added via i18next.use. Will not load resources."),a&&a();M(e)&&(e=this.languageUtils.toResolveHierarchy(e)),M(t)&&(t=[t]);e=this.queueLoad(e,t,r,a);if(!e.toLoad.length)return e.pending.length||a(),null;e.toLoad.forEach(e=>{this.loadOne(e)})}load(e,t,r){this.prepareLoading(e,t,{},r)}reload(e,t,r){this.prepareLoading(e,t,{reload:!0},r)}loadOne(r,a=""){var e=r.split("|");const s=e[0],n=e[1];this.read(s,n,"read",void 0,void 0,(e,t)=>{e&&this.logger.warn(`${a}loading namespace ${n} for language ${s} failed`,e),!e&&t&&this.logger.log(`${a}loaded namespace ${n} for language `+s,t),this.loaded(r,e,t)})}saveMissing(t,r,a,s,n,i={},o=()=>{}){if(this.services?.utils?.hasLoadedNamespace&&!this.services?.utils?.hasLoadedNamespace(r))this.logger.warn(`did not save key "${a}" as the namespace "${r}" was not yet loaded`,"This means something IS WRONG in your setup. You access the t function before i18next.init / i18next.loadNamespace / i18next.changeLanguage was done. Wait for the callback or Promise to resolve before accessing it!!!");else if(null!=a&&""!==a){if(this.backend?.create){i={...i,isUpdate:n},n=this.backend.create.bind(this.backend);if(n.length<6)try{let e;(e=5===n.length?n(t,r,a,s,i):n(t,r,a,s))&&"function"==typeof e.then?e.then(e=>o(null,e)).catch(o):o(null,e)}catch(e){o(e)}else n(t,r,a,s,o,i)}t&&t[0]&&this.store.addResource(t[0],r,a,s)}}},xe=()=>({debug:!1,initAsync:!0,ns:["translation"],defaultNS:["translation"],fallbackLng:["dev"],fallbackNS:!1,supportedLngs:!1,nonExplicitSupportedLngs:!1,load:"all",preload:!1,simplifyPluralSuffix:!0,keySeparator:".",nsSeparator:":",pluralSeparator:"_",contextSeparator:"_",partialBundledLanguages:!1,saveMissing:!1,updateMissing:!1,saveMissingTo:"fallback",saveMissingPlurals:!0,missingKeyHandler:!1,missingInterpolationHandler:!1,postProcess:!1,postProcessPassResolved:!1,returnNull:!1,returnEmptyString:!0,returnObjects:!1,joinArrays:!1,returnedObjectHandler:!1,parseMissingKeyHandler:!1,appendNamespaceToMissingKey:!1,appendNamespaceToCIMode:!1,overloadTranslationOptionHandler:e=>{let t={};if("object"==typeof e[1]&&(t=e[1]),M(e[1])&&(t.defaultValue=e[1]),M(e[2])&&(t.tDescription=e[2]),"object"==typeof e[2]||"object"==typeof e[3]){const r=e[3]||e[2];Object.keys(r).forEach(e=>{t[e]=r[e]})}return t},interpolation:{escapeValue:!0,format:e=>e,prefix:"{{",suffix:"}}",formatSeparator:",",unescapePrefix:"-",nestingPrefix:"$t(",nestingSuffix:")",nestingOptionsSeparator:",",maxReplaces:1e3,skipOnVariables:!0},cacheInBuiltFormats:!0}),Se=e=>(M(e.ns)&&(e.ns=[e.ns]),M(e.fallbackLng)&&(e.fallbackLng=[e.fallbackLng]),M(e.fallbackNS)&&(e.fallbackNS=[e.fallbackNS]),e.supportedLngs?.indexOf?.("cimode")<0&&(e.supportedLngs=e.supportedLngs.concat(["cimode"])),"boolean"==typeof e.initImmediate&&(e.initAsync=e.initImmediate),e),Le=()=>{},Oe=t=>{Object.getOwnPropertyNames(Object.getPrototypeOf(t)).forEach(e=>{"function"==typeof t[e]&&(t[e]=t[e].bind(t))})},Re=class extends e{constructor(e={},t){if(super(),this.options=Se(e),this.services={},this.logger=o,this.modules={external:[]},Oe(this),t&&!this.isInitialized&&!e.isClone){if(!this.options.initAsync)return this.init(e,t),this;setTimeout(()=>{this.init(e,t)},0)}}init(t={},r){this.isInitializing=!0,"function"==typeof t&&(r=t,t={}),null==t.defaultNS&&t.ns&&(M(t.ns)?t.defaultNS=t.ns:t.ns.indexOf("translation")<0&&(t.defaultNS=t.ns[0]));var a=xe(),t=(this.options={...a,...this.options,...Se(t)},this.options.interpolation={...a.interpolation,...this.options.interpolation},void 0!==t.keySeparator&&(this.options.userDefinedKeySeparator=t.keySeparator),void 0!==t.nsSeparator&&(this.options.userDefinedNsSeparator=t.nsSeparator),e=>e?"function"==typeof e?new e:e:null);if(!this.options.isClone){this.modules.logger?o.init(t(this.modules.logger),this.options):o.init(null,this.options);let e;e=this.modules.formatter||ye;var s=new ce(this.options),n=(this.store=new se(this.options.resources,this.options),this.services);n.logger=o,n.resourceStore=this.store,n.languageUtils=s,n.pluralResolver=new fe(s,{prepend:this.options.pluralSeparator,simplifyPluralSuffix:this.options.simplifyPluralSuffix}),!e||this.options.interpolation.format&&this.options.interpolation.format!==a.interpolation.format||(n.formatter=t(e),n.formatter.init(n,this.options),this.options.interpolation.format=n.formatter.format.bind(n.formatter)),n.interpolator=new ge(this.options),n.utils={hasLoadedNamespace:this.hasLoadedNamespace.bind(this)},n.backendConnector=new ke(t(this.modules.backend),n.resourceStore,n,this.options),n.backendConnector.on("*",(e,...t)=>{this.emit(e,...t)}),this.modules.languageDetector&&(n.languageDetector=t(this.modules.languageDetector),n.languageDetector.init)&&n.languageDetector.init(n,this.options.detection,this.options),this.modules.i18nFormat&&(n.i18nFormat=t(this.modules.i18nFormat),n.i18nFormat.init)&&n.i18nFormat.init(this),this.translator=new le(this.services,this.options),this.translator.on("*",(e,...t)=>{this.emit(e,...t)}),this.modules.external.forEach(e=>{e.init&&e.init(this)})}this.format=this.options.interpolation.format,r=r||Le,!this.options.fallbackLng||this.services.languageDetector||this.options.lng||0<(s=this.services.languageUtils.getFallbackCodes(this.options.fallbackLng)).length&&"dev"!==s[0]&&(this.options.lng=s[0]),this.services.languageDetector||this.options.lng||this.logger.warn("init: no languageDetector is used and no lng is defined");["getResource","hasResourceBundle","getResourceBundle","getDataByLanguage"].forEach(t=>{this[t]=(...e)=>this.store[t](...e)});["addResource","addResources","addResourceBundle","removeResourceBundle"].forEach(t=>{this[t]=(...e)=>(this.store[t](...e),this)});const i=U();a=()=>{var e=(e,t)=>{this.isInitializing=!1,this.isInitialized&&!this.initializedStoreOnce&&this.logger.warn("init: i18next is already initialized. You should call init just once!"),this.isInitialized=!0,this.options.isClone||this.logger.log("initialized",this.options),this.emit("initialized",this.options),i.resolve(t),r(e,t)};if(this.languages&&!this.isInitialized)return e(null,this.t.bind(this));this.changeLanguage(this.options.lng,e)};return this.options.resources||!this.options.initAsync?a():setTimeout(a,0),i}loadResources(e,t=Le){let r=t;t=M(e)?e:this.language;if("function"==typeof e&&(r=e),!this.options.resources||this.options.partialBundledLanguages){if("cimode"===t?.toLowerCase()&&(!this.options.preload||0===this.options.preload.length))return r();const a=[],s=e=>{e&&"cimode"!==e&&this.services.languageUtils.toResolveHierarchy(e).forEach(e=>{"cimode"!==e&&a.indexOf(e)<0&&a.push(e)})};t?s(t):this.services.languageUtils.getFallbackCodes(this.options.fallbackLng).forEach(e=>s(e)),this.options.preload?.forEach?.(e=>s(e)),this.services.backendConnector.load(a,this.options.ns,e=>{e||this.resolvedLanguage||!this.language||this.setResolvedLanguage(this.language),r(e)})}else r(null)}reloadResources(e,t,r){const a=U();return"function"==typeof e&&(r=e,e=void 0),"function"==typeof t&&(r=t,t=void 0),e=e||this.languages,t=t||this.options.ns,r=r||Le,this.services.backendConnector.reload(e,t,e=>{a.resolve(),r(e)}),a}use(e){if(!e)throw new Error("You are passing an undefined module! Please check the object you are passing to i18next.use()");if(e.type)return"backend"===e.type&&(this.modules.backend=e),("logger"===e.type||e.log&&e.warn&&e.error)&&(this.modules.logger=e),"languageDetector"===e.type&&(this.modules.languageDetector=e),"i18nFormat"===e.type&&(this.modules.i18nFormat=e),"postProcessor"===e.type&&ne.addPostProcessor(e),"formatter"===e.type&&(this.modules.formatter=e),"3rdParty"===e.type&&this.modules.external.push(e),this;throw new Error("You are passing a wrong module! Please check the object you are passing to i18next.use()")}setResolvedLanguage(e){if(e&&this.languages&&!(-1<["cimode","dev"].indexOf(e))){for(let e=0;e<this.languages.length;e++){var t=this.languages[e];if(!(-1<["cimode","dev"].indexOf(t))&&this.store.hasLanguageSomeTranslations(t)){this.resolvedLanguage=t;break}}!this.resolvedLanguage&&this.languages.indexOf(e)<0&&this.store.hasLanguageSomeTranslations(e)&&(this.resolvedLanguage=e,this.languages.unshift(e))}}changeLanguage(a,r){this.isLanguageChangingTo=a;const s=U(),n=(this.emit("languageChanging",a),e=>{this.language=e,this.languages=this.services.languageUtils.toResolveHierarchy(e),this.resolvedLanguage=void 0,this.setResolvedLanguage(e)}),i=(e,t)=>{t?this.isLanguageChangingTo===a&&(n(t),this.translator.changeLanguage(t),this.isLanguageChangingTo=void 0,this.emit("languageChanged",t),this.logger.log("languageChanged",t)):this.isLanguageChangingTo=void 0,s.resolve((...e)=>this.t(...e)),r&&r(e,(...e)=>this.t(...e))};var e=e=>{a||e||!this.services.languageDetector||(e=[]);var t=M(e)?e:e&&e[0];const r=this.store.hasLanguageSomeTranslations(t)?t:this.services.languageUtils.getBestMatchFromCodes(M(e)?[e]:e);r&&(this.language||n(r),this.translator.language||this.translator.changeLanguage(r),this.services.languageDetector?.cacheUserLanguage?.(r)),this.loadResources(r,e=>{i(e,r)})};return a||!this.services.languageDetector||this.services.languageDetector.async?!a&&this.services.languageDetector&&this.services.languageDetector.async?0===this.services.languageDetector.detect.length?this.services.languageDetector.detect().then(e):this.services.languageDetector.detect(e):e(a):e(this.services.languageDetector.detect()),s}getFixedT(e,t,i){const o=(e,t,...r)=>{let a;(a="object"!=typeof t?this.options.overloadTranslationOptionHandler([e,t].concat(r)):{...t}).lng=a.lng||o.lng,a.lngs=a.lngs||o.lngs,a.ns=a.ns||o.ns,""!==a.keyPrefix&&(a.keyPrefix=a.keyPrefix||i||o.keyPrefix);const s=this.options.keySeparator||".";let n;return n=a.keyPrefix&&Array.isArray(e)?e.map(e=>""+a.keyPrefix+s+e):a.keyPrefix?""+a.keyPrefix+s+e:e,this.t(n,a)};return M(e)?o.lng=e:o.lngs=e,o.ns=t,o.keyPrefix=i,o}t(...e){return this.translator?.translate(...e)}exists(...e){return this.translator?.exists(...e)}setDefaultNamespace(e){this.options.defaultNS=e}hasLoadedNamespace(e,t={}){if(!this.isInitialized)return this.logger.warn("hasLoadedNamespace: i18next was not initialized",this.languages),!1;if(!this.languages||!this.languages.length)return this.logger.warn("hasLoadedNamespace: i18n.languages were undefined or empty",this.languages),!1;var r=t.lng||this.resolvedLanguage||this.languages[0],a=!!this.options&&this.options.fallbackLng,s=this.languages[this.languages.length-1];if("cimode"===r.toLowerCase())return!0;var n=(e,t)=>{e=this.services.backendConnector.state[e+"|"+t];return-1===e||0===e||2===e};if(t.precheck){t=t.precheck(this,n);if(void 0!==t)return t}return!!this.hasResourceBundle(r,e)||!(this.services.backendConnector.backend&&(!this.options.resources||this.options.partialBundledLanguages)&&(!n(r,e)||a&&!n(s,e)))}loadNamespaces(e,t){const r=U();return this.options.ns?((e=M(e)?[e]:e).forEach(e=>{this.options.ns.indexOf(e)<0&&this.options.ns.push(e)}),this.loadResources(e=>{r.resolve(),t&&t(e)}),r):(t&&t(),Promise.resolve())}loadLanguages(e,t){const r=U(),a=(M(e)&&(e=[e]),this.options.preload||[]);e=e.filter(e=>a.indexOf(e)<0&&this.services.languageUtils.isSupportedCode(e));return e.length?(this.options.preload=a.concat(e),this.loadResources(e=>{r.resolve(),t&&t(e)}),r):(t&&t(),Promise.resolve())}dir(e){var t;return!(e=e||this.resolvedLanguage||(0<this.languages?.length?this.languages[0]:this.language))||(t=this.services?.languageUtils||new ce(xe()),-1<["ar","shu","sqr","ssh","xaa","yhd","yud","aao","abh","abv","acm","acq","acw","acx","acy","adf","ads","aeb","aec","afb","ajp","apc","apd","arb","arq","ars","ary","arz","auz","avl","ayh","ayl","ayn","ayp","bbz","pga","he","iw","ps","pbt","pbu","pst","prp","prd","ug","ur","ydd","yds","yih","ji","yi","hbo","men","xmn","fa","jpr","peo","pes","prs","dv","sam","ckb"].indexOf(t.getLanguagePartFromCode(e)))||1<e.toLowerCase().indexOf("-arab")?"rtl":"ltr"}static createInstance(e={},t){return new Re(e,t)}cloneInstance(e={},t=Le){var r=e.forkResourceStore,a=(r&&delete e.forkResourceStore,{...this.options,...e,isClone:!0});const s=new Re(a);void 0===e.debug&&void 0===e.prefix||(s.logger=s.logger.clone(e));return["store","services","language"].forEach(e=>{s[e]=this[e]}),s.services={...this.services},s.services.utils={hasLoadedNamespace:s.hasLoadedNamespace.bind(s)},r&&(e=Object.keys(this.store.data).reduce((r,a)=>(r[a]={...this.store.data[a]},r[a]=Object.keys(r[a]).reduce((e,t)=>(e[t]={...r[a][t]},e),r[a]),r),{}),s.store=new se(e,a),s.services.resourceStore=s.store),s.translator=new le(s.services,a),s.translator.on("*",(e,...t)=>{s.emit(e,...t)}),s.init(a,t),s.translator.options=a,s.translator.backendConnector.services.utils={hasLoadedNamespace:s.hasLoadedNamespace.bind(s)},s}toJSON(){return{options:this.options,store:this.store,language:this.language,languages:this.languages,resolvedLanguage:this.resolvedLanguage}}},e=Re.createInstance(),Ne=(e.createInstance=Re.createInstance,e.createInstance,e.dir,e.init,e.loadResources,e.reloadResources,e.use,e.changeLanguage,e.getFixedT,e.t),e=(e.exists,e.setDefaultNamespace,e.hasLoadedNamespace,e.loadNamespaces,e.loadLanguages,"marketplace"),g={installedExtensions:e+":installed-extensions",installedSnippets:e+":installed-snippets",installedThemes:e+":installed-themes",activeTab:e+":active-tab",tabs:e+":tabs",sort:e+":sort",themeInstalled:e+":theme-installed",localTheme:e+":local-theme",albumArtBasedColor:e+":albumArtBasedColors",albumArtBasedColorMode:e+":albumArtBasedColorsMode",albumArtBasedColorVibrancy:e+":albumArtBasedColorsVibrancy",colorShift:e+":colorShift"},Ce=100,{min:je,max:Pe}=Math,L=(e,t=0,r=1)=>je(Pe(t,e),r),Me=t=>{t._clipped=!1,t._unclipped=t.slice(0);for(let e=0;e<=3;e++)e<3?((t[e]<0||255<t[e])&&(t._clipped=!0),t[e]=L(t[e],0,255)):3===e&&(t[e]=L(t[e],0,1));return t},Ee={};for(_ of["Boolean","Number","String","Function","Array","Date","RegExp","Undefined","Null"])Ee[`[object ${_}]`]=_.toLowerCase();function O(e){return Ee[Object.prototype.toString.call(e)]||"object"}var p=(t,e=null)=>3<=t.length?Array.prototype.slice.call(t):"object"==O(t[0])&&e?e.split("").filter(e=>void 0!==t[0][e]).map(e=>t[0][e]):t[0].slice(0),l=e=>{var t;return!(e.length<2)&&"string"==O(e[t=e.length-1])?e[t].toLowerCase():null},{PI:e,min:$e,max:Ae}=Math,a=e=>Math.round(100*e)/100,Ie=e=>Math.round(100*e)/100,h=2*e,_e=e/3,Ue=e/180,Fe=180/e,i={format:{},autodetect:[]},m=class{constructor(...e){if("object"===O(e[0])&&e[0].constructor&&e[0].constructor===this.constructor)return e[0];let t=l(e),r=!1;if(!t){r=!0,i.sorted||(i.autodetect=i.autodetect.sort((e,t)=>t.p-e.p),i.sorted=!0);for(var a of i.autodetect)if(t=a.test(...e))break}if(!i.format[t])throw new Error("unknown format: "+e);var s=i.format[t].apply(null,r?e:e.slice(0,-1));this._rgb=Me(s),3===this._rgb.length&&this._rgb.push(1)}toString(){return"function"==O(this.hex)?this.hex():`[${this._rgb.join(",")}]`}},e=(...e)=>new m(...e),R=(e.version="3.1.1",e),r={aliceblue:"#f0f8ff",antiquewhite:"#faebd7",aqua:"#00ffff",aquamarine:"#7fffd4",azure:"#f0ffff",beige:"#f5f5dc",bisque:"#ffe4c4",black:"#000000",blanchedalmond:"#ffebcd",blue:"#0000ff",blueviolet:"#8a2be2",brown:"#a52a2a",burlywood:"#deb887",cadetblue:"#5f9ea0",chartreuse:"#7fff00",chocolate:"#d2691e",coral:"#ff7f50",cornflowerblue:"#6495ed",cornsilk:"#fff8dc",crimson:"#dc143c",cyan:"#00ffff",darkblue:"#00008b",darkcyan:"#008b8b",darkgoldenrod:"#b8860b",darkgray:"#a9a9a9",darkgreen:"#006400",darkgrey:"#a9a9a9",darkkhaki:"#bdb76b",darkmagenta:"#8b008b",darkolivegreen:"#556b2f",darkorange:"#ff8c00",darkorchid:"#9932cc",darkred:"#8b0000",darksalmon:"#e9967a",darkseagreen:"#8fbc8f",darkslateblue:"#483d8b",darkslategray:"#2f4f4f",darkslategrey:"#2f4f4f",darkturquoise:"#00ced1",darkviolet:"#9400d3",deeppink:"#ff1493",deepskyblue:"#00bfff",dimgray:"#696969",dimgrey:"#696969",dodgerblue:"#1e90ff",firebrick:"#b22222",floralwhite:"#fffaf0",forestgreen:"#228b22",fuchsia:"#ff00ff",gainsboro:"#dcdcdc",ghostwhite:"#f8f8ff",gold:"#ffd700",goldenrod:"#daa520",gray:"#808080",green:"#008000",greenyellow:"#adff2f",grey:"#808080",honeydew:"#f0fff0",hotpink:"#ff69b4",indianred:"#cd5c5c",indigo:"#4b0082",ivory:"#fffff0",khaki:"#f0e68c",laserlemon:"#ffff54",lavender:"#e6e6fa",lavenderblush:"#fff0f5",lawngreen:"#7cfc00",lemonchiffon:"#fffacd",lightblue:"#add8e6",lightcoral:"#f08080",lightcyan:"#e0ffff",lightgoldenrod:"#fafad2",lightgoldenrodyellow:"#fafad2",lightgray:"#d3d3d3",lightgreen:"#90ee90",lightgrey:"#d3d3d3",lightpink:"#ffb6c1",lightsalmon:"#ffa07a",lightseagreen:"#20b2aa",lightskyblue:"#87cefa",lightslategray:"#778899",lightslategrey:"#778899",lightsteelblue:"#b0c4de",lightyellow:"#ffffe0",lime:"#00ff00",limegreen:"#32cd32",linen:"#faf0e6",magenta:"#ff00ff",maroon:"#800000",maroon2:"#7f0000",maroon3:"#b03060",mediumaquamarine:"#66cdaa",mediumblue:"#0000cd",mediumorchid:"#ba55d3",mediumpurple:"#9370db",mediumseagreen:"#3cb371",mediumslateblue:"#7b68ee",mediumspringgreen:"#00fa9a",mediumturquoise:"#48d1cc",mediumvioletred:"#c71585",midnightblue:"#191970",mintcream:"#f5fffa",mistyrose:"#ffe4e1",moccasin:"#ffe4b5",navajowhite:"#ffdead",navy:"#000080",oldlace:"#fdf5e6",olive:"#808000",olivedrab:"#6b8e23",orange:"#ffa500",orangered:"#ff4500",orchid:"#da70d6",palegoldenrod:"#eee8aa",palegreen:"#98fb98",paleturquoise:"#afeeee",palevioletred:"#db7093",papayawhip:"#ffefd5",peachpuff:"#ffdab9",peru:"#cd853f",pink:"#ffc0cb",plum:"#dda0dd",powderblue:"#b0e0e6",purple:"#800080",purple2:"#7f007f",purple3:"#a020f0",rebeccapurple:"#663399",red:"#ff0000",rosybrown:"#bc8f8f",royalblue:"#4169e1",saddlebrown:"#8b4513",salmon:"#fa8072",sandybrown:"#f4a460",seagreen:"#2e8b57",seashell:"#fff5ee",sienna:"#a0522d",silver:"#c0c0c0",skyblue:"#87ceeb",slateblue:"#6a5acd",slategray:"#708090",slategrey:"#708090",snow:"#fffafa",springgreen:"#00ff7f",steelblue:"#4682b4",tan:"#d2b48c",teal:"#008080",thistle:"#d8bfd8",tomato:"#ff6347",turquoise:"#40e0d0",violet:"#ee82ee",wheat:"#f5deb3",white:"#ffffff",whitesmoke:"#f5f5f5",yellow:"#ffff00",yellowgreen:"#9acd32"},Te=/^#?([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/,Ve=/^#?([A-Fa-f0-9]{8}|[A-Fa-f0-9]{4})$/,Be=e=>{var t;if(e.match(Te))return 3===(e=4!==e.length&&7!==e.length?e:e.substr(1)).length&&(e=(e=e.split(""))[0]+e[0]+e[1]+e[1]+e[2]+e[2]),[(t=parseInt(e,16))>>16,t>>8&255,255&t,1];if(e.match(Ve))return 4===(e=5!==e.length&&9!==e.length?e:e.substr(1)).length&&(e=(e=e.split(""))[0]+e[0]+e[1]+e[1]+e[2]+e[2]+e[3]+e[3]),[(t=parseInt(e,16))>>24&255,t>>16&255,t>>8&255,Math.round((255&t)/255*100)/100];throw new Error("unknown hex color: "+e)},De=Math["round"],Ke=(...e)=>{let[t,r,a,s]=p(e,"rgba"),n=l(e)||"auto";void 0===s&&(s=1),"auto"===n&&(n=s<1?"rgba":"rgb");let i="000000"+(De(t)<<16|De(r)<<8|De(a)).toString(16),o=(i=i.substr(i.length-6),"0"+De(255*s).toString(16));switch(o=o.substr(o.length-2),n.toLowerCase()){case"rgba":return"#"+i+o;case"argb":return"#"+o+i;default:return"#"+i}},qe=(m.prototype.name=function(){var e,t=Ke(this._rgb,"rgb");for(e of Object.keys(r))if(r[e]===t)return e.toLowerCase();return t},i.format.named=e=>{if(e=e.toLowerCase(),r[e])return Be(r[e]);throw new Error("unknown color name: "+e)},i.autodetect.push({p:5,test:(e,...t)=>{if(!t.length&&"string"===O(e)&&r[e.toLowerCase()])return"named"}}),m.prototype.alpha=function(e,t=!1){return void 0!==e&&"number"===O(e)?t?(this._rgb[3]=e,this):new m([this._rgb[0],this._rgb[1],this._rgb[2],e],"rgb"):this._rgb[3]},m.prototype.clipped=function(){return this._rgb._clipped||!1},{Kn:18,labWhitePoint:"d65",Xn:.95047,Yn:1,Zn:1.08883,t0:.137931034,t1:.206896552,t2:.12841855,t3:.008856452,kE:216/24389,kKE:8,kK:24389/27,RefWhiteRGB:{X:.95047,Y:1,Z:1.08883},MtxRGB2XYZ:{m00:.4124564390896922,m01:.21267285140562253,m02:.0193338955823293,m10:.357576077643909,m11:.715152155287818,m12:.11919202588130297,m20:.18043748326639894,m21:.07217499330655958,m22:.9503040785363679},MtxXYZ2RGB:{m00:3.2404541621141045,m01:-.9692660305051868,m02:.055643430959114726,m10:-1.5371385127977166,m11:1.8760108454466942,m12:-.2040259135167538,m20:-.498531409556016,m21:.041556017530349834,m22:1.0572251882231791},As:.9414285350000001,Bs:1.040417467,Cs:1.089532651,MtxAdaptMa:{m00:.8951,m01:-.7502,m02:.0389,m10:.2664,m11:1.7135,m12:-.0685,m20:-.1614,m21:.0367,m22:1.0296},MtxAdaptMaI:{m00:.9869929054667123,m01:.43230526972339456,m02:-.008528664575177328,m10:-.14705425642099013,m11:.5183602715367776,m12:.04004282165408487,m20:.15996265166373125,m21:.0492912282128556,m22:.9684866957875502}}),w=qe,ze=new Map([["a",[1.0985,.35585]],["b",[1.0985,.35585]],["c",[.98074,1.18232]],["d50",[.96422,.82521]],["d55",[.95682,.92149]],["d65",[.95047,1.08883]],["e",[1,1,1]],["f2",[.99186,.67393]],["f7",[.95041,1.08747]],["f11",[1.00962,.6435]],["icc",[.96422,.82521]]]);function c(e){var t=ze.get(String(e).toLowerCase());if(!t)throw new Error("unknown Lab illuminant "+e);qe.labWhitePoint=e,qe.Xn=t[0],qe.Zn=t[1]}function We(){return qe.labWhitePoint}var He=e=>{var t=Math.sign(e);return((e=Math.abs(e))<=.0031308?12.92*e:1.055*Math.pow(e,1/2.4)-.055)*t},Ye=(e,t,r)=>{var{MtxAdaptMa:a,MtxAdaptMaI:s,MtxXYZ2RGB:n,RefWhiteRGB:i,Xn:o,Yn:l,Zn:c}=w,u=o*a.m00+l*a.m10+c*a.m20,h=o*a.m01+l*a.m11+c*a.m21,o=o*a.m02+l*a.m12+c*a.m22,l=i.X*a.m00+i.Y*a.m10+i.Z*a.m20,c=i.X*a.m01+i.Y*a.m11+i.Z*a.m21,i=i.X*a.m02+i.Y*a.m12+i.Z*a.m22,l=(e*a.m00+t*a.m10+r*a.m20)*(l/u),u=(e*a.m01+t*a.m11+r*a.m21)*(c/h),c=(e*a.m02+t*a.m12+r*a.m22)*(i/o),h=l*s.m00+u*s.m10+c*s.m20,e=l*s.m01+u*s.m11+c*s.m21,t=l*s.m02+u*s.m12+c*s.m22;return[255*He(h*n.m00+e*n.m10+t*n.m20),255*He(h*n.m01+e*n.m11+t*n.m21),255*He(h*n.m02+e*n.m12+t*n.m22)]},Ge=(...e)=>{var[t,r,a]=e=p(e,"lab"),[t,r,a]=((e,t,r)=>{const{kE:a,kK:s,kKE:n,Xn:i,Yn:o,Zn:l}=w,c=(e+16)/116,u=.002*t+c,h=c-.005*r,f=u*u*u,d=h*h*h,p=f>a?f:(116*u-16)/s,g=e>n?Math.pow((e+16)/116,3):e/s,m=d>a?d:(116*h-16)/s,b=p*i,v=g*o,y=m*l;return[b,v,y]})(t,r,a),[t,r,a]=Ye(t,r,a);return[t,r,a,3<e.length?e[3]:1]};function Je(e){var t=Math.sign(e);return((e=Math.abs(e))<=.04045?e/12.92:Math.pow((e+.055)/1.055,2.4))*t}var Xe=(e,t,r)=>{e=Je(e/255),t=Je(t/255),r=Je(r/255);var{MtxRGB2XYZ:a,MtxAdaptMa:s,MtxAdaptMaI:n,Xn:i,Yn:o,Zn:l,As:c,Bs:u,Cs:h}=w,f=e*a.m00+t*a.m10+r*a.m20,d=e*a.m01+t*a.m11+r*a.m21,e=e*a.m02+t*a.m12+r*a.m22,t=i*s.m00+o*s.m10+l*s.m20,r=i*s.m01+o*s.m11+l*s.m21,a=i*s.m02+o*s.m12+l*s.m22,i=f*s.m00+d*s.m10+e*s.m20,o=f*s.m01+d*s.m11+e*s.m21,l=f*s.m02+d*s.m12+e*s.m22;return[(i*=t/c)*n.m00+(o*=r/u)*n.m10+(l*=a/h)*n.m20,i*n.m01+o*n.m11+l*n.m21,i*n.m02+o*n.m12+l*n.m22]},Ze=(...e)=>{var[e,t,r,...a]=p(e,"rgb"),[e,t,r]=Xe(e,t,r),[e,t,r]=function(e,t,r){var{Xn:a,Yn:s,Zn:n,kE:i,kK:o}=w,e=e/a,a=t/s,t=r/n,s=i<e?Math.pow(e,1/3):(o*e+16)/116,r=i<a?Math.pow(a,1/3):(o*a+16)/116,n=i<t?Math.pow(t,1/3):(o*t+16)/116;return[116*r-16,500*(s-r),200*(r-n)]}(e,t,r);return[e,t,r,...0<a.length&&a[0]<1?[a[0]]:[]]},Qe=(m.prototype.lab=function(){return Ze(this._rgb)},(Object.assign(R,{lab:(...e)=>new m(...e,"lab"),getLabWhitePoint:We,setLabWhitePoint:c}),i.format.lab=Ge,i.autodetect.push({p:2,test:(...e)=>{if("array"===O(e=p(e,"lab"))&&3===e.length)return"lab"}}),m.prototype.darken=function(e=1){var t=this.lab();return t[0]-=w.Kn*e,new m(t,"lab").alpha(this.alpha(),!0)},m.prototype.brighten=function(e=1){return this.darken(-e)},m.prototype.darker=m.prototype.darken,m.prototype.brighter=m.prototype.brighten,m.prototype.get=function(e){var[e,t]=e.split("."),r=this[e]();if(t){var a=e.indexOf(t)-("ok"===e.substr(0,2)?2:0);if(-1<a)return r[a];throw new Error(`unknown channel ${t} in mode `+e)}return r},Math)["pow"]),et=(m.prototype.luminance=function(n,i="rgb"){if(void 0===n||"number"!==O(n))return et(...this._rgb.slice(0,3));{if(0===n)return new m([0,0,0,this._rgb[3]],"rgb");if(1===n)return new m([255,255,255,this._rgb[3]],"rgb");var e=this.luminance();let s=20;const o=(e,t)=>{var r=e.interpolate(t,.5,i),a=r.luminance();return Math.abs(n-a)<1e-7||!s--?r:n<a?o(e,r):o(r,t)};e=(n<e?o(new m([0,0,0]),this):o(this,new m([255,255,255]))).rgb();return new m([...e,this._rgb[3]])}},(e,t,r)=>.2126*(e=tt(e))+.7152*(t=tt(t))+.0722*(r=tt(r))),tt=e=>(e/=255)<=.03928?e/12.92:Qe((e+.055)/1.055,2.4),n={},s=(e,t,r=.5,...a)=>{let s=a[0]||"lrgb";if(n[s]||a.length||(s=Object.keys(n)[0]),n[s])return"object"!==O(e)&&(e=new m(e)),"object"!==O(t)&&(t=new m(t)),n[s](e,t,r).alpha(e.alpha()+r*(t.alpha()-e.alpha()));throw new Error(`interpolation mode ${s} is not defined`)},{sin:rt,cos:at}=(m.prototype.mix=m.prototype.interpolate=function(e,t=.5,...r){return s(this,e,t,...r)},m.prototype.premultiply=function(e=!1){var t=this._rgb,r=t[3];return e?(this._rgb=[t[0]*r,t[1]*r,t[2]*r,r],this):new m([t[0]*r,t[1]*r,t[2]*r,r],"rgb")},Math),st=(...e)=>{let[t,r,a]=p(e,"lch");return isNaN(a)&&(a=0),a*=Ue,[t,at(a)*r,rt(a)*r]},nt=(...e)=>{var[t,r,a]=e=p(e,"lch"),[t,r,a]=st(t,r,a),[t,r,a]=Ge(t,r,a);return[t,r,a,3<e.length?e[3]:1]},e=(...e)=>{e=p(e,"hcl").reverse();return nt(...e)},{sqrt:it,atan2:ot,round:lt}=Math,ct=(...e)=>{var[e,t,r]=p(e,"lab"),a=it(t*t+r*r);let s=(ot(r,t)*Fe+360)%360;return[e,a,s=0===lt(1e4*a)?Number.NaN:s]},ut=(...e)=>{var[e,t,r,...a]=p(e,"rgb"),[e,t,r]=Ze(e,t,r),[e,t,r]=ct(e,t,r);return[e,t,r,...0<a.length&&a[0]<1?[a[0]]:[]]},{sqrt:ht,pow:u}=(m.prototype.lch=function(){return ut(this._rgb)},m.prototype.hcl=function(){return ut(this._rgb).reverse()},Object.assign(R,{lch:(...e)=>new m(...e,"lch"),hcl:(...e)=>new m(...e,"hcl")}),i.format.lch=nt,i.format.hcl=e,["lch","hcl"].forEach(t=>i.autodetect.push({p:2,test:(...e)=>{if("array"===O(e=p(e,t))&&3===e.length)return t}})),m.prototype.saturate=function(e=1){var t=this.lch();return t[1]+=w.Kn*e,t[1]<0&&(t[1]=0),new m(t,"lch").alpha(this.alpha(),!0)},m.prototype.desaturate=function(e=1){return this.saturate(-e)},m.prototype.set=function(e,t,r=!1){var[e,a]=e.split("."),s=this[e]();if(a){var n=e.indexOf(a)-("ok"===e.substr(0,2)?2:0);if(-1<n){if("string"==O(t))switch(t.charAt(0)){case"+":case"-":s[n]+=+t;break;case"*":s[n]*=+t.substr(1);break;case"/":s[n]/=+t.substr(1);break;default:s[n]=+t}else{if("number"!==O(t))throw new Error("unsupported value for Color.set");s[n]=t}var i=new m(s,e);return r?(this._rgb=i._rgb,this):i}throw new Error(`unknown channel ${a} in mode `+e)}return s},m.prototype.tint=function(e=.5,...t){return s(this,"white",e,...t)},m.prototype.shade=function(e=.5,...t){return s(this,"black",e,...t)},n.rgb=(e,t,r)=>{e=e._rgb,t=t._rgb;return new m(e[0]+r*(t[0]-e[0]),e[1]+r*(t[1]-e[1]),e[2]+r*(t[2]-e[2]),"rgb")},Math),f=(n.lrgb=(e,t,r)=>{var[e,a,s]=e._rgb,[t,n,i]=t._rgb;return new m(ht(u(e,2)*(1-r)+u(t,2)*r),ht(u(a,2)*(1-r)+u(n,2)*r),ht(u(s,2)*(1-r)+u(i,2)*r),"rgb")},n.lab=(e,t,r)=>{e=e.lab(),t=t.lab();return new m(e[0]+r*(t[0]-e[0]),e[1]+r*(t[1]-e[1]),e[2]+r*(t[2]-e[2]),"lab")},(e,t,r,a)=>{let s,n;"hsl"===a?(s=e.hsl(),n=t.hsl()):"hsv"===a?(s=e.hsv(),n=t.hsv()):"hcg"===a?(s=e.hcg(),n=t.hcg()):"hsi"===a?(s=e.hsi(),n=t.hsi()):"lch"===a||"hcl"===a?(a="hcl",s=e.hcl(),n=t.hcl()):"oklch"===a&&(s=e.oklch().reverse(),n=t.oklch().reverse());let i,o,l,c,u,h;"h"!==a.substr(0,1)&&"oklch"!==a||([i,l,u]=s,[o,c,h]=n);let f,d,p,g;return isNaN(i)||isNaN(o)?isNaN(i)?isNaN(o)?d=Number.NaN:(d=o,1!=u&&0!=u||"hsv"==a||(f=c)):(d=i,1!=h&&0!=h||"hsv"==a||(f=l)):(g=o>i&&180<o-i?o-(i+360):o<i&&180<i-o?o+360-i:o-i,d=i+r*g),void 0===f&&(f=l+r*(c-l)),p=u+r*(h-u),new m("oklch"===a?[p,f,d]:[d,f,p],a)}),e=(e,t,r)=>f(e,t,r,"lch"),e=(n.lch=e,n.hcl=e,e=>{if("number"==O(e)&&0<=e&&e<=16777215)return[e>>16,e>>8&255,255&e,1];throw new Error("unknown num color: "+e)}),ft=(...e)=>{var[e,t,r]=p(e,"rgb");return(e<<16)+(t<<8)+r},dt=(m.prototype.num=function(){return ft(this._rgb)},Object.assign(R,{num:(...e)=>new m(...e,"num")}),i.format.num=e,i.autodetect.push({p:5,test:(...e)=>{if(1===e.length&&"number"===O(e[0])&&0<=e[0]&&e[0]<=16777215)return"num"}}),(n.num=(e,t,r)=>{e=e.num(),t=t.num();return new m(e+r*(t-e),"num")},Math)["floor"]),e=(...e)=>{let[t,r,a]=e=p(e,"hcg"),s,n,i;a*=255;var o=255*r;if(0===r)s=n=i=a;else{360<(t=360===t?0:t)&&(t-=360),t<0&&(t+=360),t/=60;var l=dt(t),c=t-l,u=a*(1-r),h=u+o*(1-c),f=u+o*c,d=u+o;switch(l){case 0:[s,n,i]=[d,f,u];break;case 1:[s,n,i]=[h,d,u];break;case 2:[s,n,i]=[u,d,f];break;case 3:[s,n,i]=[u,h,d];break;case 4:[s,n,i]=[f,u,d];break;case 5:[s,n,i]=[d,u,h]}}return[s,n,i,3<e.length?e[3]:1]},pt=(...e)=>{var[e,t,r]=p(e,"rgb"),a=$e(e,t,r),s=Ae(e,t,r),n=s-a;let i;return 0==n?i=Number.NaN:(e===s&&(i=(t-r)/n),t===s&&(i=2+(r-e)/n),r===s&&(i=4+(e-t)/n),(i*=60)<0&&(i+=360)),[i,100*n/255,a/(255-n)*100]},d=(m.prototype.hcg=function(){return pt(this._rgb)},R.hcg=(...e)=>new m(...e,"hcg"),i.format.hcg=e,i.autodetect.push({p:1,test:(...e)=>{if("array"===O(e=p(e,"hcg"))&&3===e.length)return"hcg"}}),(n.hcg=(e,t,r)=>f(e,t,r,"hcg"),Math)["cos"]),e=(...e)=>{let[t,r,a]=e=p(e,"hsi"),s,n,i;return isNaN(t)&&(t=0),isNaN(r)&&(r=0),360<t&&(t-=360),t<0&&(t+=360),(t/=360)<1/3?(i=(1-r)/3,s=(1+r*d(h*t)/d(_e-h*t))/3,n=1-(i+s)):t<2/3?(t-=1/3,s=(1-r)/3,n=(1+r*d(h*t)/d(_e-h*t))/3,i=1-(s+n)):(t-=2/3,n=(1-r)/3,i=(1+r*d(h*t)/d(_e-h*t))/3,s=1-(n+i)),s=L(a*s*3),n=L(a*n*3),i=L(a*i*3),[255*s,255*n,255*i,3<e.length?e[3]:1]},{min:gt,sqrt:mt,acos:bt}=Math,vt=(...e)=>{var[e,t,r]=p(e,"rgb");let a;var s=gt(e/=255,t/=255,r/=255),n=(e+t+r)/3,s=0<n?1-s/n:0;return 0==s?a=NaN:(a=(e-t+(e-r))/2,a/=mt((e-t)*(e-t)+(e-r)*(t-r)),a=bt(a),t<r&&(a=h-a),a/=h),[360*a,s,n]},yt=(m.prototype.hsi=function(){return vt(this._rgb)},R.hsi=(...e)=>new m(...e,"hsi"),i.format.hsi=e,i.autodetect.push({p:2,test:(...e)=>{if("array"===O(e=p(e,"hsi"))&&3===e.length)return"hsi"}}),n.hsi=(e,t,r)=>f(e,t,r,"hsi"),(...e)=>{var[t,r,a]=e=p(e,"hsl");let s,n,i;if(0===r)s=n=i=255*a;else{var o=[0,0,0],l=[0,0,0],c=a<.5?a*(1+r):a+r-a*r,u=2*a-c,r=t/360;o[0]=r+1/3,o[1]=r,o[2]=r-1/3;for(let e=0;e<3;e++)o[e]<0&&(o[e]+=1),1<o[e]&&--o[e],6*o[e]<1?l[e]=u+6*(c-u)*o[e]:2*o[e]<1?l[e]=c:3*o[e]<2?l[e]=u+(c-u)*(2/3-o[e])*6:l[e]=u;[s,n,i]=[255*l[0],255*l[1],255*l[2]]}return 3<e.length?[s,n,i,e[3]]:[s,n,i,1]}),wt=(...e)=>{var[t,r,a]=e=p(e,"rgba"),s=$e(t/=255,r/=255,a/=255),n=Ae(t,r,a),i=(n+s)/2;let o,l;return n===s?(o=0,l=Number.NaN):o=i<.5?(n-s)/(n+s):(n-s)/(2-n-s),t==n?l=(r-a)/(n-s):r==n?l=2+(a-t)/(n-s):a==n&&(l=4+(t-r)/(n-s)),(l*=60)<0&&(l+=360),3<e.length&&void 0!==e[3]?[l,o,i,e[3]]:[l,o,i]},kt=(m.prototype.hsl=function(){return wt(this._rgb)},R.hsl=(...e)=>new m(...e,"hsl"),i.format.hsl=yt,i.autodetect.push({p:2,test:(...e)=>{if("array"===O(e=p(e,"hsl"))&&3===e.length)return"hsl"}}),(n.hsl=(e,t,r)=>f(e,t,r,"hsl"),Math)["floor"]),e=(...e)=>{let[t,r,a]=e=p(e,"hsv"),s,n,i;if(a*=255,0===r)s=n=i=a;else{360<(t=360===t?0:t)&&(t-=360),t<0&&(t+=360),t/=60;var o=kt(t),l=t-o,c=a*(1-r),u=a*(1-r*l),h=a*(1-r*(1-l));switch(o){case 0:[s,n,i]=[a,h,c];break;case 1:[s,n,i]=[u,a,c];break;case 2:[s,n,i]=[c,a,h];break;case 3:[s,n,i]=[c,u,a];break;case 4:[s,n,i]=[h,c,a];break;case 5:[s,n,i]=[a,c,u]}}return[s,n,i,3<e.length?e[3]:1]},{min:xt,max:St}=Math,Lt=(...e)=>{var[e,t,r]=e=p(e,"rgb"),a=xt(e,t,r),s=St(e,t,r),a=s-a;let n,i;return 0===s?(n=Number.NaN,i=0):(i=a/s,e===s&&(n=(t-r)/a),t===s&&(n=2+(r-e)/a),r===s&&(n=4+(e-t)/a),(n*=60)<0&&(n+=360)),[n,i,s/255]};m.prototype.hsv=function(){return Lt(this._rgb)},R.hsv=(...e)=>new m(...e,"hsv"),i.format.hsv=e,i.autodetect.push({p:2,test:(...e)=>{if("array"===O(e=p(e,"hsv"))&&3===e.length)return"hsv"}});function Ot(e,r){var t=e.length,a=(Array.isArray(e[0])||(e=[e]),(r=Array.isArray(r[0])?r:r.map(e=>[e]))[0].length);let s=r[0].map((e,t)=>r.map(e=>e[t])),n=e.map(r=>s.map(a=>Array.isArray(r)?r.reduce((e,t,r)=>e+t*(a[r]||0),0):a.reduce((e,t)=>e+t*r,0)));return 1===t&&(n=n[0]),1===a?n.map(e=>e[0]):n}n.hsv=(e,t,r)=>f(e,t,r,"hsv");var Rt=(...e)=>{var[e,t,r,...a]=e=p(e,"lab"),[e,t,r]=function(e){e=Ot([[1,.3963377773761749,.2158037573099136],[1,-.1055613458156586,-.0638541728258133],[1,-.0894841775298119,-1.2914855480194092]],e);return Ot([[1.2268798758459243,-.5578149944602171,.2813910456659647],[-.0405757452148008,1.112286803280317,-.0717110580655164],[-.0763729366746601,-.4214933324022432,1.5869240198367816]],e.map(e=>e**3))}([e,t,r]),[e,t,r]=Ye(e,t,r);return[e,t,r,...0<a.length&&a[0]<1?[a[0]]:[]]};var Nt=(...e)=>{var[e,t,r,...a]=p(e,"rgb");return[...function(e){e=Ot([[.819022437996703,.3619062600528904,-.1288737815209879],[.0329836539323885,.9292868615863434,.0361446663506424],[.0481771893596242,.2642395317527308,.6335478284694309]],e);return Ot([[.210454268309314,.7936177747023054,-.0040720430116193],[1.9779985324311684,-2.42859224204858,.450593709617411],[.0259040424655478,.7827717124575296,-.8086757549230774]],e.map(e=>Math.cbrt(e)))}(Xe(e,t,r)),...0<a.length&&a[0]<1?[a[0]]:[]]},{pow:Ct,sqrt:jt,PI:Pt,cos:Mt,sin:Et,atan2:$t}=(m.prototype.oklab=function(){return Nt(this._rgb)},Object.assign(R,{oklab:(...e)=>new m(...e,"oklab")}),i.format.oklab=Rt,i.autodetect.push({p:2,test:(...e)=>{if("array"===O(e=p(e,"oklab"))&&3===e.length)return"oklab"}}),n.oklab=(e,t,r)=>{e=e.oklab(),t=t.oklab();return new m(e[0]+r*(t[0]-e[0]),e[1]+r*(t[1]-e[1]),e[2]+r*(t[2]-e[2]),"oklab")},n.oklch=(e,t,r)=>f(e,t,r,"oklch"),Math),At=Math["pow"];function It(n){let i="rgb",o=R("#ccc"),t=0,l=[0,1],c=[],u=[0,0],h=!1,f=[],r=!1,d=0,p=1,a,g={},m=!0,b=1;function s(t){if((t=t||["#fff","#000"])&&"string"===O(t)&&R.brewer&&R.brewer[t.toLowerCase()]&&(t=R.brewer[t.toLowerCase()]),"array"===O(t)){t=(t=1===t.length?[t[0],t[0]]:t).slice(0);for(let e=0;e<t.length;e++)t[e]=R(t[e]);for(let e=c.length=0;e<t.length;e++)c.push(e/(t.length-1))}x(),f=t}const v=function(t){if(null==h)return 0;{var r=h.length-1;let e=0;for(;e<r&&t>=h[e];)e++;return e-1}};let y=e=>e,w=e=>e;function k(e,t){let r,a;if(null==t&&(t=!1),isNaN(e)||null===e)return o;if(a=t?e:h&&2<h.length?v(e)/(h.length-2):p!==d?(e-d)/(p-d):1,a=w(a),t||(a=y(a)),1!==b&&(a=At(a,b)),a=u[0]+a*(1-u[0]-u[1]),a=L(a,0,1),e=Math.floor(1e4*a),m&&g[e])r=g[e];else{if("array"===O(f))for(let e=0;e<c.length;e++){var s=c[e];if(a<=s){r=f[e];break}if(a>=s&&e===c.length-1){r=f[e];break}if(a>s&&a<c[e+1]){a=(a-s)/(c[e+1]-s),r=R.interpolate(f[e],f[e+1],a,i);break}}else"function"===O(f)&&(r=f(a));m&&(g[e]=r)}return r}var x=()=>g={};s(n);function S(e){return e=R(k(e)),r&&e[r]?e[r]():e}return S.classes=function(e){var t;return null!=e?("array"===O(e)?(h=e,l=[e[0],e[e.length-1]]):(t=R.analyze(l),h=0===e?[t.min,t.max]:R.limits(t,"e",e)),S):h},S.domain=function(r){if(!arguments.length)return l;d=r[0],p=r[r.length-1],c=[];var t=f.length;if(r.length===t&&d!==p)for(var e of Array.from(r))c.push((e-d)/(p-d));else{for(let e=0;e<t;e++)c.push(e/(t-1));if(2<r.length){const a=r.map((e,t)=>t/(r.length-1)),s=r.map(e=>(e-d)/(p-d));s.every((e,t)=>a[t]===e)||(w=e=>{if(e<=0||1<=e)return e;let t=0;for(;e>=s[t+1];)t++;var r=(e-s[t])/(s[t+1]-s[t]);return a[t]+r*(a[t+1]-a[t])})}}return l=[d,p],S},S.mode=function(e){return arguments.length?(i=e,x(),S):i},S.range=function(e,t){return s(e),S},S.out=function(e){return r=e,S},S.spread=function(e){return arguments.length?(t=e,S):t},S.correctLightness=function(e){return null==e&&(e=!0),a=e,x(),y=a?function(e){var t=k(0,!0).lab()[0],r=k(1,!0).lab()[0];const a=r<t;let s=k(e,!0).lab()[0];const n=t+(r-t)*e;let i=s-n,o=0,l=1,c=20;for(;.01<Math.abs(i)&&0<c--;)a&&(i*=-1),i<0?(o=e,e+=.5*(l-e)):(l=e,e+=.5*(o-e)),s=k(e,!0).lab()[0],i=s-n;return e}:e=>e,S},S.padding=function(e){return null!=e?("number"===O(e)&&(e=[e,e]),u=e,S):u},S.colors=function(t,r){arguments.length<2&&(r="hex");let e=[];if(0===arguments.length)e=f.slice(0);else if(1===t)e=[S(.5)];else if(1<t){const a=l[0],s=l[1]-a;e=function(t,e,r){var a=[],s=t<e,n=r?s?e+1:e-1:e;for(let e=t;s?e<n:e>n;s?e++:e--)a.push(e);return a}(0,t,!1).map(e=>S(a+e/(t-1)*s))}else{n=[];let a=[];if(h&&2<h.length)for(let e=1,t=h.length,r=1<=t;r?e<t:e>t;r?e++:e--)a.push(.5*(h[e-1]+h[e]));else a=l;e=a.map(e=>S(e))}return e=R[r]?e.map(e=>e[r]()):e},S.cache=function(e){return null!=e?(m=e,S):m},S.gamma=function(e){return null!=e?(b=e,S):b},S.nodata=function(e){return null!=e?(o=R(e),S):o},S}var _t=function(t){let r=[1,1];for(let e=1;e<t;e++){var a=[1];for(let e=1;e<=r.length;e++)a[e]=(r[e]||0)+r[e-1];r=a}return r},Ut=Math["round"],v=(m.prototype.rgb=function(e=!0){return!1===e?this._rgb.slice(0,3):this._rgb.slice(0,3).map(Ut)},m.prototype.rgba=function(r=!0){return this._rgb.slice(0,4).map((e,t)=>!(t<3)||!1===r?e:Ut(e))},Object.assign(R,{rgb:(...e)=>new m(...e,"rgb")}),i.format.rgb=(...e)=>{e=p(e,"rgba");return void 0===e[3]&&(e[3]=1),e},i.autodetect.push({p:3,test:(...e)=>{if("array"===O(e=p(e,"rgba"))&&(3===e.length||4===e.length&&"number"==O(e[3])&&0<=e[3]&&e[3]<=1))return"rgb"}}),(e,t,r)=>{if(v[r])return v[r](e,t);throw new Error("unknown blend mode "+r)}),e=r=>(e,t)=>{t=R(t).rgb(),e=R(e).rgb();return R.rgb(r(t,e))},t=a=>(e,t)=>{var r=[];return r[0]=a(e[0],t[0]),r[1]=a(e[1],t[1]),r[2]=a(e[2],t[2]),r},e=(v.normal=e(t(e=>e)),v.multiply=e(t((e,t)=>e*t/255)),v.screen=e(t((e,t)=>255*(1-(1-e/255)*(1-t/255)))),v.overlay=e(t((e,t)=>t<128?2*e*t/255:255*(1-2*(1-e/255)*(1-t/255)))),v.darken=e(t((e,t)=>t<e?t:e)),v.lighten=e(t((e,t)=>t<e?e:t)),v.dodge=e(t((e,t)=>255===e||255<(e=t/255*255/(1-e/255))?255:e)),v.burn=e(t((e,t)=>255*(1-(1-t/255)/(e/255)))),v),{pow:Ft,sin:Tt,cos:Vt}=Math;var{floor:Bt,random:Dt}=Math,{log:Kt,pow:qt,floor:zt,abs:Wt}=Math;function Ht(e,t=null){const r={min:Number.MAX_VALUE,max:-1*Number.MAX_VALUE,sum:0,values:[],count:0};return(e="object"===O(e)?Object.values(e):e).forEach(e=>{null==(e=t&&"object"===O(e)?e[t]:e)||isNaN(e)||(r.values.push(e),r.sum+=e,e<r.min&&(r.min=e),e>r.max&&(r.max=e),r.count+=1)}),r.domain=[r.min,r.max],r.limits=(e,t)=>Yt(r,e,t),r}function Yt(e,t="equal",n=7){var{min:i,max:o}=e="array"==O(e)?Ht(e):e,l=e.values.sort((e,t)=>e-t);if(1===n)return[i,o];var c=[];if("c"===t.substr(0,1)&&(c.push(i),c.push(o)),"e"===t.substr(0,1)){c.push(i);for(let e=1;e<n;e++)c.push(i+e/n*(o-i));c.push(o)}else if("l"===t.substr(0,1)){if(i<=0)throw new Error("Logarithmic scales are only possible for values > 0");var r=Math.LOG10E*Kt(i),a=Math.LOG10E*Kt(o);c.push(i);for(let e=1;e<n;e++)c.push(qt(10,r+e/n*(a-r)));c.push(o)}else if("q"===t.substr(0,1)){c.push(i);for(let e=1;e<n;e++){var s=(l.length-1)*e/n,u=zt(s);c.push(u===s?l[u]:l[u]*(1-(s=s-u))+l[u+1]*s)}c.push(o)}else if("k"===t.substr(0,1)){let t;var h=l.length,f=new Array(h),d=new Array(n);let r=!0,e=0,s=null;(s=[]).push(i);for(let e=1;e<n;e++)s.push(i+e/n*(o-i));for(s.push(o);r;){for(let e=0;e<n;e++)d[e]=0;for(let a=0;a<h;a++){var p=l[a];let t=Number.MAX_VALUE,r;for(let e=0;e<n;e++){var g=Wt(s[e]-p);g<t&&(t=g,r=e),d[r]++,f[a]=r}}var m=new Array(n);for(let e=0;e<n;e++)m[e]=null;for(let e=0;e<h;e++)null===m[t=f[e]]?m[t]=l[e]:m[t]+=l[e];for(let e=0;e<n;e++)m[e]*=1/d[e];r=!1;for(let e=0;e<n;e++)if(m[e]!==s[e]){r=!0;break}s=m,200<++e&&(r=!1)}var b={};for(let e=0;e<n;e++)b[e]=[];for(let e=0;e<h;e++)b[t=f[e]].push(l[e]);let a=[];for(let e=0;e<n;e++)a.push(b[e][0]),a.push(b[e][b[e].length-1]);a=a.sort((e,t)=>e-t),c.push(a[0]);for(let e=1;e<a.length;e+=2){var v=a[e];isNaN(v)||-1!==c.indexOf(v)||c.push(v)}}return c}function Gt(e,t,r){return.2126729*Math.pow(e/255,2.4)+.7151522*Math.pow(t/255,2.4)+.072175*Math.pow(r/255,2.4)}var{sqrt:y,pow:k,min:Jt,max:Xt,atan2:Zt,abs:Qt,cos:er,sin:tr,exp:rr,PI:ar}=Math;var t={cool(){return It([R.hsl(180,1,.9),R.hsl(250,.7,.4)])},hot(){return It(["#000","#f00","#ff0","#fff"]).mode("rgb")}},sr={OrRd:["#fff7ec","#fee8c8","#fdd49e","#fdbb84","#fc8d59","#ef6548","#d7301f","#b30000","#7f0000"],PuBu:["#fff7fb","#ece7f2","#d0d1e6","#a6bddb","#74a9cf","#3690c0","#0570b0","#045a8d","#023858"],BuPu:["#f7fcfd","#e0ecf4","#bfd3e6","#9ebcda","#8c96c6","#8c6bb1","#88419d","#810f7c","#4d004b"],Oranges:["#fff5eb","#fee6ce","#fdd0a2","#fdae6b","#fd8d3c","#f16913","#d94801","#a63603","#7f2704"],BuGn:["#f7fcfd","#e5f5f9","#ccece6","#99d8c9","#66c2a4","#41ae76","#238b45","#006d2c","#00441b"],YlOrBr:["#ffffe5","#fff7bc","#fee391","#fec44f","#fe9929","#ec7014","#cc4c02","#993404","#662506"],YlGn:["#ffffe5","#f7fcb9","#d9f0a3","#addd8e","#78c679","#41ab5d","#238443","#006837","#004529"],Reds:["#fff5f0","#fee0d2","#fcbba1","#fc9272","#fb6a4a","#ef3b2c","#cb181d","#a50f15","#67000d"],RdPu:["#fff7f3","#fde0dd","#fcc5c0","#fa9fb5","#f768a1","#dd3497","#ae017e","#7a0177","#49006a"],Greens:["#f7fcf5","#e5f5e0","#c7e9c0","#a1d99b","#74c476","#41ab5d","#238b45","#006d2c","#00441b"],YlGnBu:["#ffffd9","#edf8b1","#c7e9b4","#7fcdbb","#41b6c4","#1d91c0","#225ea8","#253494","#081d58"],Purples:["#fcfbfd","#efedf5","#dadaeb","#bcbddc","#9e9ac8","#807dba","#6a51a3","#54278f","#3f007d"],GnBu:["#f7fcf0","#e0f3db","#ccebc5","#a8ddb5","#7bccc4","#4eb3d3","#2b8cbe","#0868ac","#084081"],Greys:["#ffffff","#f0f0f0","#d9d9d9","#bdbdbd","#969696","#737373","#525252","#252525","#000000"],YlOrRd:["#ffffcc","#ffeda0","#fed976","#feb24c","#fd8d3c","#fc4e2a","#e31a1c","#bd0026","#800026"],PuRd:["#f7f4f9","#e7e1ef","#d4b9da","#c994c7","#df65b0","#e7298a","#ce1256","#980043","#67001f"],Blues:["#f7fbff","#deebf7","#c6dbef","#9ecae1","#6baed6","#4292c6","#2171b5","#08519c","#08306b"],PuBuGn:["#fff7fb","#ece2f0","#d0d1e6","#a6bddb","#67a9cf","#3690c0","#02818a","#016c59","#014636"],Viridis:["#440154","#482777","#3f4a8a","#31678e","#26838f","#1f9d8a","#6cce5a","#b6de2b","#fee825"],Spectral:["#9e0142","#d53e4f","#f46d43","#fdae61","#fee08b","#ffffbf","#e6f598","#abdda4","#66c2a5","#3288bd","#5e4fa2"],RdYlGn:["#a50026","#d73027","#f46d43","#fdae61","#fee08b","#ffffbf","#d9ef8b","#a6d96a","#66bd63","#1a9850","#006837"],RdBu:["#67001f","#b2182b","#d6604d","#f4a582","#fddbc7","#f7f7f7","#d1e5f0","#92c5de","#4393c3","#2166ac","#053061"],PiYG:["#8e0152","#c51b7d","#de77ae","#f1b6da","#fde0ef","#f7f7f7","#e6f5d0","#b8e186","#7fbc41","#4d9221","#276419"],PRGn:["#40004b","#762a83","#9970ab","#c2a5cf","#e7d4e8","#f7f7f7","#d9f0d3","#a6dba0","#5aae61","#1b7837","#00441b"],RdYlBu:["#a50026","#d73027","#f46d43","#fdae61","#fee090","#ffffbf","#e0f3f8","#abd9e9","#74add1","#4575b4","#313695"],BrBG:["#543005","#8c510a","#bf812d","#dfc27d","#f6e8c3","#f5f5f5","#c7eae5","#80cdc1","#35978f","#01665e","#003c30"],RdGy:["#67001f","#b2182b","#d6604d","#f4a582","#fddbc7","#ffffff","#e0e0e0","#bababa","#878787","#4d4d4d","#1a1a1a"],PuOr:["#7f3b08","#b35806","#e08214","#fdb863","#fee0b6","#f7f7f7","#d8daeb","#b2abd2","#8073ac","#542788","#2d004b"],Set2:["#66c2a5","#fc8d62","#8da0cb","#e78ac3","#a6d854","#ffd92f","#e5c494","#b3b3b3"],Accent:["#7fc97f","#beaed4","#fdc086","#ffff99","#386cb0","#f0027f","#bf5b17","#666666"],Set1:["#e41a1c","#377eb8","#4daf4a","#984ea3","#ff7f00","#ffff33","#a65628","#f781bf","#999999"],Set3:["#8dd3c7","#ffffb3","#bebada","#fb8072","#80b1d3","#fdb462","#b3de69","#fccde5","#d9d9d9","#bc80bd","#ccebc5","#ffed6f"],Dark2:["#1b9e77","#d95f02","#7570b3","#e7298a","#66a61e","#e6ab02","#a6761d","#666666"],Paired:["#a6cee3","#1f78b4","#b2df8a","#33a02c","#fb9a99","#e31a1c","#fdbf6f","#ff7f00","#cab2d6","#6a3d9a","#ffff99","#b15928"],Pastel2:["#b3e2cd","#fdcdac","#cbd5e8","#f4cae4","#e6f5c9","#fff2ae","#f1e2cc","#cccccc"],Pastel1:["#fbb4ae","#b3cde3","#ccebc5","#decbe4","#fed9a6","#ffffcc","#e5d8bd","#fddaec","#f2f2f2"]},nr=Object.keys(sr),ir=new Map(nr.map(e=>[e.toLowerCase(),e])),sr="function"==typeof Proxy?new Proxy(sr,{get(e,t){t=t.toLowerCase();if(ir.has(t))return e[ir.get(t)]},getOwnPropertyNames(){return Object.getOwnPropertyNames(nr)}}):sr,x=(...e)=>{var[t,r,a,s]=e=p(e,"cmyk"),e=4<e.length?e[4]:1;return 1===s?[0,0,0,e]:[1<=t?0:255*(1-t)*(1-s),1<=r?0:255*(1-r)*(1-s),1<=a?0:255*(1-a)*(1-s),e]},or=Math["max"],lr=(...e)=>{var[e,t,r]=p(e,"rgb"),a=1-or(e/=255,or(t/=255,r/=255)),s=a<1?1/(1-a):0;return[(1-e-a)*s,(1-t-a)*s,(1-r-a)*s,a]},cr=(m.prototype.cmyk=function(){return lr(this._rgb)},Object.assign(R,{cmyk:(...e)=>new m(...e,"cmyk")}),i.format.cmyk=x,i.autodetect.push({p:2,test:(...e)=>{if("array"===O(e=p(e,"cmyk"))&&4===e.length)return"cmyk"}}),(...e)=>{var t=p(e,"hsla");let r=l(e)||"lsa";return t[0]=a(t[0]||0)+"deg",t[1]=a(100*t[1])+"%",t[2]=a(100*t[2])+"%","hsla"===r||3<t.length&&t[3]<1?(t[3]="/ "+(3<t.length?t[3]:1),r="hsla"):t.length=3,`${r.substr(0,3)}(${t.join(" ")})`}),ur=(...e)=>{var t=p(e,"lab"),e=l(e)||"lab";return t[0]=a(t[0])+"%",t[1]=a(t[1]),t[2]=a(t[2]),"laba"===e||3<t.length&&t[3]<1?t[3]="/ "+(3<t.length?t[3]:1):t.length=3,`lab(${t.join(" ")})`},hr=(...e)=>{var t=p(e,"lch"),e=l(e)||"lab";return t[0]=a(t[0])+"%",t[1]=a(t[1]),t[2]=isNaN(t[2])?"none":a(t[2])+"deg","lcha"===e||3<t.length&&t[3]<1?t[3]="/ "+(3<t.length?t[3]:1):t.length=3,`lch(${t.join(" ")})`},fr=(...e)=>{e=p(e,"lab");return e[0]=a(100*e[0])+"%",e[1]=Ie(e[1]),e[2]=Ie(e[2]),3<e.length&&e[3]<1?e[3]="/ "+(3<e.length?e[3]:1):e.length=3,`oklab(${e.join(" ")})`},dr=(...e)=>{var[e,t,r,...a]=p(e,"rgb"),[e,t,r]=Nt(e,t,r),[e,t,r]=ct(e,t,r);return[e,t,r,...0<a.length&&a[0]<1?[a[0]]:[]]},pr=(...e)=>{e=p(e,"lch");return e[0]=a(100*e[0])+"%",e[1]=Ie(e[1]),e[2]=isNaN(e[2])?"none":a(e[2])+"deg",3<e.length&&e[3]<1?e[3]="/ "+(3<e.length?e[3]:1):e.length=3,`oklch(${e.join(" ")})`},gr=Math["round"],mr=(...e)=>{var t,r=p(e,"rgba");let a=l(e)||"rgb";return"hsl"===a.substr(0,3)?cr(wt(r),a):"lab"===a.substr(0,3)?(e=We(),c("d50"),t=ur(Ze(r),a),c(e),t):"lch"===a.substr(0,3)?(e=We(),c("d50"),t=hr(ut(r),a),c(e),t):"oklab"===a.substr(0,5)?fr(Nt(r)):"oklch"===a.substr(0,5)?pr(dr(r)):(r[0]=gr(r[0]),r[1]=gr(r[1]),r[2]=gr(r[2]),("rgba"===a||3<r.length&&r[3]<1)&&(r[3]="/ "+(3<r.length?r[3]:1),a="rgba"),`${a.substr(0,3)}(${r.slice(0,"rgb"===a?3:4).join(" ")})`)},br=(...e)=>{var[e,t,r,...a]=e=p(e,"lch"),[e,t,r]=st(e,t,r),[e,t,r]=Rt(e,t,r);return[e,t,r,...0<a.length&&a[0]<1?[a[0]]:[]]},x=/((?:-?\d+)|(?:-?\d+(?:\.\d+)?)%|none)/.source,S=/((?:-?(?:\d+(?:\.\d*)?|\.\d+)%?)|none)/.source,N=/((?:-?(?:\d+(?:\.\d*)?|\.\d+)%)|none)/.source,C=/\s*/.source,j=/\s+/.source,vr=/\s*,\s*/.source,yr=/((?:-?(?:\d+(?:\.\d*)?|\.\d+)(?:deg)?)|none)/.source,P=/\s*(?:\/\s*((?:[01]|[01]?\.\d+)|\d+(?:\.\d+)?%))?/.source,wr=new RegExp("^rgba?\\("+C+[x,x,x].join(j)+P+"\\)$"),kr=new RegExp("^rgb\\("+C+[x,x,x].join(vr)+C+"\\)$"),xr=new RegExp("^rgba\\("+C+[x,x,x,S].join(vr)+C+"\\)$"),Sr=new RegExp("^hsla?\\("+C+[yr,N,N].join(j)+P+"\\)$"),Lr=new RegExp("^hsl?\\("+C+[yr,N,N].join(vr)+C+"\\)$"),Or=/^hsla\(\s*(-?\d+(?:\.\d+)?),\s*(-?\d+(?:\.\d+)?)%\s*,\s*(-?\d+(?:\.\d+)?)%\s*,\s*([01]|[01]?\.\d+)\)$/,Rr=new RegExp("^lab\\("+C+[S,S,S].join(j)+P+"\\)$"),Nr=new RegExp("^lch\\("+C+[S,S,yr].join(j)+P+"\\)$"),Cr=new RegExp("^oklab\\("+C+[S,S,S].join(j)+P+"\\)$"),jr=new RegExp("^oklch\\("+C+[S,S,yr].join(j)+P+"\\)$"),Pr=Math["round"],E=e=>e.map((e,t)=>t<=2?L(Pr(e),0,255):e),$=(e,t=0,r=100,a=!1)=>("string"==typeof e&&e.endsWith("%")&&(e=parseFloat(e.substring(0,e.length-1))/100,e=a?t+.5*(e+1)*(r-t):t+e*(r-t)),+e),A=(e,t)=>"none"===e?t:e,x=e=>{if("transparent"===(e=e.toLowerCase().trim()))return[0,0,0,0];let r;if(i.format.named)try{return i.format.named(e)}catch(e){}if(r=(r=e.match(wr))||e.match(kr)){let t=r.slice(1,4);for(let e=0;e<3;e++)t[e]=+$(A(t[e],0),0,255);t=E(t);var a=void 0!==r[4]?+$(r[4],0,1):1;return t[3]=a,t}if(r=e.match(xr)){var t=r.slice(1,5);for(let e=0;e<4;e++)t[e]=+$(t[e],0,255);return t}if(r=(r=e.match(Sr))||e.match(Lr))return(a=r.slice(1,4))[0]=+A(a[0].replace("deg",""),0),a[1]=.01*+$(A(a[1],0),0,100),a[2]=.01*+$(A(a[2],0),0,100),a=E(yt(a)),s=void 0!==r[4]?+$(r[4],0,1):1,a[3]=s,a;if(r=e.match(Or)){var s=r.slice(1,4),n=(s[1]*=.01,s[2]*=.01,yt(s));for(let e=0;e<3;e++)n[e]=Pr(n[e]);return n[3]=+r[4],n}return(r=e.match(Rr))?((a=r.slice(1,4))[0]=$(A(a[0],0),0,100),a[1]=$(A(a[1],0),-125,125,!0),a[2]=$(A(a[2],0),-125,125,!0),s=We(),c("d50"),a=E(Ge(a)),c(s),s=void 0!==r[4]?+$(r[4],0,1):1,a[3]=s,a):(r=e.match(Nr))?((s=r.slice(1,4))[0]=$(s[0],0,100),s[1]=$(A(s[1],0),0,150,!1),s[2]=+A(s[2].replace("deg",""),0),a=We(),c("d50"),s=E(nt(s)),c(a),a=void 0!==r[4]?+$(r[4],0,1):1,s[3]=a,s):(r=e.match(Cr))?((a=r.slice(1,4))[0]=$(A(a[0],0),0,1),a[1]=$(A(a[1],0),-.4,.4,!0),a[2]=$(A(a[2],0),-.4,.4,!0),s=E(Rt(a)),a=void 0!==r[4]?+$(r[4],0,1):1,s[3]=a,s):(r=e.match(jr))?((a=r.slice(1,4))[0]=$(A(a[0],0),0,1),a[1]=$(A(a[1],0),0,.4,!1),a[2]=+A(a[2].replace("deg",""),0),s=E(br(a)),e=void 0!==r[4]?+$(r[4],0,1):1,s[3]=e,s):void 0},Mr=(x.test=e=>wr.test(e)||Sr.test(e)||Rr.test(e)||Nr.test(e)||Cr.test(e)||jr.test(e)||kr.test(e)||xr.test(e)||Lr.test(e)||Or.test(e)||"transparent"===e,x),Er=(m.prototype.css=function(e){return mr(this._rgb,e)},R.css=(...e)=>new m(...e,"css"),i.format.css=Mr,i.autodetect.push({p:5,test:(e,...t)=>{if(!t.length&&"string"===O(e)&&Mr.test(e))return"css"}}),i.format.gl=(...e)=>{e=p(e,"rgba");return e[0]*=255,e[1]*=255,e[2]*=255,e},R.gl=(...e)=>new m(...e,"gl"),m.prototype.gl=function(){var e=this._rgb;return[e[0]/255,e[1]/255,e[2]/255,e[3]]},m.prototype.hex=function(e){return Ke(this._rgb,e)},(R.hex=(...e)=>new m(...e,"hex"),i.format.hex=Be,i.autodetect.push({p:4,test:(e,...t)=>{if(!t.length&&"string"===O(e)&&0<=[3,4,5,6,7,8,9].indexOf(e.length))return"hex"}}),Math)["log"]),$r=e=>{e/=100;let t,r,a;return a=e<66?(t=255,r=e<6?0:-155.25485562709179-.44596950469579133*(r=e-2)+104.49216199393888*Er(r),e<20?0:.8274096064007395*(a=e-10)-254.76935184120902+115.67994401066147*Er(a)):(t=351.97690566805693+.114206453784165*(t=e-55)-40.25366309332127*Er(t),r=325.4494125711974+.07943456536662342*(r=e-50)-28.0852963507957*Er(r),255),[t,r,a,1]},Ar=Math["round"],Ir=(...e)=>{var e=p(e,"rgb"),t=e[0],r=e[2];let a=1e3,s=4e4;let n;for(;.4<s-a;){n=.5*(s+a);var i=$r(n);i[2]/i[0]>=r/t?s=n:a=n}return Ar(n)},N=(m.prototype.temp=m.prototype.kelvin=m.prototype.temperature=function(){return Ir(this._rgb)},(...e)=>new m(...e,"temp")),_r=(Object.assign(R,{temp:N,kelvin:N,temperature:N}),i.format.temp=i.format.kelvin=i.format.temperature=$r,m.prototype.oklch=function(){return dr(this._rgb)},Object.assign(R,{oklch:(...e)=>new m(...e,"oklch")}),i.format.oklch=br,i.autodetect.push({p:2,test:(...e)=>{if("array"===O(e=p(e,"oklch"))&&3===e.length)return"oklch"}}),Object.assign(R,{analyze:Ht,average:(e,o="lrgb",l=null)=>{var t=e.length;const r=t/(l=l||Array.from(new Array(t)).map(()=>1)).reduce(function(e,t){return e+t});if(l.forEach((e,t)=>{l[t]*=r}),e=e.map(e=>new m(e)),"lrgb"===o){var a=e,s=l,n=a.length,i=[0,0,0,0];for(let e=0;e<a.length;e++){var c=a[e],u=s[e]/n,c=c._rgb;i[0]+=Ct(c[0],2)*u,i[1]+=Ct(c[1],2)*u,i[2]+=Ct(c[2],2)*u,i[3]+=c[3]*u}return i[0]=jt(i[0]),i[1]=jt(i[1]),i[2]=jt(i[2]),.9999999<i[3]&&(i[3]=1),new m(Me(i))}{var h,f=e.shift();const d=f.get(o),p=[];let s=0,n=0;for(let e=0;e<d.length;e++)d[e]=(d[e]||0)*l[0],p.push(isNaN(d[e])?0:l[0]),"h"!==o.charAt(e)||isNaN(d[e])||(h=d[e]/180*Pt,s+=Mt(h)*l[0],n+=Et(h)*l[0]);let i=f.alpha()*l[0];e.forEach((e,t)=>{var r,a=e.get(o);i+=e.alpha()*l[t+1];for(let e=0;e<d.length;e++)isNaN(a[e])||(p[e]+=l[t+1],"h"===o.charAt(e)?(r=a[e]/180*Pt,s+=Mt(r)*l[t+1],n+=Et(r)*l[t+1]):d[e]+=a[e]*l[t+1])});for(let t=0;t<d.length;t++)if("h"===o.charAt(t)){let e=$t(n/p[t],s/p[t])/Pt*180;for(;e<0;)e+=360;for(;360<=e;)e-=360;d[t]=e}else d[t]=d[t]/p[t];return i/=t,new m(d,o).alpha(.99999<i?1:i,!0)}},bezier:e=>{const t=function(e){let a,s,n,i;if(2===(e=e.map(e=>new m(e))).length)[s,n]=e.map(e=>e.lab()),a=function(t){var e=[0,1,2].map(e=>s[e]+t*(n[e]-s[e]));return new m(e,"lab")};else if(3===e.length)[s,n,i]=e.map(e=>e.lab()),a=function(t){var e=[0,1,2].map(e=>(1-t)*(1-t)*s[e]+2*(1-t)*t*n[e]+t*t*i[e]);return new m(e,"lab")};else if(4===e.length){let r;[s,n,i,r]=e.map(e=>e.lab()),a=function(t){var e=[0,1,2].map(e=>(1-t)*(1-t)*(1-t)*s[e]+3*(1-t)*(1-t)*t*n[e]+3*(1-t)*t*t*i[e]+t*t*t*r[e]);return new m(e,"lab")}}else{if(!(5<=e.length))throw new RangeError("No point in running bezier with only one color.");{let t,i,o;t=e.map(e=>e.lab()),o=e.length-1,i=_t(o),a=function(s){const n=1-s;var e=[0,1,2].map(a=>t.reduce((e,t,r)=>e+i[r]*n**(o-r)*s**r*t[a],0));return new m(e,"lab")}}}return a}(e);return t.scale=()=>It(t),t},blend:e,brewer:sr,Color:m,colors:r,contrast:(e,t)=>{e=new m(e),t=new m(t);e=e.luminance(),t=t.luminance();return t<e?(e+.05)/(t+.05):(t+.05)/(e+.05)},contrastAPCA:(e,t)=>{e=new m(e),t=new m(t);var e=Gt(...(e=e.alpha()<1?s(t,e,e.alpha(),"rgb"):e).rgb()),t=Gt(...t.rgb()),e=.022<=e?e:e+Math.pow(.022-e,1.414),t=.022<=t?t:t+Math.pow(.022-t,1.414),r=Math.pow(t,.56)-Math.pow(e,.57),a=Math.pow(t,.65)-Math.pow(e,.62),e=Math.abs(t-e)<5e-4?0:e<t?1.14*r:1.14*a;return 100*(Math.abs(e)<.1?0:0<e?e-.027:.027+e)},cubehelix:function(s=300,n=-1.5,i=1,o=1,l=[0,1]){let c=0,u;function t(e){var t=h*((s+120)/360+n*e),r=Ft(l[0]+u*e,o),e=(0!==c?i[0]+e*c:i)*r*(1-r)/2,a=Vt(t),t=Tt(t);return R(Me([255*(r+e*(-.14861*a+1.78277*t)),255*(r+e*(-.29227*a-.90649*t)),255*(r+1.97294*a*e),1]))}return"array"===O(l)?u=l[1]-l[0]:(u=0,l=[l,l]),t.start=function(e){return null==e?s:(s=e,t)},t.rotations=function(e){return null==e?n:(n=e,t)},t.gamma=function(e){return null==e?o:(o=e,t)},t.hue=function(e){return null==e?i:("array"===O(i=e)?0===(c=i[1]-i[0])&&(i=i[1]):c=0,t)},t.lightness=function(e){return null==e?l:(u="array"===O(e)?(l=e)[1]-e[0]:(l=[e,e],0),t)},t.scale=()=>R.scale(t),t.hue(i),t},deltaE:function(e,t,r=1,a=1,s=1){function n(e){return 360*e/(2*ar)}function i(e){return 2*ar*e/360}e=new m(e),t=new m(t);var[e,o,l]=Array.from(e.lab()),[t,c,u]=Array.from(t.lab()),h=(e+t)/2,f=(y(k(o,2)+k(l,2))+y(k(c,2)+k(u,2)))/2,f=.5*(1-y(k(f,7)/(k(f,7)+k(25,7)))),c=c*(1+f),f=y(k(o=o*(1+f),2)+k(l,2)),d=y(k(c,2)+k(u,2)),p=(f+d)/2,l=n(Zt(l,o)),o=n(Zt(u,c)),l=180<Qt((u=0<=l?l:l+360)-(c=0<=o?o:o+360))?(u+c+360)/2:(u+c)/2,o=1-.17*er(i(l-30))+.24*er(i(2*l))+.32*er(i(3*l+6))-.2*er(i(4*l-63)),g=Qt(g=c-u)<=180?g:c<=u?360+g:g-360,c=(g=2*y(f*d)*tr(i(g)/2),t-e),u=d-f,t=1+.015*k(h-50,2)/y(20+k(h-50,2)),e=1+.045*p,d=1+.015*p*o,f=30*rr(-k((l-275)/25,2)),h=-(2*y(k(p,7)/(k(p,7)+k(25,7))))*tr(2*i(f)),o=y(k(c/(r*t),2)+k(u/(a*e),2)+k(g/(s*d),2)+u/(a*e)*h*(g/(s*d)));return Xt(0,Jt(100,o))},distance:function(e,t,r="lab"){e=new m(e),t=new m(t);var a,s=e.get(r),n=t.get(r);let i=0;for(a in s){var o=(s[a]||0)-(n[a]||0);i+=o*o}return Math.sqrt(i)},input:i,interpolate:s,limits:Yt,mix:s,random:()=>{let t="#";for(let e=0;e<6;e++)t+="0123456789abcdef".charAt(Bt(16*Dt()));return new m(t,"hex")},scale:It,scales:t,valid:(...e)=>{try{return new m(...e),!0}catch(e){return!1}}}),R),I=(t,e)=>{t=localStorage.getItem(t);if(!t)return e;try{return JSON.parse(t)}catch(e){return t}},Ur=e=>{e=3===e.length?e.split("").map(e=>e+e).join(""):e;if(6!==e.length)throw"Only 3- or 6-digit hex colours are allowed.";if(e.match(/[^0-9a-f]/i))throw"Only hex colours are allowed.";e=e.match(/.{1,2}/g);if(e&&3===e.length)return[Number.parseInt(e[0],16),Number.parseInt(e[1],16),Number.parseInt(e[2],16)];throw"Could not parse hex colour."},Fr=(e,t)=>{let r=[];return e&&0<e.length?r=e.map(e=>({name:e.name,url:Yr(e.url)})):r.push({name:t,url:"https://github.com/"+t}),r},Tr=(...e)=>{console.debug("Resetting Marketplace");var t=[];if(0===e.length)for(const r in localStorage)r.startsWith("marketplace:")&&t.push(r);for(const a of e)switch(a){case"extensions":t.push(...I(g.installedExtensions,[])),t.push(g.installedExtensions);break;case"snippets":t.push(...I(g.installedSnippets,[])),t.push(g.installedSnippets);break;case"theme":t.push(...I(g.installedThemes,[])),t.push(g.installedThemes),t.push(g.themeInstalled);break;default:console.error("Unknown category: "+a)}for(const s of t)localStorage.removeItem(s),console.debug("Removed "+s);console.debug("Marketplace has been reset"),location.reload()},Vr=()=>{var e={};for(const t in localStorage)t.startsWith("marketplace:")&&(e[t]=localStorage.getItem(t));return e},Br=t=>{var r=document.querySelector("style.marketplaceCSS.marketplaceScheme");if(r&&r.remove(),t){r=document.createElement("style");r.classList.add("marketplaceCSS"),r.classList.add("marketplaceScheme");let e=":root {";for(const a of Object.keys(t))e=(e+=`--spice-${a}: #${t[a]};`)+`--spice-rgb-${a}: ${Ur(t[a])};`;e+="}",r.innerHTML=e,document.body.appendChild(r)}},Dr=async e=>{let t=I(g.albumArtBasedColorVibrancy);return t=t.replace(/([A-Z])/g,"_$1").toUpperCase(),(await Spicetify.colorExtractor(e))[t].substring(1)},Kr=async(e,t)=>{var r=I(g.albumArtBasedColorMode).replace(/([A-Z])/g,"-$1").toLowerCase();return(await fetch(`https://www.thecolorapi.com/scheme?hex=${e}&mode=${r}&count=`+t).then(e=>e.json())).colors.map(e=>e.hex.value.substring(1))};var qr=f=>{Spicetify.Player.addEventListener("songchange",async()=>{var t;t=1e3,await new Promise(e=>setTimeout(e,t));let r=Spicetify.Player.data?.item?.metadata?.image_xlarge_url;if(r=null==r?await new Promise(t=>{setInterval(()=>{var e=Spicetify.Player.data?.item?.metadata?.image_xlarge_url;e&&t(e)},50)}):r){var a,s,n=new Set(Object.values(f)).size,i=await Dr(r),o=await Kr(i,n);let e=new Map;for([a,s]of Object.entries(f))e.has(s)?e.get(s).push(a):e.set(s,[a]);var l,i=new Map([...e.entries()].sort((e,t)=>{e=_r(e[0]),t=_r(t[0]);return e.get("lab.l")-t.get("lab.l")})),c={};for([,l]of(e=i).entries()){var u=o.shift();if(u)for(const h of l)c[h]=u}Br(c)}})},zr=e=>{e=new URL(e);return e.host,"raw.githubusercontent.com"===e.host},Wr=e=>{e=e.match(/https:\/\/raw\.githubusercontent\.com\/(?<user>[^/]+)\/(?<repo>[^/]+)\/(?<branch>[^/]+)\/(?<filePath>.+$)/);return{user:e?e.groups?.user:null,repo:e?e.groups?.repo:null,branch:e?e.groups?.branch:null,filePath:e?e.groups?.filePath:null}};function Hr(e,t){if(e)for(const s of e){var r=t||s.user+"-"+s.repo,a=window.sessionStorage.getItem(r),a=a?JSON.parse(a):[];a.push(s),window.sessionStorage.setItem(r,JSON.stringify(a))}}var Yr=e=>{var t=decodeURI(e).trim().toLowerCase();return t.startsWith("javascript:")||t.startsWith("data:")||t.startsWith("vbscript:")?"about:blank":e},Gr=e=>{e&&(e=e.split("/").pop())&&-1===Spicetify.Config.extensions.indexOf(e)&&Spicetify.Config.extensions.push(e)};async function Jr(){for(const e of["net","xyz"])try{if("opaqueredirect"===(await fetch("https://cdn.jsdelivr."+e,{redirect:"manual",cache:"no-cache"})).type)return e}catch(e){console.error(e)}}var vr=new Blob([`
  self.addEventListener('message', async (event) => {
    const url = event.data;
    const response = await fetch(url);
    const data = await response.json().catch(() => null);
    self.postMessage(data);
  });
`],{type:"application/javascript"}),Xr=URL.createObjectURL(vr);async function Zr(e,t,r){var a=e+"-"+t,s=window.sessionStorage.getItem(a),n=JSON.parse(window.sessionStorage.getItem("noManifests")||"[]");if(s)return JSON.parse(s);s=`https://raw.githubusercontent.com/${e}/${t}/${r}/manifest.json`;if(n.includes(s))return null;let i=await async function(e){const a=new Worker(Xr);return new Promise(t=>{const r=e=>{a.terminate(),t(e)};a.postMessage(e),a.addEventListener("message",e=>r(e.data),{once:!0}),a.addEventListener("error",()=>r(null),{once:!0})})}(s);return i?(Hr(i=Array.isArray(i)?i:[i],a),i):Hr([s],"noManifests")}async function Qr(e,t){var r=await async function(e,t=1){const r=window.sessionStorage.getItem("marketplace:blacklist");let a=`https://api.github.com/search/repositories?per_page=${Ce}&q=`+encodeURIComponent(`topic:spicetify-${e}s`);t&&(a+="&page="+t);var s=JSON.parse(window.sessionStorage.getItem(`spicetify-${e}s-page-`+t)||"null")||await fetch(a).then(e=>e.json()).catch(()=>null);return s?.items?(window.sessionStorage.setItem(`spicetify-${e}s-page-`+t,JSON.stringify(s)),{...s,page_count:s.items.length,items:s.items.filter(e=>!r?.includes(e.html_url))}):(Spicetify.showNotification(Ne("notifications.tooManyRequests"),!0,5e3),{items:[]})}(e,t),a=(!async function(e,t){for(const r of e.items)"theme"===t?await async function(e,a,s){try{var t=e.match(/https:\/\/api\.github\.com\/repos\/(?<user>.+)\/(?<repo>.+)\/contents/);if(!t||!t.groups)return;const{user:n,repo:i}=t.groups;return(await Zr(n,i,a)).reduce((e,t)=>{var r;return t?.name&&t?.usercss&&t?.description&&(r=t.branch||a,r={manifest:t,title:t.name,subtitle:t.description,authors:Fr(t.authors,n),user:n,repo:i,branch:r,imageURL:t.preview?.startsWith("http")?t.preview:`https://raw.githubusercontent.com/${n}/${i}/${r}/`+t.preview,readmeURL:t.readme?.startsWith("http")?t.readme:`https://raw.githubusercontent.com/${n}/${i}/${r}/`+t.readme,stars:s,tags:t.tags,cssURL:t.usercss.startsWith("http")?t.usercss:`https://raw.githubusercontent.com/${n}/${i}/${r}/`+t.usercss,schemesURL:t.schemes?t.schemes.startsWith("http")?t.schemes:`https://raw.githubusercontent.com/${n}/${i}/${r}/`+t.schemes:null,include:t.include},e.push(r)),e},[])}catch{return}}(r.contents_url,r.default_branch,r.stargazers_count):"extension"===t?await async function(e,a,s,n=!1){try{var t=e.match(/https:\/\/api\.github\.com\/repos\/(?<user>.+)\/(?<repo>.+)\/contents/);if(!t||!t.groups)return;const{user:i,repo:o}=t.groups;return(await Zr(i,o,a)).reduce((e,t)=>{var r;return t?.name&&t.description&&t.main&&(r=t.branch||a,r={manifest:t,title:t.name,subtitle:t.description,authors:Fr(t.authors,i),user:i,repo:o,branch:r,imageURL:t.preview?.startsWith("http")?t.preview:`https://raw.githubusercontent.com/${i}/${o}/${r}/`+t.preview,extensionURL:t.main.startsWith("http")?t.main:`https://raw.githubusercontent.com/${i}/${o}/${r}/`+t.main,readmeURL:t.readme?.startsWith("http")?t.readme:`https://raw.githubusercontent.com/${i}/${o}/${r}/`+t.readme,stars:s,tags:t.tags},n&&localStorage.getItem(`marketplace:installed:${i}/${o}/`+t.main)||e.push(r)),e},[])}catch{return}}(r.contents_url,r.default_branch,r.stargazers_count):"app"===t&&await async function(e,a,s){try{var t=e.match(/https:\/\/api\.github\.com\/repos\/(?<user>.+)\/(?<repo>.+)\/contents/);if(!t||!t.groups)return;const{user:n,repo:i}=t.groups;return(await Zr(n,i,a)).reduce((e,t)=>{var r;return t?.name&&t.description&&!t.main&&!t.usercss&&(r=t.branch||a,r={manifest:t,title:t.name,subtitle:t.description,authors:Fr(t.authors,n),user:n,repo:i,branch:r,imageURL:t.preview?.startsWith("http")?t.preview:`https://raw.githubusercontent.com/${n}/${i}/${r}/`+t.preview,readmeURL:t.readme?.startsWith("http")?t.readme:`https://raw.githubusercontent.com/${n}/${i}/${r}/`+t.readme,stars:s,tags:t.tags},e.push(r)),e},[])}catch{return}}(r.contents_url,r.default_branch,r.stargazers_count)}(r,e),Ce*t+r.page_count),s=(console.debug({pageOfRepos:r}),r.total_count-a);if(console.debug(`Parsed ${a}/${r.total_count} ${e}s`),0<s)return Qr(e,t+1);console.debug(`No more ${e} results`)}!async function e(){if(Spicetify.LocalStorage&&Spicetify.showNotification){var t=document.createElement("script");t.innerHTML="const global = globalThis;",document.body.appendChild(t),console.log("Initializing Spicetify Marketplace v1.0.6"),window.Marketplace={reset:Tr,export:Vr,version:"1.0.6"};const p=await Jr();t=async t=>{t=I(t);if(t){if(console.debug("Initializing theme: ",t),t.schemes){var e=t.schemes[t.activeScheme];if(Br(e),Spicetify.Config.color_scheme=t.activeScheme,"true"===localStorage.getItem(g.albumArtBasedColor))qr(e);else if("true"===localStorage.getItem(g.colorShift)){var r=t.schemes;let e=0;const f=Object.keys(r).length;setInterval(()=>{e%=f,Br(Object.values(r)[e]),e++},6e4)}}else console.warn("No schemes found for theme");e=document.querySelector("link.marketplaceCSS"),e=(e&&e.remove(),await(async(e,t)=>{if(!e.cssURL)throw new Error("No CSS URL provided");var t=t||await Jr(),t=zr(e.cssURL)?`https://cdn.jsdelivr.${t}/gh/${e.user}/${e.repo}@${e.branch}/`+e.manifest.usercss:e.cssURL,r=t.replace("/user.css","/assets/");console.debug("Parsing CSS: ",t);let a=await fetch(t+"?time="+Date.now()).then(e=>e.text());for(const i of a.matchAll(/url\(['|"](?<path>.+?)['|"]\)/gm)||[]){var s,n=i?.groups?.path;!n||n.startsWith("http")||n.startsWith("data")||(s=r+n.replace(/\.\//g,""),a=a.replace(n,s))}return a})(t,p));try{var a,s,n=document.querySelector("link[href='user.css']"),i=(n&&n.remove(),document.querySelector("style.marketplaceCSS.marketplaceUserCSS"));i&&i.remove(),e?((a=document.createElement("style")).classList.add("marketplaceCSS"),a.classList.add("marketplaceUserCSS"),a.innerHTML=e,document.body.appendChild(a)):((s=document.createElement("link")).setAttribute("rel","stylesheet"),s.setAttribute("href","user.css"),s.classList.add("userCSS"),document.body.appendChild(s))}catch(e){console.warn(e)}if(Spicetify.Config.current_theme=t.manifest?.name,t.include?.length)for(const d of t.include){var o=document.createElement("script");let e=d;if(zr(d)){var{user:l,repo:c,branch:u,filePath:h}=Wr(d);if(!(l&&c&&u&&h))return;e=`https://cdn.jsdelivr.${p}/gh/${l}/${c}@${u}/`+h,h.endsWith(".mjs")&&(o.type="module")}o.src=e+"?time="+Date.now(),o.classList.add("marketplaceScript"),document.body.appendChild(o),Gr(d)}}else console.debug("No theme manifest found")};console.log("Loaded Marketplace extension");var r=I(g.installedSnippets,[]).map(e=>I(e));if(r=r,(a=document.querySelector("style.marketplaceSnippets"))&&a.remove(),a=document.createElement("style"),r=r.reduce((e,t)=>`${e}/* ${t.title} - ${t.description} */
${t.code}
`,""),a.innerHTML=r,a.classList.add("marketplaceSnippets"),document.body.appendChild(a),p){window.sessionStorage.setItem("marketplace-request-tld",p);for(const s of I(g.installedExtensions,[]))(e=>{e=I(e);if(e&&e.extensionURL){console.debug("Initializing extension: ",e);var t=document.createElement("script");if(t.defer=!0,t.src=e.extensionURL,zr(t.src)){var{user:r,repo:a,branch:s,filePath:n}=Wr(e.extensionURL);if(!(r&&a&&s&&n))return;t.src=`https://cdn.jsdelivr.${p}/gh/${r}/${a}@${s}/`+n,n.endsWith(".mjs")&&(t.type="module")}t.src=t.src+"?time="+Date.now(),document.body.appendChild(t),Gr(e.manifest?.main)}})(s);var r=Spicetify.Config["current_theme"],a=(localStorage.setItem(g.localTheme,r),localStorage.getItem(g.themeInstalled));a&&("marketplace"!==r.toLocaleLowerCase()?Spicetify.showNotification(Ne("notifications.wrongLocalTheme"),!0,5e3):t(a))}else window.navigator.onLine?(console.error(new Error("Unable to connect to the CDN, please check your Internet configuration.")),Spicetify.showNotification(Ne("notifications.noCdnConnection"),!0,5e3)):window.addEventListener("online",e,{once:!0})}else setTimeout(e,100)}(),async function(){console.debug("Preloading extensions and themes..."),window.sessionStorage.clear();var e=await(await fetch("https://raw.githubusercontent.com/spicetify/marketplace/main/resources/blacklist.json").then(e=>e.json()).catch(()=>({}))).repos;window.sessionStorage.setItem("marketplace:blacklist",JSON.stringify(e)),await Promise.all([Qr("extension",0),Qr("theme",0),Qr("app",0)])}()})()}();