var marketplace=(()=>{var E,k=Object.create,T=Object.defineProperty,O=Object.getOwnPropertyDescriptor,L=Object.getOwnPropertyNames,D=Object.getPrototypeOf,R=Object.prototype.hasOwnProperty,e=(e,t)=>function(){return t||(0,e[L(e)[0]])((t={exports:{}}).exports,t),t.exports},M=(t,r,a,n)=>{if(r&&"object"==typeof r||"function"==typeof r)for(let e of L(r))R.call(t,e)||e===a||T(t,e,{get:()=>r[e],enumerable:!(n=O(r,e))||n.enumerable});return t},t=(e,t,r)=>(r=null!=e?k(D(e)):{},M(!t&&e&&e.__esModule?r:T(r,"default",{value:e,enumerable:!0}),e)),r=e({"external-global-plugin:react"(e,t){t.exports=Spicetify.React}}),_=e({"node_modules/.pnpm/void-elements@3.1.0/node_modules/void-elements/index.js"(e,t){t.exports={area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0}}}),G=e({"node_modules/.pnpm/semver@7.7.0/node_modules/semver/internal/constants.js"(e,t){var r=Number.MAX_SAFE_INTEGER||9007199254740991;t.exports={MAX_LENGTH:256,MAX_SAFE_COMPONENT_LENGTH:16,MAX_SAFE_BUILD_LENGTH:250,MAX_SAFE_INTEGER:r,RELEASE_TYPES:["major","premajor","minor","preminor","patch","prepatch","prerelease"],SEMVER_SPEC_VERSION:"2.0.0",FLAG_INCLUDE_PRERELEASE:1,FLAG_LOOSE:2}}}),B=e({"node_modules/.pnpm/semver@7.7.0/node_modules/semver/internal/debug.js"(e,t){var r="object"==typeof process&&process.env&&process.env.NODE_DEBUG&&/\bsemver\b/i.test(process.env.NODE_DEBUG)?(...e)=>console.error("SEMVER",...e):()=>{};t.exports=r}}),V=e({"node_modules/.pnpm/semver@7.7.0/node_modules/semver/internal/re.js"(e,t){var{MAX_SAFE_COMPONENT_LENGTH:r,MAX_SAFE_BUILD_LENGTH:a,MAX_LENGTH:n}=G(),o=B(),i=(e=t.exports={}).re=[],s=e.safeRe=[],l=e.src=[],c=e.t={},d=0,t="[a-zA-Z0-9-]",u=[["\\s",1],["\\d",n],[t,a]],n=(e,t,r)=>{var a=(e=>{for(var[t,r]of u)e=e.split(t+"*").join(`${t}{0,${r}}`).split(t+"+").join(`${t}{1,${r}}`);return e})(t),n=d++;o(e,n,t),c[e]=n,l[n]=t,i[n]=new RegExp(t,r?"g":void 0),s[n]=new RegExp(a,r?"g":void 0)};n("NUMERICIDENTIFIER","0|[1-9]\\d*"),n("NUMERICIDENTIFIERLOOSE","\\d+"),n("NONNUMERICIDENTIFIER",`\\d*[a-zA-Z-]${t}*`),n("MAINVERSION",`(${l[c.NUMERICIDENTIFIER]})\\.(${l[c.NUMERICIDENTIFIER]})\\.(${l[c.NUMERICIDENTIFIER]})`),n("MAINVERSIONLOOSE",`(${l[c.NUMERICIDENTIFIERLOOSE]})\\.(${l[c.NUMERICIDENTIFIERLOOSE]})\\.(${l[c.NUMERICIDENTIFIERLOOSE]})`),n("PRERELEASEIDENTIFIER",`(?:${l[c.NUMERICIDENTIFIER]}|${l[c.NONNUMERICIDENTIFIER]})`),n("PRERELEASEIDENTIFIERLOOSE",`(?:${l[c.NUMERICIDENTIFIERLOOSE]}|${l[c.NONNUMERICIDENTIFIER]})`),n("PRERELEASE",`(?:-(${l[c.PRERELEASEIDENTIFIER]}(?:\\.${l[c.PRERELEASEIDENTIFIER]})*))`),n("PRERELEASELOOSE",`(?:-?(${l[c.PRERELEASEIDENTIFIERLOOSE]}(?:\\.${l[c.PRERELEASEIDENTIFIERLOOSE]})*))`),n("BUILDIDENTIFIER",t+"+"),n("BUILD",`(?:\\+(${l[c.BUILDIDENTIFIER]}(?:\\.${l[c.BUILDIDENTIFIER]})*))`),n("FULLPLAIN",`v?${l[c.MAINVERSION]}${l[c.PRERELEASE]}?${l[c.BUILD]}?`),n("FULL",`^${l[c.FULLPLAIN]}$`),n("LOOSEPLAIN",`[v=\\s]*${l[c.MAINVERSIONLOOSE]}${l[c.PRERELEASELOOSE]}?${l[c.BUILD]}?`),n("LOOSE",`^${l[c.LOOSEPLAIN]}$`),n("GTLT","((?:<|>)?=?)"),n("XRANGEIDENTIFIERLOOSE",l[c.NUMERICIDENTIFIERLOOSE]+"|x|X|\\*"),n("XRANGEIDENTIFIER",l[c.NUMERICIDENTIFIER]+"|x|X|\\*"),n("XRANGEPLAIN",`[v=\\s]*(${l[c.XRANGEIDENTIFIER]})(?:\\.(${l[c.XRANGEIDENTIFIER]})(?:\\.(${l[c.XRANGEIDENTIFIER]})(?:${l[c.PRERELEASE]})?${l[c.BUILD]}?)?)?`),n("XRANGEPLAINLOOSE",`[v=\\s]*(${l[c.XRANGEIDENTIFIERLOOSE]})(?:\\.(${l[c.XRANGEIDENTIFIERLOOSE]})(?:\\.(${l[c.XRANGEIDENTIFIERLOOSE]})(?:${l[c.PRERELEASELOOSE]})?${l[c.BUILD]}?)?)?`),n("XRANGE",`^${l[c.GTLT]}\\s*${l[c.XRANGEPLAIN]}$`),n("XRANGELOOSE",`^${l[c.GTLT]}\\s*${l[c.XRANGEPLAINLOOSE]}$`),n("COERCEPLAIN",`(^|[^\\d])(\\d{1,${r}})(?:\\.(\\d{1,${r}}))?(?:\\.(\\d{1,${r}}))?`),n("COERCE",l[c.COERCEPLAIN]+"(?:$|[^\\d])"),n("COERCEFULL",l[c.COERCEPLAIN]+`(?:${l[c.PRERELEASE]})?(?:${l[c.BUILD]})?(?:$|[^\\d])`),n("COERCERTL",l[c.COERCE],!0),n("COERCERTLFULL",l[c.COERCEFULL],!0),n("LONETILDE","(?:~>?)"),n("TILDETRIM",`(\\s*)${l[c.LONETILDE]}\\s+`,!0),e.tildeTrimReplace="$1~",n("TILDE",`^${l[c.LONETILDE]}${l[c.XRANGEPLAIN]}$`),n("TILDELOOSE",`^${l[c.LONETILDE]}${l[c.XRANGEPLAINLOOSE]}$`),n("LONECARET","(?:\\^)"),n("CARETTRIM",`(\\s*)${l[c.LONECARET]}\\s+`,!0),e.caretTrimReplace="$1^",n("CARET",`^${l[c.LONECARET]}${l[c.XRANGEPLAIN]}$`),n("CARETLOOSE",`^${l[c.LONECARET]}${l[c.XRANGEPLAINLOOSE]}$`),n("COMPARATORLOOSE",`^${l[c.GTLT]}\\s*(${l[c.LOOSEPLAIN]})$|^$`),n("COMPARATOR",`^${l[c.GTLT]}\\s*(${l[c.FULLPLAIN]})$|^$`),n("COMPARATORTRIM",`(\\s*)${l[c.GTLT]}\\s*(${l[c.LOOSEPLAIN]}|${l[c.XRANGEPLAIN]})`,!0),e.comparatorTrimReplace="$1$2$3",n("HYPHENRANGE",`^\\s*(${l[c.XRANGEPLAIN]})\\s+-\\s+(${l[c.XRANGEPLAIN]})\\s*$`),n("HYPHENRANGELOOSE",`^\\s*(${l[c.XRANGEPLAINLOOSE]})\\s+-\\s+(${l[c.XRANGEPLAINLOOSE]})\\s*$`),n("STAR","(<|>)?=?\\s*\\*"),n("GTE0","^\\s*>=\\s*0\\.0\\.0\\s*$"),n("GTE0PRE","^\\s*>=\\s*0\\.0\\.0-0\\s*$")}}),$=e({"node_modules/.pnpm/semver@7.7.0/node_modules/semver/internal/parse-options.js"(e,t){var r=Object.freeze({loose:!0}),a=Object.freeze({});t.exports=e=>e?"object"!=typeof e?r:e:a}}),H=e({"node_modules/.pnpm/semver@7.7.0/node_modules/semver/internal/identifiers.js"(e,t){var n=/^[0-9]+$/,r=(e,t)=>{var r=n.test(e),a=n.test(t);return r&&a&&(e=+e,t=+t),e===t?0:r&&!a||(!a||r)&&e<t?-1:1};t.exports={compareIdentifiers:r,rcompareIdentifiers:(e,t)=>r(t,e)}}}),q=e({"node_modules/.pnpm/semver@7.7.0/node_modules/semver/classes/semver.js"(e,t){var n=B(),{MAX_LENGTH:r,MAX_SAFE_INTEGER:a}=G(),{safeRe:o,t:i}=V(),s=$(),l=H()["compareIdentifiers"],c=class{constructor(e,t){if(t=s(t),e instanceof c){if(e.loose===!!t.loose&&e.includePrerelease===!!t.includePrerelease)return e;e=e.version}else if("string"!=typeof e)throw new TypeError(`Invalid version. Must be a string. Got type "${typeof e}".`);if(e.length>r)throw new TypeError(`version is longer than ${r} characters`);n("SemVer",e,t),this.options=t,this.loose=!!t.loose,this.includePrerelease=!!t.includePrerelease;t=e.trim().match(t.loose?o[i.LOOSE]:o[i.FULL]);if(!t)throw new TypeError("Invalid Version: "+e);if(this.raw=e,this.major=+t[1],this.minor=+t[2],this.patch=+t[3],this.major>a||this.major<0)throw new TypeError("Invalid major version");if(this.minor>a||this.minor<0)throw new TypeError("Invalid minor version");if(this.patch>a||this.patch<0)throw new TypeError("Invalid patch version");t[4]?this.prerelease=t[4].split(".").map(e=>{if(/^[0-9]+$/.test(e)){var t=+e;if(0<=t&&t<a)return t}return e}):this.prerelease=[],this.build=t[5]?t[5].split("."):[],this.format()}format(){return this.version=`${this.major}.${this.minor}.`+this.patch,this.prerelease.length&&(this.version+="-"+this.prerelease.join(".")),this.version}toString(){return this.version}compare(e){if(n("SemVer.compare",this.version,this.options,e),!(e instanceof c)){if("string"==typeof e&&e===this.version)return 0;e=new c(e,this.options)}return e.version===this.version?0:this.compareMain(e)||this.comparePre(e)}compareMain(e){return e instanceof c||(e=new c(e,this.options)),l(this.major,e.major)||l(this.minor,e.minor)||l(this.patch,e.patch)}comparePre(e){if(e instanceof c||(e=new c(e,this.options)),this.prerelease.length&&!e.prerelease.length)return-1;if(!this.prerelease.length&&e.prerelease.length)return 1;if(!this.prerelease.length&&!e.prerelease.length)return 0;let t=0;do{var r=this.prerelease[t],a=e.prerelease[t];if(n("prerelease compare",t,r,a),void 0===r&&void 0===a)return 0;if(void 0===a)return 1;if(void 0===r)return-1;if(r!==a)return l(r,a)}while(++t)}compareBuild(e){e instanceof c||(e=new c(e,this.options));let t=0;do{var r=this.build[t],a=e.build[t];if(n("build compare",t,r,a),void 0===r&&void 0===a)return 0;if(void 0===a)return 1;if(void 0===r)return-1;if(r!==a)return l(r,a)}while(++t)}inc(e,t,r){if(e.startsWith("pre")){if(!t&&!1===r)throw new Error("invalid increment argument: identifier is empty");if(t){var a=("-"+t).match(this.options.loose?o[i.PRERELEASELOOSE]:o[i.PRERELEASE]);if(!a||a[1]!==t)throw new Error("invalid identifier: "+t)}}switch(e){case"premajor":this.prerelease.length=0,this.patch=0,this.minor=0,this.major++,this.inc("pre",t,r);break;case"preminor":this.prerelease.length=0,this.patch=0,this.minor++,this.inc("pre",t,r);break;case"prepatch":this.prerelease.length=0,this.inc("patch",t,r),this.inc("pre",t,r);break;case"prerelease":0===this.prerelease.length&&this.inc("patch",t,r),this.inc("pre",t,r);break;case"release":if(0===this.prerelease.length)throw new Error(`version ${this.raw} is not a prerelease`);this.prerelease.length=0;break;case"major":0===this.minor&&0===this.patch&&0!==this.prerelease.length||this.major++,this.minor=0,this.patch=0,this.prerelease=[];break;case"minor":0===this.patch&&0!==this.prerelease.length||this.minor++,this.patch=0,this.prerelease=[];break;case"patch":0===this.prerelease.length&&this.patch++,this.prerelease=[];break;case"pre":var n=Number(r)?1:0;if(0===this.prerelease.length)this.prerelease=[n];else{let e=this.prerelease.length;for(;0<=--e;)"number"==typeof this.prerelease[e]&&(this.prerelease[e]++,e=-2);if(-1===e){if(t===this.prerelease.join(".")&&!1===r)throw new Error("invalid increment argument: identifier already exists");this.prerelease.push(n)}}if(t){let e=!1===r?[t]:[t,n];(0!==l(this.prerelease[0],t)||isNaN(this.prerelease[1]))&&(this.prerelease=e)}break;default:throw new Error("invalid increment argument: "+e)}return this.raw=this.format(),this.build.length&&(this.raw+="+"+this.build.join(".")),this}};t.exports=c}}),ee=e({"node_modules/.pnpm/semver@7.7.0/node_modules/semver/functions/parse.js"(e,t){var a=q();t.exports=(e,t,r=!1)=>{if(e instanceof a)return e;try{return new a(e,t)}catch(e){if(r)throw e;return null}}}}),te=e({"node_modules/.pnpm/semver@7.7.0/node_modules/semver/functions/valid.js"(e,t){var r=ee();t.exports=(e,t)=>{e=r(e,t);return e?e.version:null}}}),re=e({"node_modules/.pnpm/semver@7.7.0/node_modules/semver/functions/clean.js"(e,t){var r=ee();t.exports=(e,t)=>{e=r(e.trim().replace(/^[=v]+/,""),t);return e?e.version:null}}}),ae=e({"node_modules/.pnpm/semver@7.7.0/node_modules/semver/functions/inc.js"(e,t){var o=q();t.exports=(e,t,r,a,n)=>{"string"==typeof r&&(n=a,a=r,r=void 0);try{return new o(e instanceof o?e.version:e,r).inc(t,a,n).version}catch(e){return null}}}}),ne=e({"node_modules/.pnpm/semver@7.7.0/node_modules/semver/functions/diff.js"(e,t){var o=ee();t.exports=(e,t)=>{var e=o(e,null,!0),t=o(t,null,!0),r=e.compare(t);if(0===r)return null;var r=0<r,a=r?e:t,r=r?t:e,n=!!a.prerelease.length;if(!!r.prerelease.length&&!n){if(!r.patch&&!r.minor)return"major";if(0===r.compareMain(a))return r.minor&&!r.patch?"minor":"patch"}a=n?"pre":"";return e.major!==t.major?a+"major":e.minor!==t.minor?a+"minor":e.patch!==t.patch?a+"patch":"prerelease"}}}),oe=e({"node_modules/.pnpm/semver@7.7.0/node_modules/semver/functions/major.js"(e,t){var r=q();t.exports=(e,t)=>new r(e,t).major}}),ie=e({"node_modules/.pnpm/semver@7.7.0/node_modules/semver/functions/minor.js"(e,t){var r=q();t.exports=(e,t)=>new r(e,t).minor}}),se=e({"node_modules/.pnpm/semver@7.7.0/node_modules/semver/functions/patch.js"(e,t){var r=q();t.exports=(e,t)=>new r(e,t).patch}}),le=e({"node_modules/.pnpm/semver@7.7.0/node_modules/semver/functions/prerelease.js"(e,t){var r=ee();t.exports=(e,t)=>{e=r(e,t);return e&&e.prerelease.length?e.prerelease:null}}}),ce=e({"node_modules/.pnpm/semver@7.7.0/node_modules/semver/functions/compare.js"(e,t){var a=q();t.exports=(e,t,r)=>new a(e,r).compare(new a(t,r))}}),de=e({"node_modules/.pnpm/semver@7.7.0/node_modules/semver/functions/rcompare.js"(e,t){var a=ce();t.exports=(e,t,r)=>a(t,e,r)}}),ue=e({"node_modules/.pnpm/semver@7.7.0/node_modules/semver/functions/compare-loose.js"(e,t){var r=ce();t.exports=(e,t)=>r(e,t,!0)}}),pe=e({"node_modules/.pnpm/semver@7.7.0/node_modules/semver/functions/compare-build.js"(e,t){var a=q();t.exports=(e,t,r)=>{e=new a(e,r),t=new a(t,r);return e.compare(t)||e.compareBuild(t)}}}),me=e({"node_modules/.pnpm/semver@7.7.0/node_modules/semver/functions/sort.js"(e,t){var a=pe();t.exports=(e,r)=>e.sort((e,t)=>a(e,t,r))}}),he=e({"node_modules/.pnpm/semver@7.7.0/node_modules/semver/functions/rsort.js"(e,t){var a=pe();t.exports=(e,r)=>e.sort((e,t)=>a(t,e,r))}}),fe=e({"node_modules/.pnpm/semver@7.7.0/node_modules/semver/functions/gt.js"(e,t){var a=ce();t.exports=(e,t,r)=>0<a(e,t,r)}}),ge=e({"node_modules/.pnpm/semver@7.7.0/node_modules/semver/functions/lt.js"(e,t){var a=ce();t.exports=(e,t,r)=>a(e,t,r)<0}}),be=e({"node_modules/.pnpm/semver@7.7.0/node_modules/semver/functions/eq.js"(e,t){var a=ce();t.exports=(e,t,r)=>0===a(e,t,r)}}),ve=e({"node_modules/.pnpm/semver@7.7.0/node_modules/semver/functions/neq.js"(e,t){var a=ce();t.exports=(e,t,r)=>0!==a(e,t,r)}}),ye=e({"node_modules/.pnpm/semver@7.7.0/node_modules/semver/functions/gte.js"(e,t){var a=ce();t.exports=(e,t,r)=>0<=a(e,t,r)}}),we=e({"node_modules/.pnpm/semver@7.7.0/node_modules/semver/functions/lte.js"(e,t){var a=ce();t.exports=(e,t,r)=>a(e,t,r)<=0}}),Se=e({"node_modules/.pnpm/semver@7.7.0/node_modules/semver/functions/cmp.js"(e,t){var n=be(),o=ve(),i=fe(),s=ye(),l=ge(),c=we();t.exports=(e,t,r,a)=>{switch(t){case"===":return(e="object"==typeof e?e.version:e)===(r="object"==typeof r?r.version:r);case"!==":return(e="object"==typeof e?e.version:e)!==(r="object"==typeof r?r.version:r);case"":case"=":case"==":return n(e,r,a);case"!=":return o(e,r,a);case">":return i(e,r,a);case">=":return s(e,r,a);case"<":return l(e,r,a);case"<=":return c(e,r,a);default:throw new TypeError("Invalid operator: "+t)}}}}),Ee=e({"node_modules/.pnpm/semver@7.7.0/node_modules/semver/functions/coerce.js"(e,t){var d=q(),u=ee(),{safeRe:p,t:m}=V();t.exports=(e,t)=>{if(e instanceof d)return e;if("string"!=typeof(e="number"==typeof e?String(e):e))return null;let r=null;if((t=t||{}).rtl){for(var a,n=t.includePrerelease?p[m.COERCERTLFULL]:p[m.COERCERTL];(a=n.exec(e))&&(!r||r.index+r[0].length!==e.length);)r&&a.index+a[0].length===r.index+r[0].length||(r=a),n.lastIndex=a.index+a[1].length+a[2].length;n.lastIndex=-1}else r=e.match(t.includePrerelease?p[m.COERCEFULL]:p[m.COERCE]);var o,i,s,l,c;return null===r?null:(o=r[2],i=r[3]||"0",s=r[4]||"0",l=t.includePrerelease&&r[5]?"-"+r[5]:"",c=t.includePrerelease&&r[6]?"+"+r[6]:"",u(o+`.${i}.`+s+l+c,t))}}}),z=e({"node_modules/.pnpm/semver@7.7.0/node_modules/semver/internal/lrucache.js"(e,t){t.exports=class{constructor(){this.max=1e3,this.map=new Map}get(e){var t=this.map.get(e);if(void 0!==t)return this.map.delete(e),this.map.set(e,t),t}delete(e){return this.map.delete(e)}set(e,t){var r;return this.delete(e)||void 0===t||(this.map.size>=this.max&&(r=this.map.keys().next().value,this.delete(r)),this.map.set(e,t)),this}}}}),ke=e({"node_modules/.pnpm/semver@7.7.0/node_modules/semver/classes/range.js"(e,t){var a=/\s+/g,n=class{constructor(e,t){if(t=o(t),e instanceof n)return e.loose===!!t.loose&&e.includePrerelease===!!t.includePrerelease?e:new n(e.raw,t);if(e instanceof l)return this.raw=e.value,this.set=[[e]],this.formatted=void 0,this;if(this.options=t,this.loose=!!t.loose,this.includePrerelease=!!t.includePrerelease,this.raw=e.trim().replace(a," "),this.set=this.raw.split("||").map(e=>this.parseRange(e.trim())).filter(e=>e.length),!this.set.length)throw new TypeError("Invalid SemVer Range: "+this.raw);if(1<this.set.length){t=this.set[0];if(this.set=this.set.filter(e=>!b(e[0])),0===this.set.length)this.set=[t];else if(1<this.set.length)for(const r of this.set)if(1===r.length&&i(r[0])){this.set=[r];break}}this.formatted=void 0}get range(){if(void 0===this.formatted){this.formatted="";for(let e=0;e<this.set.length;e++){0<e&&(this.formatted+="||");var t=this.set[e];for(let e=0;e<t.length;e++)0<e&&(this.formatted+=" "),this.formatted+=t[e].toString().trim()}}return this.formatted}format(){return this.range}toString(){return this.range}parseRange(e){var t=((this.options.includePrerelease&&f)|(this.options.loose&&g))+":"+e,r=s.get(t);if(r)return r;var r=this.options.loose,a=r?p[m.HYPHENRANGELOOSE]:p[m.HYPHENRANGE];e=e.replace(a,T(this.options.includePrerelease)),u("hyphen replace",e),e=e.replace(p[m.COMPARATORTRIM],c),u("comparator trim",e),e=e.replace(p[m.TILDETRIM],d),u("tilde trim",e),e=e.replace(p[m.CARETTRIM],h),u("caret trim",e);let n=e.split(" ").map(e=>y(e,this.options)).join(" ").split(/\s+/).map(e=>A(e,this.options));r&&(n=n.filter(e=>(u("loose invalid filter",e,this.options),!!e.match(p[m.COMPARATORLOOSE])))),u("range list",n);var o=new Map;for(const i of n.map(e=>new l(e,this.options))){if(b(i))return[i];o.set(i.value,i)}1<o.size&&o.has("")&&o.delete("");a=[...o.values()];return s.set(t,a),a}intersects(e,r){if(e instanceof n)return this.set.some(t=>v(t,r)&&e.set.some(e=>v(e,r)&&t.every(t=>e.every(e=>t.intersects(e,r)))));throw new TypeError("a Range is required")}test(t){if(t){if("string"==typeof t)try{t=new r(t,this.options)}catch(e){return!1}for(let e=0;e<this.set.length;e++)if(O(this.set[e],t,this.options))return!0}return!1}};t.exports=n;var s=new(z()),o=$(),l=Ce(),u=B(),r=q(),{safeRe:p,t:m,comparatorTrimReplace:c,tildeTrimReplace:d,caretTrimReplace:h}=V(),{FLAG_INCLUDE_PRERELEASE:f,FLAG_LOOSE:g}=G(),b=e=>"<0.0.0-0"===e.value,i=e=>""===e.value,v=(e,t)=>{let r=!0;var a=e.slice();let n=a.pop();for(;r&&a.length;)r=a.every(e=>n.intersects(e,t)),n=a.pop();return r},y=(e,t)=>(u("comp",e,t),e=k(e,t),u("caret",e),e=S(e,t),u("tildes",e),e=x(e,t),u("xrange",e),e=N(e,t),u("stars",e),e),w=e=>!e||"x"===e.toLowerCase()||"*"===e,S=(e,t)=>e.trim().split(/\s+/).map(e=>E(e,t)).join(" "),E=(i,e)=>{e=e.loose?p[m.TILDELOOSE]:p[m.TILDE];return i.replace(e,(e,t,r,a,n)=>{u("tilde",i,e,t,r,a,n);let o;return o=w(t)?"":w(r)?`>=${t}.0.0 <${+t+1}.0.0-0`:w(a)?`>=${t}.${r}.0 <${t}.${+r+1}.0-0`:n?(u("replaceTilde pr",n),`>=${t}.${r}.${a}-${n} <${t}.${+r+1}.0-0`):`>=${t}.${r}.${a} <${t}.${+r+1}.0-0`,u("tilde return",o),o})},k=(e,t)=>e.trim().split(/\s+/).map(e=>C(e,t)).join(" "),C=(i,e)=>{u("caret",i,e);var t=e.loose?p[m.CARETLOOSE]:p[m.CARET];const s=e.includePrerelease?"-0":"";return i.replace(t,(e,t,r,a,n)=>{u("caret",i,e,t,r,a,n);let o;return o=w(t)?"":w(r)?`>=${t}.0.0${s} <${+t+1}.0.0-0`:w(a)?"0"===t?`>=${t}.${r}.0${s} <${t}.${+r+1}.0-0`:`>=${t}.${r}.0${s} <${+t+1}.0.0-0`:n?(u("replaceCaret pr",n),"0"===t?"0"===r?`>=${t}.${r}.${a}-${n} <${t}.${r}.${+a+1}-0`:`>=${t}.${r}.${a}-${n} <${t}.${+r+1}.0-0`:`>=${t}.${r}.${a}-${n} <${+t+1}.0.0-0`):(u("no pr"),"0"===t?"0"===r?`>=${t}.${r}.${a}${s} <${t}.${r}.${+a+1}-0`:`>=${t}.${r}.${a}${s} <${t}.${+r+1}.0-0`:`>=${t}.${r}.${a} <${+t+1}.0.0-0`),u("caret return",o),o})},x=(e,t)=>(u("replaceXRanges",e,t),e.split(/\s+/).map(e=>I(e,t)).join(" ")),I=(c,d)=>{c=c.trim();var e=d.loose?p[m.XRANGELOOSE]:p[m.XRANGE];return c.replace(e,(e,t,r,a,n,o)=>{u("xRange",c,e,t,r,a,n,o);var i=w(r),s=i||w(a),l=s||w(n);return"="===t&&l&&(t=""),o=d.includePrerelease?"-0":"",i?e=">"===t||"<"===t?"<0.0.0-0":"*":t&&l?(s&&(a=0),n=0,">"===t?(t=">=",n=s?(r=+r+1,a=0):(a=+a+1,0)):"<="===t&&(t="<",s?r=+r+1:a=+a+1),e=t+r+`.${a}.`+n+(o="<"===t?"-0":o)):s?e=`>=${r}.0.0${o} <${+r+1}.0.0-0`:l&&(e=`>=${r}.${a}.0${o} <${r}.${+a+1}.0-0`),u("xRange return",e),e})},N=(e,t)=>(u("replaceStars",e,t),e.trim().replace(p[m.STAR],"")),A=(e,t)=>(u("replaceGTE0",e,t),e.trim().replace(p[t.includePrerelease?m.GTE0PRE:m.GTE0],"")),T=p=>(e,t,r,a,n,o,i,s,l,c,d,u)=>((t=w(r)?"":w(a)?`>=${r}.0.0`+(p?"-0":""):w(n)?`>=${r}.${a}.0`+(p?"-0":""):o?">="+t:">="+t+(p?"-0":""))+" "+(s=w(l)?"":w(c)?`<${+l+1}.0.0-0`:w(d)?`<${l}.${+c+1}.0-0`:u?`<=${l}.${c}.${d}-`+u:p?`<${l}.${c}.${+d+1}-0`:"<="+s)).trim(),O=(t,r,e)=>{for(let e=0;e<t.length;e++)if(!t[e].test(r))return!1;if(!r.prerelease.length||e.includePrerelease)return!0;for(let e=0;e<t.length;e++)if(u(t[e].semver),t[e].semver!==l.ANY&&0<t[e].semver.prerelease.length){var a=t[e].semver;if(a.major===r.major&&a.minor===r.minor&&a.patch===r.patch)return!0}return!1}}}),Ce=e({"node_modules/.pnpm/semver@7.7.0/node_modules/semver/classes/comparator.js"(e,t){var r=Symbol("SemVer ANY"),a=class{static get ANY(){return r}constructor(e,t){if(t=n(t),e instanceof a){if(e.loose===!!t.loose)return e;e=e.value}e=e.trim().split(/\s+/).join(" "),l("comparator",e,t),this.options=t,this.loose=!!t.loose,this.parse(e),this.semver===r?this.value="":this.value=this.operator+this.semver.version,l("comp",this)}parse(e){var t=this.options.loose?o[i.COMPARATORLOOSE]:o[i.COMPARATOR],t=e.match(t);if(!t)throw new TypeError("Invalid comparator: "+e);this.operator=void 0!==t[1]?t[1]:"","="===this.operator&&(this.operator=""),t[2]?this.semver=new c(t[2],this.options.loose):this.semver=r}toString(){return this.value}test(e){if(l("Comparator.test",e,this.options.loose),this.semver===r||e===r)return!0;if("string"==typeof e)try{e=new c(e,this.options)}catch(e){return!1}return s(e,this.operator,this.semver,this.options)}intersects(e,t){if(e instanceof a)return""===this.operator?""===this.value||new d(e.value,t).test(this.value):""===e.operator?""===e.value||new d(this.value,t).test(e.semver):(!(t=n(t)).includePrerelease||"<0.0.0-0"!==this.value&&"<0.0.0-0"!==e.value)&&!(!t.includePrerelease&&(this.value.startsWith("<0.0.0")||e.value.startsWith("<0.0.0"))||(!this.operator.startsWith(">")||!e.operator.startsWith(">"))&&!(this.operator.startsWith("<")&&e.operator.startsWith("<")||this.semver.version===e.semver.version&&this.operator.includes("=")&&e.operator.includes("=")||s(this.semver,"<",e.semver,t)&&this.operator.startsWith(">")&&e.operator.startsWith("<")||s(this.semver,">",e.semver,t)&&this.operator.startsWith("<")&&e.operator.startsWith(">")));throw new TypeError("a Comparator is required")}},n=(t.exports=a,$()),{safeRe:o,t:i}=V(),s=Se(),l=B(),c=q(),d=ke()}}),xe=e({"node_modules/.pnpm/semver@7.7.0/node_modules/semver/functions/satisfies.js"(e,t){var a=ke();t.exports=(e,t,r)=>{try{t=new a(t,r)}catch(e){return!1}return t.test(e)}}}),Ie=e({"node_modules/.pnpm/semver@7.7.0/node_modules/semver/ranges/to-comparators.js"(e,t){var r=ke();t.exports=(e,t)=>new r(e,t).set.map(e=>e.map(e=>e.value).join(" ").trim().split(" "))}}),Ne=e({"node_modules/.pnpm/semver@7.7.0/node_modules/semver/ranges/max-satisfying.js"(e,t){var i=q(),s=ke();t.exports=(e,t,r)=>{let a=null,n=null,o=null;try{o=new s(t,r)}catch(e){return null}return e.forEach(e=>{!o.test(e)||a&&-1!==n.compare(e)||(a=e,n=new i(a,r))}),a}}}),Ae=e({"node_modules/.pnpm/semver@7.7.0/node_modules/semver/ranges/min-satisfying.js"(e,t){var i=q(),s=ke();t.exports=(e,t,r)=>{let a=null,n=null,o=null;try{o=new s(t,r)}catch(e){return null}return e.forEach(e=>{!o.test(e)||a&&1!==n.compare(e)||(a=e,n=new i(a,r))}),a}}}),Te=e({"node_modules/.pnpm/semver@7.7.0/node_modules/semver/ranges/min-version.js"(e,t){var o=q(),r=ke(),i=fe();t.exports=(t,e)=>{t=new r(t,e);let a=new o("0.0.0");if(t.test(a))return a;if(a=new o("0.0.0-0"),t.test(a))return a;a=null;for(let e=0;e<t.set.length;++e){var n=t.set[e];let r=null;n.forEach(e=>{var t=new o(e.semver.version);switch(e.operator){case">":0===t.prerelease.length?t.patch++:t.prerelease.push(0),t.raw=t.format();case"":case">=":r&&!i(t,r)||(r=t);break;case"<":case"<=":break;default:throw new Error("Unexpected operation: "+e.operator)}}),!r||a&&!i(a,r)||(a=r)}return a&&t.test(a)?a:null}}}),Oe=e({"node_modules/.pnpm/semver@7.7.0/node_modules/semver/ranges/valid.js"(e,t){var r=ke();t.exports=(e,t)=>{try{return new r(e,t).range||"*"}catch(e){return null}}}}),Pe=e({"node_modules/.pnpm/semver@7.7.0/node_modules/semver/ranges/outside.js"(e,t){var r=q(),p=Ce(),m=p["ANY"],h=ke(),f=xe(),g=fe(),b=ge(),v=we(),y=ye();t.exports=(a,n,e,o)=>{a=new r(a,o),n=new h(n,o);let i,s,l,c,d;switch(e){case">":i=g,s=v,l=b,c=">",d=">=";break;case"<":i=b,s=y,l=g,c="<",d="<=";break;default:throw new TypeError('Must provide a hilo val of "<" or ">"')}if(f(a,n,o))return!1;for(let e=0;e<n.set.length;++e){var u=n.set[e];let t=null,r=null;if(u.forEach(e=>{e.semver===m&&(e=new p(">=0.0.0")),t=t||e,r=r||e,i(e.semver,t.semver,o)?t=e:l(e.semver,r.semver,o)&&(r=e)}),t.operator===c||t.operator===d)return!1;if((!r.operator||r.operator===c)&&s(a,r.semver))return!1;if(r.operator===d&&l(a,r.semver))return!1}return!0}}}),Le=e({"node_modules/.pnpm/semver@7.7.0/node_modules/semver/ranges/gtr.js"(e,t){var a=Pe();t.exports=(e,t,r)=>a(e,t,">",r)}}),De=e({"node_modules/.pnpm/semver@7.7.0/node_modules/semver/ranges/ltr.js"(e,t){var a=Pe();t.exports=(e,t,r)=>a(e,t,"<",r)}}),Re=e({"node_modules/.pnpm/semver@7.7.0/node_modules/semver/ranges/intersects.js"(e,t){var a=ke();t.exports=(e,t,r)=>(e=new a(e,r),t=new a(t,r),e.intersects(t,r))}}),Me=e({"node_modules/.pnpm/semver@7.7.0/node_modules/semver/ranges/simplify.js"(e,t){var m=xe(),h=ce();t.exports=(e,t,r)=>{var a=[];let n=null,o=null;var i=e.sort((e,t)=>h(e,t,r));for(const p of i){var s=m(p,t,r);n=s?(o=p,n||p):(o&&a.push([n,o]),o=null)}n&&a.push([n,null]);var l,c,d=[];for([l,c]of a)l===c?d.push(l):c||l!==i[0]?c?l===i[0]?d.push("<="+c):d.push(l+" - "+c):d.push(">="+l):d.push("*");var e=d.join(" || "),u="string"==typeof t.raw?t.raw:String(t);return e.length<u.length?e:t}}}),je=e({"node_modules/.pnpm/semver@7.7.0/node_modules/semver/ranges/subset.js"(e,t){var s=ke(),r=Ce(),b=r["ANY"],v=xe(),y=ce(),w=[new r(">=0.0.0-0")],S=[new r(">=0.0.0")],E=(e,t,r)=>{return!e||!(0<(r=y(e.semver,t.semver,r)))&&(r<0||">"===t.operator&&">="===e.operator)?t:e},k=(e,t,r)=>{return!e||!((r=y(e.semver,t.semver,r))<0)&&(0<r||"<"===t.operator&&"<="===e.operator)?t:e};t.exports=(t,r,a={})=>{if(t!==r){t=new s(t,a),r=new s(r,a);let e=!1;e:for(const o of t.set){for(const i of r.set){var n=((s,l,c)=>{if(s!==l){if(1===s.length&&s[0].semver===b){if(1===l.length&&l[0].semver===b)return!0;s=c.includePrerelease?w:S}if(1===l.length&&l[0].semver===b){if(c.includePrerelease)return!0;l=S}var d=new Set,u,p;let e,t;for(const m of s)">"===m.operator||">="===m.operator?e=E(e,m,c):"<"===m.operator||"<="===m.operator?t=k(t,m,c):d.add(m.semver);if(1<d.size)return null;let r;if(e&&t){if(0<(r=y(e.semver,t.semver,c)))return null;if(0===r&&(">="!==e.operator||"<="!==t.operator))return null}for(const h of d){if(e&&!v(h,String(e),c))return null;if(t&&!v(h,String(t),c))return null;for(const f of l)if(!v(h,String(f),c))return!1;return!0}let a,n,o=!(!t||c.includePrerelease||!t.semver.prerelease.length)&&t.semver,i=!(!e||c.includePrerelease||!e.semver.prerelease.length)&&e.semver;o&&1===o.prerelease.length&&"<"===t.operator&&0===o.prerelease[0]&&(o=!1);for(const g of l){if(n=n||">"===g.operator||">="===g.operator,a=a||"<"===g.operator||"<="===g.operator,e)if(i&&g.semver.prerelease&&g.semver.prerelease.length&&g.semver.major===i.major&&g.semver.minor===i.minor&&g.semver.patch===i.patch&&(i=!1),">"===g.operator||">="===g.operator){if((u=E(e,g,c))===g&&u!==e)return!1}else if(">="===e.operator&&!v(e.semver,String(g),c))return!1;if(t)if(o&&g.semver.prerelease&&g.semver.prerelease.length&&g.semver.major===o.major&&g.semver.minor===o.minor&&g.semver.patch===o.patch&&(o=!1),"<"===g.operator||"<="===g.operator){if((p=k(t,g,c))===g&&p!==t)return!1}else if("<="===t.operator&&!v(t.semver,String(g),c))return!1;if(!g.operator&&(t||e)&&0!==r)return!1}if(e&&a&&!t&&0!==r)return!1;if(t&&n&&!e&&0!==r)return!1;if(i||o)return!1}return true})(o,i,a);if(e=e||null!==n,n)continue e}if(e)return!1}}return!0}}}),a=e({"node_modules/.pnpm/semver@7.7.0/node_modules/semver/index.js"(j,e){var t=V(),r=G(),a=q(),n=H(),o=ee(),i=te(),s=re(),l=ae(),c=ne(),d=oe(),u=ie(),p=se(),m=le(),h=ce(),f=de(),g=ue(),b=pe(),v=me(),y=he(),w=fe(),S=ge(),E=be(),k=ve(),C=ye(),x=we(),I=Se(),N=Ee(),A=Ce(),T=ke(),O=xe(),P=Ie(),L=Ne(),D=Ae(),R=Te(),M=Oe(),_=Pe(),B=Le(),$=De(),z=Re(),F=Me(),U=je();e.exports={parse:o,valid:i,clean:s,inc:l,diff:c,major:d,minor:u,patch:p,prerelease:m,compare:h,rcompare:f,compareLoose:g,compareBuild:b,sort:v,rsort:y,gt:w,lt:S,eq:E,neq:k,gte:C,lte:x,cmp:I,coerce:N,Comparator:A,Range:T,satisfies:O,toComparators:P,maxSatisfying:L,minSatisfying:D,minVersion:R,validRange:M,outside:_,gtr:B,ltr:$,intersects:z,simplifyRange:F,subset:U,SemVer:a,re:t.re,src:t.src,tokens:t.t,SEMVER_SPEC_VERSION:r.SEMVER_SPEC_VERSION,RELEASE_TYPES:r.RELEASE_TYPES,compareIdentifiers:n.compareIdentifiers,rcompareIdentifiers:n.rcompareIdentifiers}}}),F=e({"node_modules/.pnpm/prismjs@1.30.0/node_modules/prismjs/components/prism-core.js"(e,t){var l,r,a,n,O,o="undefined"!=typeof window?window:"undefined"!=typeof WorkerGlobalScope&&self instanceof WorkerGlobalScope?self:{},o=(r=/(?:^|\s)lang(?:uage)?-([\w-]+)(?=\s|$)/i,a=0,n={},O={manual:(l=o).Prism&&l.Prism.manual,disableWorkerMessageHandler:l.Prism&&l.Prism.disableWorkerMessageHandler,util:{encode:function e(t){return t instanceof P?new P(t.type,e(t.content),t.alias):Array.isArray(t)?t.map(e):t.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/\u00a0/g," ")},type:function(e){return Object.prototype.toString.call(e).slice(8,-1)},objId:function(e){return e.__id||Object.defineProperty(e,"__id",{value:++a}),e.__id},clone:function r(e,a){var n,t;switch(a=a||{},O.util.type(e)){case"Object":if(t=O.util.objId(e),a[t])return a[t];for(var o in n={},a[t]=n,e)e.hasOwnProperty(o)&&(n[o]=r(e[o],a));return n;case"Array":return(t=O.util.objId(e),a[t])?a[t]:(n=[],a[t]=n,e.forEach(function(e,t){n[t]=r(e,a)}),n);default:return e}},getLanguage:function(e){for(;e;){var t=r.exec(e.className);if(t)return t[1].toLowerCase();e=e.parentElement}return"none"},setLanguage:function(e,t){e.className=e.className.replace(RegExp(r,"gi"),""),e.classList.add("language-"+t)},currentScript:function(){if("undefined"==typeof document)return null;if(document.currentScript&&"SCRIPT"===document.currentScript.tagName)return document.currentScript;try{throw new Error}catch(e){var t=(/at [^(\r\n]*\((.*):[^:]+:[^:]+\)$/i.exec(e.stack)||[])[1];if(t){var r,a=document.getElementsByTagName("script");for(r in a)if(a[r].src==t)return a[r]}return null}},isActive:function(e,t,r){for(var a="no-"+t;e;){var n=e.classList;if(n.contains(t))return!0;if(n.contains(a))return!1;e=e.parentElement}return!!r}},languages:{plain:n,plaintext:n,text:n,txt:n,extend:function(e,t){var r,a=O.util.clone(O.languages[e]);for(r in t)a[r]=t[r];return a},insertBefore:function(r,e,t,a){var n,o=(a=a||O.languages)[r],i={};for(n in o)if(o.hasOwnProperty(n)){if(n==e)for(var s in t)t.hasOwnProperty(s)&&(i[s]=t[s]);t.hasOwnProperty(n)||(i[n]=o[n])}var l=a[r];return a[r]=i,O.languages.DFS(O.languages,function(e,t){t===l&&e!=r&&(this[e]=i)}),i},DFS:function e(t,r,a,n){n=n||{};var o,i,s,l=O.util.objId;for(o in t)t.hasOwnProperty(o)&&(r.call(t,o,t[o],a||o),i=t[o],"Object"!==(s=O.util.type(i))||n[l(i)]?"Array"!==s||n[l(i)]||(n[l(i)]=!0,e(i,r,o,n)):(n[l(i)]=!0,e(i,r,null,n)))}},plugins:{},highlightAll:function(e,t){O.highlightAllUnder(document,e,t)},highlightAllUnder:function(e,t,r){var a={callback:r,container:e,selector:'code[class*="language-"], [class*="language-"] code, code[class*="lang-"], [class*="lang-"] code'};O.hooks.run("before-highlightall",a),a.elements=Array.prototype.slice.apply(a.container.querySelectorAll(a.selector)),O.hooks.run("before-all-elements-highlight",a);for(var n,o=0;n=a.elements[o++];)O.highlightElement(n,!0===t,a.callback)},highlightElement:function(e,t,r){var a=O.util.getLanguage(e),n=O.languages[a],o=(O.util.setLanguage(e,a),e.parentElement);o&&"pre"===o.nodeName.toLowerCase()&&O.util.setLanguage(o,a);var i={element:e,language:a,grammar:n,code:e.textContent};function s(e){i.highlightedCode=e,O.hooks.run("before-insert",i),i.element.innerHTML=i.highlightedCode,O.hooks.run("after-highlight",i),O.hooks.run("complete",i),r&&r.call(i.element)}O.hooks.run("before-sanity-check",i),(o=i.element.parentElement)&&"pre"===o.nodeName.toLowerCase()&&!o.hasAttribute("tabindex")&&o.setAttribute("tabindex","0"),i.code?(O.hooks.run("before-highlight",i),i.grammar?t&&l.Worker?((a=new Worker(O.filename)).onmessage=function(e){s(e.data)},a.postMessage(JSON.stringify({language:i.language,code:i.code,immediateClose:!0}))):s(O.highlight(i.code,i.grammar,i.language)):s(O.util.encode(i.code))):(O.hooks.run("complete",i),r&&r.call(i.element))},highlight:function(e,t,r){e={code:e,grammar:t,language:r};if(O.hooks.run("before-tokenize",e),e.grammar)return e.tokens=O.tokenize(e.code,e.grammar),O.hooks.run("after-tokenize",e),P.stringify(O.util.encode(e.tokens),e.language);throw new Error('The language "'+e.language+'" has no grammar.')},tokenize:function(e,t){var r=t.rest;if(r){for(var a in r)t[a]=r[a];delete t.rest}for(var n=new c,o=(D(n,n.head,e),!function e(t,r,a,n,o,i){for(var s in a)if(a.hasOwnProperty(s)&&a[s]){var l=a[s];l=Array.isArray(l)?l:[l];for(var c=0;c<l.length;++c){if(i&&i.cause==s+","+c)return;for(var d,u=l[c],p=u.inside,m=!!u.lookbehind,h=!!u.greedy,f=u.alias,g=(h&&!u.pattern.global&&(d=u.pattern.toString().match(/[imsuy]*$/)[0],u.pattern=RegExp(u.pattern.source,d+"g")),u.pattern||u),b=n.next,v=o;b!==r.tail&&!(i&&v>=i.reach);v+=b.value.length,b=b.next){var y=b.value;if(r.length>t.length)return;if(!(y instanceof P)){var w,S=1;if(h){if(!(w=L(g,v,t,m))||w.index>=t.length)break;var E=w.index,k=w.index+w[0].length,C=v;for(C+=b.value.length;C<=E;)b=b.next,C+=b.value.length;if(C-=b.value.length,v=C,b.value instanceof P)continue;for(var x=b;x!==r.tail&&(C<k||"string"==typeof x.value);x=x.next)S++,C+=x.value.length;S--,y=t.slice(v,C),w.index-=v}else if(!(w=L(g,0,y,m)))continue;var E=w.index,I=w[0],N=y.slice(0,E),A=y.slice(E+I.length),y=v+y.length,T=(i&&y>i.reach&&(i.reach=y),b.prev),N=(N&&(T=D(r,T,N),v+=N.length),R(r,T,S),new P(s,p?O.tokenize(I,p):I,f,I));b=D(r,T,N),A&&D(r,b,A),1<S&&(I={cause:s+","+c,reach:y},e(t,r,a,b.prev,v,I),i)&&I.reach>i.reach&&(i.reach=I.reach)}}}}}(e,n,t,n.head,0),n),i=[],s=o.head.next;s!==o.tail;)i.push(s.value),s=s.next;return i},hooks:{all:{},add:function(e,t){var r=O.hooks.all;r[e]=r[e]||[],r[e].push(t)},run:function(e,t){var r=O.hooks.all[e];if(r&&r.length)for(var a,n=0;a=r[n++];)a(t)}},Token:P},l.Prism=O,P.stringify=function t(e,r){if("string"==typeof e)return e;var a;if(Array.isArray(e))return a="",e.forEach(function(e){a+=t(e,r)}),a;var n,o={type:e.type,content:t(e.content,r),tag:"span",classes:["token",e.type],attributes:{},language:r},e=e.alias,i=(e&&(Array.isArray(e)?Array.prototype.push.apply(o.classes,e):o.classes.push(e)),O.hooks.run("wrap",o),"");for(n in o.attributes)i+=" "+n+'="'+(o.attributes[n]||"").replace(/"/g,"&quot;")+'"';return"<"+o.tag+' class="'+o.classes.join(" ")+'"'+i+">"+o.content+"</"+o.tag+">"},l.document?((n=O.util.currentScript())&&(O.filename=n.src,n.hasAttribute("data-manual"))&&(O.manual=!0),O.manual||("loading"===(o=document.readyState)||"interactive"===o&&n&&n.defer?document.addEventListener("DOMContentLoaded",i):window.requestAnimationFrame?window.requestAnimationFrame(i):window.setTimeout(i,16))):l.addEventListener&&!O.disableWorkerMessageHandler&&l.addEventListener("message",function(e){var e=JSON.parse(e.data),t=e.language,r=e.code,e=e.immediateClose;l.postMessage(O.highlight(r,O.languages[t],t)),e&&l.close()},!1),O);function P(e,t,r,a){this.type=e,this.content=t,this.alias=r,this.length=0|(a||"").length}function L(e,t,r,a){e.lastIndex=t;t=e.exec(r);return t&&a&&t[1]&&(e=t[1].length,t.index+=e,t[0]=t[0].slice(e)),t}function c(){var e={value:null,prev:null,next:null},t={value:null,prev:e,next:null};e.next=t,this.head=e,this.tail=t,this.length=0}function D(e,t,r){var a=t.next,r={value:r,prev:t,next:a};return t.next=r,a.prev=r,e.length++,r}function R(e,t,r){for(var a=t.next,n=0;n<r&&a!==e.tail;n++)a=a.next;(t.next=a).prev=t,e.length-=n}function i(){O.manual||O.highlightAll()}void 0!==t&&t.exports&&(t.exports=o),"undefined"!=typeof global&&(global.Prism=o)}}),U=e({"node_modules/.pnpm/react-simple-code-editor@0.14.1_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/react-simple-code-editor/lib/index.js"(e){"use strict";var _=e&&e.__assign||function(){return(_=Object.assign||function(e){for(var t,r=1,a=arguments.length;r<a;r++)for(var n in t=arguments[r])Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n]);return e}).apply(this,arguments)},a=e&&e.__createBinding||(Object.create?function(e,t,r,a){void 0===a&&(a=r);var n=Object.getOwnPropertyDescriptor(t,r);n&&("get"in n?t.__esModule:!n.writable&&!n.configurable)||(n={enumerable:!0,get:function(){return t[r]}}),Object.defineProperty(e,a,n)}:function(e,t,r,a){e[a=void 0===a?r:a]=t[r]}),n=e&&e.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),t=e&&e.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var r in e)"default"!==r&&Object.prototype.hasOwnProperty.call(e,r)&&a(t,e,r);return n(t,e),t},B=e&&e.__rest||function(e,t){var r={};for(n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var a=0,n=Object.getOwnPropertySymbols(e);a<n.length;a++)t.indexOf(n[a])<0&&Object.prototype.propertyIsEnumerable.call(e,n[a])&&(r[n[a]]=e[n[a]]);return r},$=(Object.defineProperty(e,"__esModule",{value:!0}),t(r())),z="undefined"!=typeof window&&"navigator"in window&&/Win/i.test(navigator.platform),F="undefined"!=typeof window&&"navigator"in window&&/(Mac|iPhone|iPod|iPad)/i.test(navigator.platform),U="npm__react-simple-code-editor__textarea",G="\n/**\n * Reset the text fill color so that placeholder is visible\n */\n.".concat(U,":empty {\n  -webkit-text-fill-color: inherit !important;\n}\n\n/**\n * Hack to apply on some CSS on IE10 and IE11\n */\n@media all and (-ms-high-contrast: none), (-ms-high-contrast: active) {\n  /**\n    * IE doesn't support '-webkit-text-fill-color'\n    * So we use 'color: transparent' to make the text transparent on IE\n    * Unlike other browsers, it doesn't affect caret color in IE\n    */\n  .").concat(U," {\n    color: transparent !important;\n  }\n\n  .").concat(U,"::selection {\n    background-color: #accef7 !important;\n    color: transparent !important;\n  }\n}\n"),t=$.forwardRef(function(e,t){function m(e,t){return e.substring(0,t).split("\n")}function h(e){var t=O.current,r=T.current.stack[T.current.offset];r&&t&&(T.current.stack[T.current.offset]=_(_({},r),{selectionStart:t.selectionStart,selectionEnd:t.selectionEnd})),D(e),M(e)}var r=e.autoFocus,a=e.disabled,n=e.form,o=e.highlight,i=e.ignoreTabKey,f=void 0!==i&&i,i=e.insertSpaces,g=void 0===i||i,i=e.maxLength,s=e.minLength,l=e.name,c=e.onBlur,d=e.onClick,u=e.onFocus,b=e.onKeyDown,p=e.onKeyUp,v=e.onValueChange,y=e.padding,y=void 0===y?0:y,w=e.placeholder,S=e.preClassName,E=e.readOnly,k=e.required,C=e.style,x=e.tabSize,I=void 0===x?2:x,x=e.textareaClassName,N=e.textareaId,A=e.value,e=B(e,["autoFocus","disabled","form","highlight","ignoreTabKey","insertSpaces","maxLength","minLength","name","onBlur","onClick","onFocus","onKeyDown","onKeyUp","onValueChange","padding","placeholder","preClassName","readOnly","required","style","tabSize","textareaClassName","textareaId","value"]),T=$.useRef({stack:[],offset:-1}),O=$.useRef(null),P=$.useState(!0),L=P[0],j=P[1],P={paddingTop:"object"==typeof y?y.top:y,paddingRight:"object"==typeof y?y.right:y,paddingBottom:"object"==typeof y?y.bottom:y,paddingLeft:"object"==typeof y?y.left:y},y=o(A),D=$.useCallback(function(e,t){void 0===t&&(t=!1);var r=T.current,a=r.stack,r=r.offset,r=(a.length&&-1<r&&(T.current.stack=a.slice(0,r+1),100<(r=T.current.stack.length))&&(T.current.stack=a.slice(a=r-100,r),T.current.offset=Math.max(T.current.offset-a,0)),Date.now());if(t){var a=T.current.stack[T.current.offset];if(a&&r-a.timestamp<3e3){var t=/[^a-z0-9]([a-z0-9]+)$/i,a=null==(a=m(a.value,a.selectionStart).pop())?void 0:a.match(t),n=null==(n=m(e.value,e.selectionStart).pop())?void 0:n.match(t);if(null!=a&&a[1]&&null!=(t=null==n?void 0:n[1])&&t.startsWith(a[1]))return void(T.current.stack[T.current.offset]=_(_({},e),{timestamp:r}))}}T.current.stack.push(_(_({},e),{timestamp:r})),T.current.offset++},[]),R=$.useCallback(function(){var e,t,r=O.current;r&&(e=r.value,t=r.selectionStart,r=r.selectionEnd,D({value:e,selectionStart:t,selectionEnd:r}))},[D]),M=function(e){var t=O.current;t&&(t.value=e.value,t.selectionStart=e.selectionStart,t.selectionEnd=e.selectionEnd,null!=v)&&v(e.value)};return $.useEffect(function(){R()},[R]),$.useImperativeHandle(t,function(){return{get session(){return{history:T.current}},set session(e){T.current=e.history}}},[]),$.createElement("div",_({},e,{style:_(_({},V.container),C)}),$.createElement("pre",_({className:S,"aria-hidden":"true",style:_(_(_({},V.editor),V.highlight),P)},"string"==typeof y?{dangerouslySetInnerHTML:{__html:y+"<br />"}}:{children:y})),$.createElement("textarea",{ref:function(e){return O.current=e},style:_(_(_({},V.editor),V.textarea),P),className:U+(x?" ".concat(x):""),id:N,value:A,onChange:function(e){var e=e.currentTarget,t=e.value,r=e.selectionStart,e=e.selectionEnd;D({value:t,selectionStart:r,selectionEnd:e},!0),v(t)},onKeyDown:function(e){var t,r,a,n,o,i,s,l,c,d,u,p;b&&(b(e),e.defaultPrevented)||("Escape"===e.key&&e.currentTarget.blur(),d=(r=e.currentTarget).value,t=r.selectionStart,r=r.selectionEnd,a=(g?" ":"\t").repeat(I),"Tab"===e.key&&!f&&L?(e.preventDefault(),e.shiftKey?(n=(c=m(d,t)).length-1,o=m(d,r).length-1,p=d.split("\n").map(function(e,t){return n<=t&&t<=o&&e.startsWith(a)?e.substring(a.length):e}).join("\n"),d!==p&&(u=c[n],h({value:p,selectionStart:null!=u&&u.startsWith(a)?t-a.length:t,selectionEnd:r-(d.length-p.length)}))):t!==r?(i=(c=m(d,t)).length-1,s=m(d,r).length-1,u=c[i],h({value:d.split("\n").map(function(e,t){return i<=t&&t<=s?a+e:e}).join("\n"),selectionStart:u&&/\S/.test(u)?t+a.length:t,selectionEnd:r+a.length*(s-i+1)})):(l=t+a.length,h({value:d.substring(0,t)+a+d.substring(r),selectionStart:l,selectionEnd:l}))):"Backspace"===e.key?(p=t!==r,d.substring(0,t).endsWith(a)&&!p&&(e.preventDefault(),l=t-a.length,h({value:d.substring(0,t-a.length)+d.substring(r),selectionStart:l,selectionEnd:l}))):"Enter"===e.key?t===r&&null!=(u=null==(c=m(d,t).pop())?void 0:c.match(/^\s+/))&&u[0]&&(e.preventDefault(),l=t+(p="\n"+u[0]).length,h({value:d.substring(0,t)+p+d.substring(r),selectionStart:l,selectionEnd:l})):57===e.keyCode||219===e.keyCode||222===e.keyCode||192===e.keyCode?(c=void 0,57===e.keyCode&&e.shiftKey?c=["(",")"]:219===e.keyCode?c=e.shiftKey?["{","}"]:["[","]"]:222===e.keyCode?c=e.shiftKey?['"','"']:["'","'"]:192!==e.keyCode||e.shiftKey||(c=["`","`"]),t!==r&&c&&(e.preventDefault(),h({value:d.substring(0,t)+c[0]+d.substring(t,r)+c[1]+d.substring(r),selectionStart:t,selectionEnd:r+2}))):(F?e.metaKey&&90===e.keyCode:e.ctrlKey&&90===e.keyCode)&&!e.shiftKey&&!e.altKey?(e.preventDefault(),u=T.current,p=u.stack,u=u.offset,(p=p[u-1])&&(M(p),T.current.offset=Math.max(u-1,0))):(F?e.metaKey&&90===e.keyCode&&e.shiftKey:z?e.ctrlKey&&89===e.keyCode:e.ctrlKey&&90===e.keyCode&&e.shiftKey)&&!e.altKey?(e.preventDefault(),l=T.current,c=l.stack,l=l.offset,(d=c[l+1])&&(M(d),T.current.offset=Math.min(l+1,c.length-1))):77!==e.keyCode||!e.ctrlKey||F&&!e.shiftKey||(e.preventDefault(),j(function(e){return!e})))},onClick:d,onKeyUp:p,onFocus:u,onBlur:c,disabled:a,form:n,maxLength:i,minLength:s,name:l,placeholder:w,readOnly:E,required:k,autoFocus:r,autoCapitalize:"off",autoComplete:"off",autoCorrect:"off",spellCheck:!1,"data-gramm":!1}),$.createElement("style",{dangerouslySetInnerHTML:{__html:G}}))}),V={container:{position:"relative",textAlign:"left",boxSizing:"border-box",padding:0,overflow:"hidden"},textarea:{position:"absolute",top:0,left:0,height:"100%",width:"100%",resize:"none",color:"inherit",overflow:"hidden",MozOsxFontSmoothing:"grayscale",WebkitFontSmoothing:"antialiased",WebkitTextFillColor:"transparent"},highlight:{position:"relative",pointerEvents:"none"},editor:{margin:0,border:0,background:"none",boxSizing:"inherit",display:"inherit",fontFamily:"inherit",fontSize:"inherit",fontStyle:"inherit",fontVariantLigatures:"inherit",fontWeight:"inherit",letterSpacing:"inherit",lineHeight:"inherit",tabSize:"inherit",textIndent:"inherit",textRendering:"inherit",textTransform:"inherit",whiteSpace:"pre-wrap",wordBreak:"keep-all",overflowWrap:"break-word"}};e.default=t}}),_e=e({"node_modules/.pnpm/classnames@2.3.2/node_modules/classnames/index.js"(e,t){!function(){"use strict";var i={}.hasOwnProperty;function s(){for(var e=[],t=0;t<arguments.length;t++){var r=arguments[t];if(r){var a,n=typeof r;if("string"==n||"number"==n)e.push(r);else if(Array.isArray(r))r.length&&(a=s.apply(null,r))&&e.push(a);else if("object"==n)if(r.toString===Object.prototype.toString||r.toString.toString().includes("[native code]"))for(var o in r)i.call(r,o)&&r[o]&&e.push(o);else e.push(r.toString())}}return e.join(" ")}void 0!==t&&t.exports?t.exports=s.default=s:"function"==typeof define&&"object"==typeof define.amd&&define.amd?define("classnames",[],function(){return s}):window.classNames=s}()}}),Be=e({"node_modules/.pnpm/react-dropdown@1.11.0_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/react-dropdown/dist/index.js"(e){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var u=function(e){if(e&&e.__esModule)return e;if(null===e||"object"!==s(e)&&"function"!=typeof e)return{default:e};var t=i();if(t&&t.has(e))return t.get(e);var r,a={},n=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(r in e){var o;Object.prototype.hasOwnProperty.call(e,r)&&((o=n?Object.getOwnPropertyDescriptor(e,r):null)&&(o.get||o.set)?Object.defineProperty(a,r,o):a[r]=e[r])}a.default=e,t&&t.set(e,a);return a}(r()),p=(t=_e())&&t.__esModule?t:{default:t};function i(){var e;return"function"!=typeof WeakMap?null:(i=function(){return e},e=new WeakMap)}function s(e){return(s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function l(){return(l=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var r,a=arguments[t];for(r in a)Object.prototype.hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(this,arguments)}function c(t,e){var r,a=Object.keys(t);return Object.getOwnPropertySymbols&&(r=Object.getOwnPropertySymbols(t),e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),a.push.apply(a,r)),a}function m(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function n(e,t){for(var r=0;r<t.length;r++){var a=t[r];a.enumerable=a.enumerable||!1,a.configurable=!0,"value"in a&&(a.writable=!0),Object.defineProperty(e,a.key,a)}}function o(e){return(o=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function d(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function h(e,t){return(h=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e})(e,t)}var f="Select...",t=function(e){var t,r=a;if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");function a(e){var t,r;if(this instanceof a)return t=this,(t=!(r=o(a).call(this,e))||"object"!==s(r)&&"function"!=typeof r?d(t):r).state={selected:t.parseValue(e.value,e.options)||{label:void 0===e.placeholder?f:e.placeholder,value:""},isOpen:!1},t.dropdownRef=(0,u.createRef)(),t.mounted=!0,t.handleDocumentClick=t.handleDocumentClick.bind(d(t)),t.fireChangeEvent=t.fireChangeEvent.bind(d(t)),t;throw new TypeError("Cannot call a class as a function")}return r.prototype=Object.create(e&&e.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),e&&h(r,e),r=a,(e=[{key:"componentDidUpdate",value:function(e){this.props.value!==e.value&&(this.props.value?(e=this.parseValue(this.props.value,this.props.options))!==this.state.selected&&this.setState({selected:e}):this.setState({selected:{label:void 0===this.props.placeholder?f:this.props.placeholder,value:""}}))}},{key:"componentDidMount",value:function(){document.addEventListener("click",this.handleDocumentClick,!1),document.addEventListener("touchend",this.handleDocumentClick,!1)}},{key:"componentWillUnmount",value:function(){this.mounted=!1,document.removeEventListener("click",this.handleDocumentClick,!1),document.removeEventListener("touchend",this.handleDocumentClick,!1)}},{key:"handleMouseDown",value:function(e){this.props.onFocus&&"function"==typeof this.props.onFocus&&this.props.onFocus(this.state.isOpen),"mousedown"===e.type&&0!==e.button||(e.stopPropagation(),e.preventDefault(),this.props.disabled)||this.setState({isOpen:!this.state.isOpen})}},{key:"parseValue",value:function(t,e){var r;if("string"==typeof t)for(var a,n=0,o=e.length;n<o;n++)"group"===e[n].type?(a=e[n].items.filter(function(e){return e.value===t})).length&&(r=a[0]):void 0!==e[n].value&&e[n].value===t&&(r=e[n]);return r||t}},{key:"setValue",value:function(e,t){e={selected:{value:e,label:t},isOpen:!1};this.fireChangeEvent(e),this.setState(e)}},{key:"fireChangeEvent",value:function(e){e.selected!==this.state.selected&&this.props.onChange&&this.props.onChange(e.selected)}},{key:"renderOption",value:function(r){var e=r.value,t=(void 0===e&&(e=r.label||r),r.label||r.value||r),a=e===this.state.selected.value||e===this.state.selected;m(n={},"".concat(this.props.baseClassName,"-option"),!0),m(n,r.className,!!r.className),m(n,"is-selected",a);var n=(0,p.default)(n),o=Object.keys(r.data||{}).reduce(function(e,t){return function(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?c(r,!0).forEach(function(e){m(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):c(r).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}({},e,m({},"data-".concat(t),r.data[t]))},{});return u.default.createElement("div",l({key:e,className:n,onMouseDown:this.setValue.bind(this,e,t),onClick:this.setValue.bind(this,e,t),role:"option","aria-selected":a?"true":"false"},o),t)}},{key:"buildMenu",value:function(){var a=this,e=this.props,t=e.options,n=e.baseClassName,e=t.map(function(e){var t,r;return"group"===e.type?(t=u.default.createElement("div",{className:"".concat(n,"-title")},e.name),r=e.items.map(function(e){return a.renderOption(e)}),u.default.createElement("div",{className:"".concat(n,"-group"),key:e.name,role:"listbox",tabIndex:"-1"},t,r)):a.renderOption(e)});return e.length?e:u.default.createElement("div",{className:"".concat(n,"-noresults")},"No options found")}},{key:"handleDocumentClick",value:function(e){!this.mounted||this.dropdownRef.current.contains(e.target)||this.state.isOpen&&this.setState({isOpen:!1})}},{key:"isValueSelected",value:function(){return"string"==typeof this.state.selected||""!==this.state.selected.value}},{key:"render",value:function(){var e=this.props,t=e.baseClassName,r=e.controlClassName,a=e.placeholderClassName,n=e.menuClassName,o=e.arrowClassName,i=e.arrowClosed,s=e.arrowOpen,e=e.className,l=this.props.disabled?"Dropdown-disabled":"",c="string"==typeof this.state.selected?this.state.selected:this.state.selected.label,e=(0,p.default)((m(d={},"".concat(t,"-root"),!0),m(d,e,!!e),m(d,"is-open",this.state.isOpen),d)),r=(0,p.default)((m(d={},"".concat(t,"-control"),!0),m(d,r,!!r),m(d,l,!!l),d)),d=(0,p.default)((m(l={},"".concat(t,"-placeholder"),!0),m(l,a,!!a),m(l,"is-selected",this.isValueSelected()),l)),l=(0,p.default)((m(a={},"".concat(t,"-menu"),!0),m(a,n,!!n),a)),a=(0,p.default)((m(n={},"".concat(t,"-arrow"),!0),m(n,o,!!o),n)),o=u.default.createElement("div",{className:d},c),n=this.state.isOpen?u.default.createElement("div",{className:l,"aria-expanded":"true"},this.buildMenu()):null;return u.default.createElement("div",{ref:this.dropdownRef,className:e},u.default.createElement("div",{className:r,onMouseDown:this.handleMouseDown.bind(this),onTouchEnd:this.handleMouseDown.bind(this),"aria-haspopup":"listbox"},o,u.default.createElement("div",{className:"".concat(t,"-arrow-wrapper")},s&&i?this.state.isOpen?s:i:u.default.createElement("span",{className:a}))),n)}}])&&n(r.prototype,e),t&&n(r,t),a}(u.Component);t.defaultProps={baseClassName:"Dropdown"},e.default=t}}),$e=e({"node_modules/.pnpm/react-is@16.13.1/node_modules/react-is/cjs/react-is.development.js"(e){"use strict";function t(e){if("object"==typeof e&&null!==e){var t=e.$$typeof;switch(t){case n:var r=e.type;switch(r){case u:case p:case i:case l:case s:case h:return r;default:var a=r&&r.$$typeof;switch(a){case d:case m:case b:case g:case c:return a;default:return t}}case o:return t}}}function r(e){return t(e)===p}var a,n,o,i,s,l,c,d,u,p,m,h,f,g,b,v,y,w,S,E,k,C,x,I,N,A,T,O,P,L,D;a="function"==typeof Symbol&&Symbol.for,n=a?Symbol.for("react.element"):60103,o=a?Symbol.for("react.portal"):60106,i=a?Symbol.for("react.fragment"):60107,s=a?Symbol.for("react.strict_mode"):60108,l=a?Symbol.for("react.profiler"):60114,c=a?Symbol.for("react.provider"):60109,d=a?Symbol.for("react.context"):60110,u=a?Symbol.for("react.async_mode"):60111,p=a?Symbol.for("react.concurrent_mode"):60111,m=a?Symbol.for("react.forward_ref"):60112,h=a?Symbol.for("react.suspense"):60113,f=a?Symbol.for("react.suspense_list"):60120,g=a?Symbol.for("react.memo"):60115,b=a?Symbol.for("react.lazy"):60116,v=a?Symbol.for("react.block"):60121,y=a?Symbol.for("react.fundamental"):60117,w=a?Symbol.for("react.responder"):60118,S=a?Symbol.for("react.scope"):60119,a=p,E=d,k=c,C=n,x=m,I=i,N=b,A=g,T=o,O=l,P=s,L=h,D=!1,e.AsyncMode=u,e.ConcurrentMode=a,e.ContextConsumer=E,e.ContextProvider=k,e.Element=C,e.ForwardRef=x,e.Fragment=I,e.Lazy=N,e.Memo=A,e.Portal=T,e.Profiler=O,e.StrictMode=P,e.Suspense=L,e.isAsyncMode=function(e){return D||(D=!0,console.warn("The ReactIs.isAsyncMode() alias has been deprecated, and will be removed in React 17+. Update your code to use ReactIs.isConcurrentMode() instead. It has the exact same API.")),r(e)||t(e)===u},e.isConcurrentMode=r,e.isContextConsumer=function(e){return t(e)===d},e.isContextProvider=function(e){return t(e)===c},e.isElement=function(e){return"object"==typeof e&&null!==e&&e.$$typeof===n},e.isForwardRef=function(e){return t(e)===m},e.isFragment=function(e){return t(e)===i},e.isLazy=function(e){return t(e)===b},e.isMemo=function(e){return t(e)===g},e.isPortal=function(e){return t(e)===o},e.isProfiler=function(e){return t(e)===l},e.isStrictMode=function(e){return t(e)===s},e.isSuspense=function(e){return t(e)===h},e.isValidElementType=function(e){return"string"==typeof e||"function"==typeof e||e===i||e===p||e===l||e===s||e===h||e===f||"object"==typeof e&&null!==e&&(e.$$typeof===b||e.$$typeof===g||e.$$typeof===c||e.$$typeof===d||e.$$typeof===m||e.$$typeof===y||e.$$typeof===w||e.$$typeof===S||e.$$typeof===v)},e.typeOf=t}}),ze=e({"node_modules/.pnpm/react-is@16.13.1/node_modules/react-is/index.js"(e,t){"use strict";t.exports=$e()}}),Fe=e({"node_modules/.pnpm/object-assign@4.1.1/node_modules/object-assign/index.js"(e,t){"use strict";var l=Object.getOwnPropertySymbols,c=Object.prototype.hasOwnProperty,d=Object.prototype.propertyIsEnumerable;t.exports=function(){try{if(Object.assign){var e=new String("abc");if(e[5]="de","5"!==Object.getOwnPropertyNames(e)[0]){for(var t={},r=0;r<10;r++)t["_"+String.fromCharCode(r)]=r;var a,n=Object.getOwnPropertyNames(t).map(function(e){return t[e]});if("0123456789"===n.join(""))return a={},"abcdefghijklmnopqrst".split("").forEach(function(e){a[e]=e}),"abcdefghijklmnopqrst"===Object.keys(Object.assign({},a)).join("")?1:void 0}}}catch(e){}}()?Object.assign:function(e,t){for(var r,a=function(e){if(null==e)throw new TypeError("Object.assign cannot be called with null or undefined");return Object(e)}(e),n=1;n<arguments.length;n++){for(var o in r=Object(arguments[n]))c.call(r,o)&&(a[o]=r[o]);if(l)for(var i=l(r),s=0;s<i.length;s++)d.call(r,i[s])&&(a[i[s]]=r[i[s]])}return a}}}),Ue=e({"node_modules/.pnpm/prop-types@15.8.1/node_modules/prop-types/lib/ReactPropTypesSecret.js"(e,t){"use strict";t.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"}}),Ge=e({"node_modules/.pnpm/prop-types@15.8.1/node_modules/prop-types/lib/has.js"(e,t){t.exports=Function.call.bind(Object.prototype.hasOwnProperty)}}),Ve=e({"node_modules/.pnpm/prop-types@15.8.1/node_modules/prop-types/checkPropTypes.js"(e,t){"use strict";var l,c,d,u=function(){};function r(e,t,r,a,n){for(var o in e)if(d(e,o)){var i,s;try{if("function"!=typeof e[o])throw(s=Error((a||"React class")+": "+r+" type `"+o+"` is invalid; it must be a function, usually from the `prop-types` package, but received `"+typeof e[o]+"`.This often happens because of typos such as `PropTypes.function` instead of `PropTypes.func`.")).name="Invariant Violation",s;i=e[o](t,o,a,r,null,l)}catch(e){i=e}!i||i instanceof Error||u((a||"React class")+": type specification of "+r+" `"+o+"` is invalid; the type checker function must return `null` or an `Error` but returned a "+typeof i+". You may have forgotten to pass an argument to the type checker creator (arrayOf, instanceOf, objectOf, oneOf, oneOfType, and shape all require an argument)."),i instanceof Error&&!(i.message in c)&&(c[i.message]=!0,o=n?n():"",u("Failed "+r+" type: "+i.message+(null!=o?o:"")))}}l=Ue(),c={},d=Ge(),u=function(e){e="Warning: "+e;"undefined"!=typeof console&&console.error(e);try{throw new Error(e)}catch(e){}},r.resetWarningCache=function(){c={}},t.exports=r}}),He=e({"node_modules/.pnpm/prop-types@15.8.1/node_modules/prop-types/factoryWithTypeCheckers.js"(e,t){"use strict";var c=ze(),g=Fe(),b=Ue(),v=Ge(),a=Ve(),y=function(){};function n(){return null}y=function(e){e="Warning: "+e;"undefined"!=typeof console&&console.error(e);try{throw new Error(e)}catch(e){}},t.exports=function(o,d){var i="function"==typeof Symbol&&Symbol.iterator,s="@@iterator";var u="<<anonymous>>",e={array:t("array"),bigint:t("bigint"),bool:t("boolean"),func:t("function"),number:t("number"),object:t("object"),string:t("string"),symbol:t("symbol"),any:r(n),arrayOf:function(l){return r(function(e,t,r,a,n){if("function"!=typeof l)return new p("Property `"+n+"` of component `"+r+"` has invalid PropType notation inside arrayOf.");var o=e[t];if(!Array.isArray(o))return new p("Invalid "+a+" `"+n+"` of type `"+h(o)+"` supplied to `"+r+"`, expected an array.");for(var i=0;i<o.length;i++){var s=l(o,i,r,a,n+"["+i+"]",b);if(s instanceof Error)return s}return null})},element:r(function(e,t,r,a,n){return e=e[t],o(e)?null:new p("Invalid "+a+" `"+n+"` of type `"+h(e)+"` supplied to `"+r+"`, expected a single ReactElement.")}),elementType:r(function(e,t,r,a,n){return e=e[t],c.isValidElementType(e)?null:new p("Invalid "+a+" `"+n+"` of type `"+h(e)+"` supplied to `"+r+"`, expected a single ReactElement type.")}),instanceOf:function(i){return r(function(e,t,r,a,n){var o;return e[t]instanceof i?null:(o=i.name||u,new p("Invalid "+a+" `"+n+"` of type `"+((a=e[t]).constructor&&a.constructor.name?a.constructor.name:u)+"` supplied to `"+r+"`, expected instance of `"+o+"`."))})},node:r(function(e,t,r,a,n){return l(e[t])?null:new p("Invalid "+a+" `"+n+"` supplied to `"+r+"`, expected a ReactNode.")}),objectOf:function(l){return r(function(e,t,r,a,n){if("function"!=typeof l)return new p("Property `"+n+"` of component `"+r+"` has invalid PropType notation inside objectOf.");var o,i=e[t];if("object"!==(e=h(i)))return new p("Invalid "+a+" `"+n+"` of type `"+e+"` supplied to `"+r+"`, expected an object.");for(o in i)if(v(i,o)){var s=l(i,o,r,a,n+"."+o,b);if(s instanceof Error)return s}return null})},oneOf:function(s){if(Array.isArray(s))return r(function(e,t,r,a,n){for(var o=e[t],i=0;i<s.length;i++)if(function(e,t){return e===t?0!==e||1/e==1/t:e!=e&&t!=t}(o,s[i]))return null;e=JSON.stringify(s,function(e,t){return"symbol"===f(t)?String(t):t});return new p("Invalid "+a+" `"+n+"` of value `"+String(o)+"` supplied to `"+r+"`, expected one of "+e+".")});y(1<arguments.length?"Invalid arguments supplied to oneOf, expected an array, got "+arguments.length+" arguments. A common mistake is to write oneOf(x, y, z) instead of oneOf([x, y, z]).":"Invalid argument supplied to oneOf, expected an array.");return n},oneOfType:function(l){if(!Array.isArray(l))return y("Invalid argument supplied to oneOfType, expected an instance of array."),n;for(var e=0;e<l.length;e++){var t=l[e];if("function"!=typeof t)return y("Invalid argument supplied to oneOfType. Expected an array of check functions, but received "+function(e){var t=f(e);switch(t){case"array":case"object":return"an "+t;case"boolean":case"date":case"regexp":return"a "+t;default:return t}}(t)+" at index "+e+"."),n}return r(function(e,t,r,a,n){for(var o=[],i=0;i<l.length;i++){var s=(0,l[i])(e,t,r,a,n,b);if(null==s)return null;s.data&&v(s.data,"expectedType")&&o.push(s.data.expectedType)}return new p("Invalid "+a+" `"+n+"` supplied to `"+r+"`"+(0<o.length?", expected one of type ["+o.join(", ")+"]":"")+".")})},shape:function(l){return r(function(e,t,r,a,n){var o,i=e[t];if("object"!==(e=h(i)))return new p("Invalid "+a+" `"+n+"` of type `"+e+"` supplied to `"+r+"`, expected `object`.");for(o in l){var s=l[o];if("function"!=typeof s)return m(r,a,n,o,f(s));s=s(i,o,r,a,n+"."+o,b);if(s)return s}return null})},exact:function(c){return r(function(e,t,r,a,n){var o,i=e[t],s=h(i);if("object"!==s)return new p("Invalid "+a+" `"+n+"` of type `"+s+"` supplied to `"+r+"`, expected `object`.");for(o in g({},e[t],c)){var l=c[o];if(v(c,o)&&"function"!=typeof l)return m(r,a,n,o,f(l));if(!l)return new p("Invalid "+a+" `"+n+"` key `"+o+"` supplied to `"+r+"`.\nBad object: "+JSON.stringify(e[t],null,"  ")+"\nValid keys: "+JSON.stringify(Object.keys(c),null,"  "));l=l(i,o,r,a,n+"."+o,b);if(l)return l}return null})}};function p(e,t){this.message=e,this.data=t&&"object"==typeof t?t:{},this.stack=""}function r(s){var l={},c=0;function e(e,t,r,a,n,o,i){if(a=a||u,o=o||r,i!==b){if(d)throw(i=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use `PropTypes.checkPropTypes()` to call them. Read more at http://fb.me/use-check-prop-types")).name="Invariant Violation",i;"undefined"!=typeof console&&!l[i=a+":"+r]&&c<3&&(y("You are manually calling a React.PropTypes validation function for the `"+o+"` prop on `"+a+"`. This is deprecated and will throw in the standalone `prop-types` package. You may be seeing this warning due to a third-party PropTypes library. See https://fb.me/react-warning-dont-call-proptypes for details."),l[i]=!0,c++)}return null==t[r]?e?null===t[r]?new p("The "+n+" `"+o+"` is marked as required in `"+a+"`, but its value is `null`."):new p("The "+n+" `"+o+"` is marked as required in `"+a+"`, but its value is `undefined`."):null:s(t,r,a,n,o)}var t=e.bind(null,!1);return t.isRequired=e.bind(null,!0),t}function t(i){return r(function(e,t,r,a,n,o){return h(e=e[t])!==i?new p("Invalid "+a+" `"+n+"` of type `"+f(e)+"` supplied to `"+r+"`, expected `"+i+"`.",{expectedType:i}):null})}function m(e,t,r,a,n){return new p((e||"React class")+": "+t+" type `"+r+"."+a+"` is invalid; it must be a function, usually from the `prop-types` package, but received `"+n+"`.")}function l(e){switch(typeof e){case"number":case"string":case"undefined":return!0;case"boolean":return!e;case"object":if(Array.isArray(e))return e.every(l);if(null!==e&&!o(e)){var t=function(e){if("function"==typeof(e=e&&(i&&e[i]||e[s])))return e}(e);if(!t)return!1;var r,a=t.call(e);if(t!==e.entries){for(;!(r=a.next()).done;)if(!l(r.value))return!1}else for(;!(r=a.next()).done;){var n=r.value;if(n&&!l(n[1]))return!1}}return!0;default:return!1}}function h(e){var t=typeof e;return Array.isArray(e)?"array":e instanceof RegExp?"object":(e=e,"symbol"===t||e&&("Symbol"===e["@@toStringTag"]||"function"==typeof Symbol&&e instanceof Symbol)?"symbol":t)}function f(e){if(null==e)return""+e;var t=h(e);if("object"===t){if(e instanceof Date)return"date";if(e instanceof RegExp)return"regexp"}return t}return p.prototype=Error.prototype,e.checkPropTypes=a,e.resetWarningCache=a.resetWarningCache,e.PropTypes=e}}}),qe=e({"node_modules/.pnpm/prop-types@15.8.1/node_modules/prop-types/index.js"(e,t){var r=ze();t.exports=He()(r.isElement,!0)}}),We=e({"node_modules/.pnpm/hoist-non-react-statics@3.3.2/node_modules/hoist-non-react-statics/dist/hoist-non-react-statics.cjs.js"(e,t){"use strict";var r=ze(),a={childContextTypes:!0,contextType:!0,contextTypes:!0,defaultProps:!0,displayName:!0,getDefaultProps:!0,getDerivedStateFromError:!0,getDerivedStateFromProps:!0,mixins:!0,propTypes:!0,type:!0},u={name:!0,length:!0,prototype:!0,caller:!0,callee:!0,arguments:!0,arity:!0},n={$$typeof:!0,compare:!0,defaultProps:!0,displayName:!0,propTypes:!0,type:!0},o={};function p(e){return r.isMemo(e)?n:o[e.$$typeof]||a}o[r.ForwardRef]={$$typeof:!0,render:!0,defaultProps:!0,displayName:!0,propTypes:!0},o[r.Memo]=n;var m=Object.defineProperty,h=Object.getOwnPropertyNames,f=Object.getOwnPropertySymbols,g=Object.getOwnPropertyDescriptor,b=Object.getPrototypeOf,v=Object.prototype;t.exports=function e(t,r,a){if("string"!=typeof r){v&&(n=b(r))&&n!==v&&e(t,n,a);for(var n,o=h(r),i=(f&&(o=o.concat(f(r))),p(t)),s=p(r),l=0;l<o.length;++l){var c=o[l];if(!(u[c]||a&&a[c]||s&&s[c]||i&&i[c])){var d=g(r,c);try{m(t,c,d)}catch(e){}}}}return t}}}),Ke=e({"node_modules/.pnpm/react-is@17.0.2/node_modules/react-is/cjs/react-is.development.js"(e){"use strict";function t(e){if("object"==typeof e&&null!==e){var t=e.$$typeof;switch(t){case n:var r=e.type;switch(r){case i:case l:case s:case p:case m:return r;default:var a=r&&r.$$typeof;switch(a){case d:case u:case f:case h:case c:return a;default:return t}}case o:return t}}}var n,o,i,s,l,c,d,u,p,m,h,f,r,a,g,b,v,y,w,S,E,k,C,x,I,N,A,T,O,P;n=60103,o=60106,i=60107,s=60108,l=60114,c=60109,d=60110,u=60112,p=60113,m=60120,h=60115,f=60116,r=60121,a=60122,g=60117,b=60129,v=60131,"function"==typeof Symbol&&Symbol.for&&(n=(y=Symbol.for)("react.element"),o=y("react.portal"),i=y("react.fragment"),s=y("react.strict_mode"),l=y("react.profiler"),c=y("react.provider"),d=y("react.context"),u=y("react.forward_ref"),p=y("react.suspense"),m=y("react.suspense_list"),h=y("react.memo"),f=y("react.lazy"),r=y("react.block"),a=y("react.server.block"),g=y("react.fundamental"),y("react.scope"),y("react.opaque.id"),b=y("react.debug_trace_mode"),y("react.offscreen"),v=y("react.legacy_hidden")),y=d,w=c,S=n,E=u,k=i,C=f,x=h,I=o,N=l,A=s,T=p,P=O=!1,e.ContextConsumer=y,e.ContextProvider=w,e.Element=S,e.ForwardRef=E,e.Fragment=k,e.Lazy=C,e.Memo=x,e.Portal=I,e.Profiler=N,e.StrictMode=A,e.Suspense=T,e.isAsyncMode=function(e){return O||(O=!0,console.warn("The ReactIs.isAsyncMode() alias has been deprecated, and will be removed in React 18+.")),!1},e.isConcurrentMode=function(e){return P||(P=!0,console.warn("The ReactIs.isConcurrentMode() alias has been deprecated, and will be removed in React 18+.")),!1},e.isContextConsumer=function(e){return t(e)===d},e.isContextProvider=function(e){return t(e)===c},e.isElement=function(e){return"object"==typeof e&&null!==e&&e.$$typeof===n},e.isForwardRef=function(e){return t(e)===u},e.isFragment=function(e){return t(e)===i},e.isLazy=function(e){return t(e)===f},e.isMemo=function(e){return t(e)===h},e.isPortal=function(e){return t(e)===o},e.isProfiler=function(e){return t(e)===l},e.isStrictMode=function(e){return t(e)===s},e.isSuspense=function(e){return t(e)===p},e.isValidElementType=function(e){return"string"==typeof e||"function"==typeof e||e===i||e===l||e===b||e===s||e===p||e===m||e===v||"object"==typeof e&&null!==e&&(e.$$typeof===f||e.$$typeof===h||e.$$typeof===c||e.$$typeof===d||e.$$typeof===u||e.$$typeof===g||e.$$typeof===r||e[0]===a)},e.typeOf=t}}),Je=e({"node_modules/.pnpm/react-is@17.0.2/node_modules/react-is/index.js"(e,t){"use strict";t.exports=Ke()}}),e=e({"external-global-plugin:react-dom"(e,t){t.exports=Spicetify.ReactDOM}}),Ye={},Ze=Ye,Xe={default:()=>function(){return sh.default.createElement(ih,null)}};for(E in Xe)T(Ze,E,{get:Xe[E],enumerable:!0});var P=e=>"string"==typeof e,Qe=()=>{let r,a;var e=new Promise((e,t)=>{r=e,a=t});return e.resolve=r,e.reject=a,e},et=e=>null==e?"":""+e,tt=(e,t,r)=>{e.forEach(e=>{t[e]&&(r[e]=t[e])})},rt=/###/g,at=e=>e&&-1<e.indexOf("###")?e.replace(rt,"."):e,nt=e=>!e||P(e),ot=(e,t,r)=>{var a=P(t)?t.split("."):t;let n=0;for(;n<a.length-1;){if(nt(e))return{};var o=at(a[n]);!e[o]&&r&&(e[o]=new r),e=Object.prototype.hasOwnProperty.call(e,o)?e[o]:{},++n}return nt(e)?{}:{obj:e,k:at(a[n])}},it=(a,n,o)=>{var{obj:e,k:t}=ot(a,n,Object);if(void 0!==e||1===n.length)e[t]=o;else{let e=n[n.length-1],t=n.slice(0,n.length-1),r=ot(a,t,Object);for(;void 0===r.obj&&t.length;)e=t[t.length-1]+"."+e,t=t.slice(0,t.length-1),(r=ot(a,t,Object))?.obj&&void 0!==r.obj[r.k+"."+e]&&(r.obj=void 0);r.obj[r.k+"."+e]=o}},st=(e,t,r,a)=>{var{obj:e,k:t}=ot(e,t,Object);e[t]=e[t]||[],e[t].push(r)},lt=(e,t)=>{var{obj:e,k:t}=ot(e,t);if(e&&Object.prototype.hasOwnProperty.call(e,t))return e[t]},ct=(e,t,r)=>{e=lt(e,r);return void 0!==e?e:lt(t,r)},dt=(e,t,r)=>{for(const a in t)"__proto__"!==a&&"constructor"!==a&&(a in e?P(e[a])||e[a]instanceof String||P(t[a])||t[a]instanceof String?r&&(e[a]=t[a]):dt(e[a],t[a],r):e[a]=t[a]);return e},ut=e=>e.replace(/[\-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g,"\\$&"),pt={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;","/":"&#x2F;"},mt=e=>P(e)?e.replace(/[&<>"'\/]/g,e=>pt[e]):e,ht=[" ",",","?","!",";"],ft=new class{constructor(e){this.capacity=e,this.regExpMap=new Map,this.regExpQueue=[]}getRegExp(e){var t=this.regExpMap.get(e);return void 0!==t||(t=new RegExp(e),this.regExpQueue.length===this.capacity&&this.regExpMap.delete(this.regExpQueue.shift()),this.regExpMap.set(e,t),this.regExpQueue.push(e)),t}}(20),gt=(e,t,r)=>{t=t||"",r=r||"";var a=ht.filter(e=>t.indexOf(e)<0&&r.indexOf(e)<0);if(0===a.length)return!0;var n,a=ft.getRegExp(`(${a.map(e=>"?"===e?"\\?":e).join("|")})`);let o=!a.test(e);return o||0<(n=e.indexOf(r))&&!a.test(e.substring(0,n))&&(o=!0),o},bt=(e,t,o=".")=>{if(e){if(e[t])return Object.prototype.hasOwnProperty.call(e,t)?e[t]:void 0;var i=t.split(o);let n=e;for(let a=0;a<i.length;){if(!n||"object"!=typeof n)return;let t,r="";for(let e=a;e<i.length;++e)if(e!==a&&(r+=o),r+=i[e],void 0!==(t=n[r])&&!(-1<["string","number","boolean"].indexOf(typeof t)&&e<i.length-1)){a+=e-a+1;break}n=t}return n}},vt=e=>e?.replace("_","-"),yt={type:"logger",log(e){this.output("log",e)},warn(e){this.output("warn",e)},error(e){this.output("error",e)},output(e,t){console?.[e]?.apply?.(console,t)}},wt=class{constructor(e,t={}){this.init(e,t)}init(e,t={}){this.prefix=t.prefix||"i18next:",this.logger=e||yt,this.options=t,this.debug=t.debug}log(...e){return this.forward(e,"log","",!0)}warn(...e){return this.forward(e,"warn","",!0)}error(...e){return this.forward(e,"error","")}deprecate(...e){return this.forward(e,"warn","WARNING DEPRECATED: ",!0)}forward(e,t,r,a){return a&&!this.debug?null:(P(e[0])&&(e[0]=""+r+this.prefix+" "+e[0]),this.logger[t](e))}create(e){return new wt(this.logger,{prefix:this.prefix+`:${e}:`,...this.options})}clone(e){return(e=e||this.options).prefix=e.prefix||this.prefix,new wt(this.logger,e)}},St=new wt,n=class{constructor(){this.observers={}}on(e,r){return e.split(" ").forEach(e=>{this.observers[e]||(this.observers[e]=new Map);var t=this.observers[e].get(r)||0;this.observers[e].set(r,t+1)}),this}off(e,t){this.observers[e]&&(t?this.observers[e].delete(t):delete this.observers[e])}emit(a,...n){this.observers[a]&&Array.from(this.observers[a].entries()).forEach(([t,r])=>{for(let e=0;e<r;e++)t(...n)}),this.observers["*"]&&Array.from(this.observers["*"].entries()).forEach(([t,r])=>{for(let e=0;e<r;e++)t.apply(t,[a,...n])})}},Et=class extends n{constructor(e,t={ns:["translation"],defaultNS:"translation"}){super(),this.data=e||{},this.options=t,void 0===this.options.keySeparator&&(this.options.keySeparator="."),void 0===this.options.ignoreJSONStructure&&(this.options.ignoreJSONStructure=!0)}addNamespaces(e){this.options.ns.indexOf(e)<0&&this.options.ns.push(e)}removeNamespaces(e){e=this.options.ns.indexOf(e);-1<e&&this.options.ns.splice(e,1)}getResource(e,t,r,a={}){var n=(void 0!==a.keySeparator?a:this.options).keySeparator,a=(void 0!==a.ignoreJSONStructure?a:this.options).ignoreJSONStructure;let o;-1<e.indexOf(".")?o=e.split("."):(o=[e,t],r&&(Array.isArray(r)?o.push(...r):P(r)&&n?o.push(...r.split(n)):o.push(r)));var i=lt(this.data,o);return!i&&!t&&!r&&-1<e.indexOf(".")&&(e=o[0],t=o[1],r=o.slice(2).join(".")),!i&&a&&P(r)?bt(this.data?.[e]?.[t],r,n):i}addResource(e,t,r,a,n={silent:!1}){var o=(void 0!==n.keySeparator?n:this.options).keySeparator;let i=[e,t];r&&(i=i.concat(o?r.split(o):r)),-1<e.indexOf(".")&&(a=t,t=(i=e.split("."))[1]),this.addNamespaces(t),it(this.data,i,a),n.silent||this.emit("added",e,t,r,a)}addResources(e,t,r,a={silent:!1}){for(const n in r)(P(r[n])||Array.isArray(r[n]))&&this.addResource(e,t,n,r[n],{silent:!0});a.silent||this.emit("added",e,t,r)}addResourceBundle(e,t,r,a,n,o={silent:!1,skipCopy:!1}){let i=[e,t],s=(-1<e.indexOf(".")&&(a=r,r=t,t=(i=e.split("."))[1]),this.addNamespaces(t),lt(this.data,i)||{});o.skipCopy||(r=JSON.parse(JSON.stringify(r))),a?dt(s,r,n):s={...s,...r},it(this.data,i,s),o.silent||this.emit("added",e,t,r)}removeResourceBundle(e,t){this.hasResourceBundle(e,t)&&delete this.data[e][t],this.removeNamespaces(t),this.emit("removed",e,t)}hasResourceBundle(e,t){return void 0!==this.getResource(e,t)}getResourceBundle(e,t){return t=t||this.options.defaultNS,this.getResource(e,t)}getDataByLanguage(e){return this.data[e]}hasLanguageSomeTranslations(e){const t=this.getDataByLanguage(e);return!!(t&&Object.keys(t)||[]).find(e=>t[e]&&0<Object.keys(t[e]).length)}toJSON(){return this.data}},kt={processors:{},addPostProcessor(e){this.processors[e.name]=e},handle(e,t,r,a,n){return e.forEach(e=>{t=this.processors[e]?.process(t,r,a,n)??t}),t}},Ct={},xt=e=>!P(e)&&"boolean"!=typeof e&&"number"!=typeof e,It=class extends n{constructor(e,t={}){super(),tt(["resourceStore","languageUtils","pluralResolver","interpolator","backendConnector","i18nFormat","utils"],e,this),this.options=t,void 0===this.options.keySeparator&&(this.options.keySeparator="."),this.logger=St.create("translator")}changeLanguage(e){e&&(this.language=e)}exists(e,t={interpolation:{}}){t={...t};return null!=e&&void 0!==this.resolve(e,t)?.res}extractFromKey(e,t){let r=(void 0!==t.nsSeparator?t:this.options).nsSeparator;void 0===r&&(r=":");var a=(void 0!==t.keySeparator?t:this.options).keySeparator;let n=t.ns||this.options.defaultNS||[];var o=r&&-1<e.indexOf(r),t=!(this.options.userDefinedKeySeparator||t.keySeparator||this.options.userDefinedNsSeparator||t.nsSeparator||gt(e,r,a));if(o&&!t){o=e.match(this.interpolator.nestingRegexp);if(o&&0<o.length)return{key:e,namespaces:P(n)?[n]:n};t=e.split(r);(r!==a||r===a&&-1<this.options.ns.indexOf(t[0]))&&(n=t.shift()),e=t.join(a)}return{key:e,namespaces:P(n)?[n]:n}}translate(r,e,a){let n="object"==typeof e?{...e}:e;if("object"!=typeof n&&this.options.overloadTranslationOptionHandler&&(n=this.options.overloadTranslationOptionHandler(arguments)),n=(n="object"==typeof options?{...n}:n)||{},null==r)return"";Array.isArray(r)||(r=[String(r)]);var e=(void 0!==n.returnDetails?n:this.options).returnDetails,o=(void 0!==n.keySeparator?n:this.options).keySeparator;const{key:i,namespaces:t}=this.extractFromKey(r[r.length-1],n),s=t[t.length-1];let l=(void 0!==n.nsSeparator?n:this.options).nsSeparator;void 0===l&&(l=":");var c=n.lng||this.language,d=n.appendNamespaceToCIMode||this.options.appendNamespaceToCIMode;if("cimode"===c?.toLowerCase())return d?e?{res:""+s+l+i,usedKey:i,exactUsedKey:i,usedLng:c,usedNS:s,usedParams:this.getUsedParamsDetails(n)}:""+s+l+i:e?{res:i,usedKey:i,exactUsedKey:i,usedLng:c,usedNS:s,usedParams:this.getUsedParamsDetails(n)}:i;d=this.resolve(r,n);let u=d?.res;var p=d?.usedKey||i,m=d?.exactUsedKey||i,h=(void 0!==n.joinArrays?n:this.options).joinArrays,f=!this.i18nFormat||this.i18nFormat.handleAsObject,g=void 0!==n.count&&!P(n.count);const b=It.hasDefaultValue(n);var v=g?this.pluralResolver.getSuffix(c,n.count,n):"",y=n.ordinal&&g?this.pluralResolver.getSuffix(c,n.count,{ordinal:!1}):"";const w=g&&!n.ordinal&&0===n.count,S=w&&n[`defaultValue${this.options.pluralSeparator}zero`]||n["defaultValue"+v]||n["defaultValue"+y]||n.defaultValue;let E=u;f&&!u&&b&&(E=S);var v=xt(E),y=Object.prototype.toString.apply(E);if(!(f&&E&&v&&["[object Number]","[object Function]","[object RegExp]"].indexOf(y)<0)||P(h)&&Array.isArray(E))if(f&&P(h)&&Array.isArray(u))u=(u=u.join(h))&&this.extendTranslation(u,r,n,a);else{let e=!1,t=!1;!this.isValidLookup(u)&&b&&(e=!0,u=S),this.isValidLookup(u)||(t=!0,u=i);const N=(n.missingKeyNoValueFallbackToKey||this.options.missingKeyNoValueFallbackToKey)&&t?void 0:u,A=b&&S!==u&&this.options.updateMissing;if(t||e||A){this.logger.log(A?"updateKey":"missingKey",c,s,i,A?S:u),o&&(v=this.resolve(i,{...n,keySeparator:!1}))&&v.res&&this.logger.warn("Seems the loaded translations were in flat JSON format instead of nested. Either set keySeparator: false on init or make sure your translations are published in nested format.");let t=[];var k=this.languageUtils.getFallbackCodes(this.options.fallbackLng,n.lng||this.language);if("fallback"===this.options.saveMissingTo&&k&&k[0])for(let e=0;e<k.length;e++)t.push(k[e]);else"all"===this.options.saveMissingTo?t=this.languageUtils.toResolveHierarchy(n.lng||this.language):t.push(n.lng||this.language);const T=(e,t,r)=>{r=b&&r!==u?r:N;this.options.missingKeyHandler?this.options.missingKeyHandler(e,s,t,r,A,n):this.backendConnector?.saveMissing&&this.backendConnector.saveMissing(e,s,t,r,A,n),this.emit("missingKey",e,s,t,u)};this.options.saveMissing&&(this.options.saveMissingPlurals&&g?t.forEach(t=>{var e=this.pluralResolver.getSuffixes(t,n);w&&n[`defaultValue${this.options.pluralSeparator}zero`]&&e.indexOf(this.options.pluralSeparator+"zero")<0&&e.push(this.options.pluralSeparator+"zero"),e.forEach(e=>{T([t],i+e,n["defaultValue"+e]||S)})}):T(t,i,S))}u=this.extendTranslation(u,r,n,d,a),t&&u===i&&this.options.appendNamespaceToMissingKey&&(u=""+s+l+i),(t||e)&&this.options.parseMissingKeyHandler&&(u=this.options.parseMissingKeyHandler(this.options.appendNamespaceToMissingKey?""+s+l+i:i,e?u:void 0,n))}else{if(!n.returnObjects&&!this.options.returnObjects)return this.options.returnedObjectHandler||this.logger.warn("accessing an object - but returnObjects options is not enabled!"),y=this.options.returnedObjectHandler?this.options.returnedObjectHandler(p,E,{...n,ns:t}):`key '${i} (${this.language})' returned an object instead of string.`,e?(d.res=y,d.usedParams=this.getUsedParamsDetails(n),d):y;if(o){var C,f=Array.isArray(E),x=f?[]:{},I=f?m:p;for(const O in E)Object.prototype.hasOwnProperty.call(E,O)&&(C=""+I+o+O,b&&!u?x[O]=this.translate(C,{...n,defaultValue:xt(S)?S[O]:void 0,joinArrays:!1,ns:t}):x[O]=this.translate(C,{...n,joinArrays:!1,ns:t}),x[O]===C)&&(x[O]=E[O]);u=x}}return e?(d.res=u,d.usedParams=this.getUsedParamsDetails(n),d):u}extendTranslation(r,a,n,o,i){if(this.i18nFormat?.parse)r=this.i18nFormat.parse(r,{...this.options.interpolation.defaultVariables,...n},n.lng||this.language||o.usedLng,o.usedNS,o.usedKey,{resolved:o});else if(!n.skipInterpolation){n.interpolation&&this.interpolator.init({...n,interpolation:{...this.options.interpolation,...n.interpolation}});var s=P(r)&&(void 0!==n?.interpolation?.skipOnVariables?n:this.options).interpolation.skipOnVariables;let e,t=(s&&(l=r.match(this.interpolator.nestingRegexp),e=l&&l.length),n.replace&&!P(n.replace)?n.replace:n);this.options.interpolation.defaultVariables&&(t={...this.options.interpolation.defaultVariables,...t}),r=this.interpolator.interpolate(r,t,n.lng||this.language||o.usedLng,n),s&&(s=(l=r.match(this.interpolator.nestingRegexp))&&l.length,e<s)&&(n.nest=!1),!n.lng&&o&&o.res&&(n.lng=this.language||o.usedLng),!1!==n.nest&&(r=this.interpolator.nest(r,(...e)=>i?.[0]!==e[0]||n.context?this.translate(...e,a):(this.logger.warn(`It seems you are nesting recursively key: ${e[0]} in key: `+a[0]),null),n)),n.interpolation&&this.interpolator.reset()}var l=n.postProcess||this.options.postProcess,s=P(l)?[l]:l;return r=null!=r&&s?.length&&!1!==n.applyPostProcessor?kt.handle(s,r,a,this.options&&this.options.postProcessPassResolved?{i18nResolved:{...o,usedParams:this.getUsedParamsDetails(n)},...n}:n,this):r}resolve(e,u={}){let p,a,m,h,n;return(e=P(e)?[e]:e).forEach(t=>{if(!this.isValidLookup(p)){t=this.extractFromKey(t,u);const s=t.key;a=s;let e=t.namespaces;this.options.fallbackNS&&(e=e.concat(this.options.fallbackNS));const l=void 0!==u.count&&!P(u.count),c=l&&!u.ordinal&&0===u.count,d=void 0!==u.context&&(P(u.context)||"number"==typeof u.context)&&""!==u.context,r=u.lngs||this.languageUtils.toResolveHierarchy(u.lng||this.language,u.fallbackLng);e.forEach(i=>{this.isValidLookup(p)||(n=i,Ct[r[0]+"-"+i]||!this.utils?.hasLoadedNamespace||this.utils?.hasLoadedNamespace(n)||(Ct[r[0]+"-"+i]=!0,this.logger.warn(`key "${a}" for languages "${r.join(", ")}" won't get resolved as namespace "${n}" was not yet loaded`,"This means something IS WRONG in your setup. You access the t function before i18next.init / i18next.loadNamespace / i18next.changeLanguage was done. Wait for the callback or Promise to resolve before accessing it!!!")),r.forEach(t=>{if(!this.isValidLookup(p)){h=t;var e,r=[s];if(this.i18nFormat?.addLookupKeys)this.i18nFormat.addLookupKeys(r,s,t,i,u);else{let e;l&&(e=this.pluralResolver.getSuffix(t,u.count,u));var a,n=this.options.pluralSeparator+"zero",o=this.options.pluralSeparator+"ordinal"+this.options.pluralSeparator;l&&(r.push(s+e),u.ordinal&&0===e.indexOf(o)&&r.push(s+e.replace(o,this.options.pluralSeparator)),c)&&r.push(s+n),d&&(a=""+s+this.options.contextSeparator+u.context,r.push(a),l)&&(r.push(a+e),u.ordinal&&0===e.indexOf(o)&&r.push(a+e.replace(o,this.options.pluralSeparator)),c)&&r.push(a+n)}for(;e=r.pop();)this.isValidLookup(p)||(m=e,p=this.getResource(t,i,e,u))}}))})}}),{res:p,usedKey:a,exactUsedKey:m,usedLng:h,usedNS:n}}isValidLookup(e){return!(void 0===e||!this.options.returnNull&&null===e||!this.options.returnEmptyString&&""===e)}getResource(e,t,r,a={}){return(this.i18nFormat?.getResource?this.i18nFormat:this.resourceStore).getResource(e,t,r,a)}getUsedParamsDetails(e={}){var t=e.replace&&!P(e.replace);let r=t?e.replace:e;if(t&&void 0!==e.count&&(r.count=e.count),this.options.interpolation.defaultVariables&&(r={...this.options.interpolation.defaultVariables,...r}),!t){r={...r};for(const a of["defaultValue","ordinal","context","replace","lng","lngs","fallbackLng","ns","keySeparator","nsSeparator","returnObjects","returnDetails","joinArrays","postProcess","interpolation"])delete r[a]}return r}static hasDefaultValue(e){var t="defaultValue";for(const r in e)if(Object.prototype.hasOwnProperty.call(e,r)&&t===r.substring(0,t.length)&&void 0!==e[r])return!0;return!1}},Nt=class{constructor(e){this.options=e,this.supportedLngs=this.options.supportedLngs||!1,this.logger=St.create("languageUtils")}getScriptPartFromCode(e){return!(e=vt(e))||e.indexOf("-")<0||2===(e=e.split("-")).length||(e.pop(),"x"===e[e.length-1].toLowerCase())?null:this.formatLanguageCode(e.join("-"))}getLanguagePartFromCode(e){return!(e=vt(e))||e.indexOf("-")<0?e:(e=e.split("-"),this.formatLanguageCode(e[0]))}formatLanguageCode(t){if(P(t)&&-1<t.indexOf("-")){let e;try{e=Intl.getCanonicalLocales(t)[0]}catch(e){}return(e=e&&this.options.lowerCaseLng?e.toLowerCase():e)?e:this.options.lowerCaseLng?t.toLowerCase():t}return this.options.cleanCode||this.options.lowerCaseLng?t.toLowerCase():t}isSupportedCode(e){return"languageOnly"!==this.options.load&&!this.options.nonExplicitSupportedLngs||(e=this.getLanguagePartFromCode(e)),!this.supportedLngs||!this.supportedLngs.length||-1<this.supportedLngs.indexOf(e)}getBestMatchFromCodes(e){if(!e)return null;let a;return e.forEach(e=>{a||(e=this.formatLanguageCode(e),this.options.supportedLngs&&!this.isSupportedCode(e))||(a=e)}),!a&&this.options.supportedLngs&&e.forEach(e=>{if(!a){var t=this.getScriptPartFromCode(e);if(this.isSupportedCode(t))return a=t;const r=this.getLanguagePartFromCode(e);if(this.isSupportedCode(r))return a=r;a=this.options.supportedLngs.find(e=>e===r||!(e.indexOf("-")<0&&r.indexOf("-")<0)&&(0<e.indexOf("-")&&r.indexOf("-")<0&&e.substring(0,e.indexOf("-"))===r||0===e.indexOf(r)&&1<r.length)?e:void 0)}}),a=a||this.getFallbackCodes(this.options.fallbackLng)[0]}getFallbackCodes(e,t){if(!e)return[];if("function"==typeof e&&(e=e(t)),P(e)&&(e=[e]),Array.isArray(e))return e;if(!t)return e.default||[];let r=e[t];return(r=(r=(r=(r=r||e[this.getScriptPartFromCode(t)])||e[this.formatLanguageCode(t)])||e[this.getLanguagePartFromCode(t)])||e.default)||[]}toResolveHierarchy(e,t){t=this.getFallbackCodes((!1===t?[]:t)||this.options.fallbackLng||[],e);const r=[],a=e=>{e&&(this.isSupportedCode(e)?r.push(e):this.logger.warn("rejecting language code not found in supportedLngs: "+e))};return P(e)&&(-1<e.indexOf("-")||-1<e.indexOf("_"))?("languageOnly"!==this.options.load&&a(this.formatLanguageCode(e)),"languageOnly"!==this.options.load&&"currentOnly"!==this.options.load&&a(this.getScriptPartFromCode(e)),"currentOnly"!==this.options.load&&a(this.getLanguagePartFromCode(e))):P(e)&&a(this.formatLanguageCode(e)),t.forEach(e=>{r.indexOf(e)<0&&a(this.formatLanguageCode(e))}),r}},At={zero:0,one:1,two:2,few:3,many:4,other:5},Tt={select:e=>1===e?"one":"other",resolvedOptions:()=>({pluralCategories:["one","other"]})},Ot=class{constructor(e,t={}){this.languageUtils=e,this.options=t,this.logger=St.create("pluralResolver"),this.pluralRulesCache={}}addRule(e,t){this.rules[e]=t}clearCache(){this.pluralRulesCache={}}getRule(t,r={}){var a=vt("dev"===t?"en":t),e=r.ordinal?"ordinal":"cardinal",n=JSON.stringify({cleanedCode:a,type:e});if(n in this.pluralRulesCache)return this.pluralRulesCache[n];let o;try{o=new Intl.PluralRules(a,{type:e})}catch(e){if(!Intl)return this.logger.error("No Intl support, please use an Intl polyfill!"),Tt;if(!t.match(/-|_/))return Tt;a=this.languageUtils.getLanguagePartFromCode(t);o=this.getRule(a,r)}return this.pluralRulesCache[n]=o}needsPlural(e,t={}){let r=this.getRule(e,t);return 1<(r=r||this.getRule("dev",t))?.resolvedOptions().pluralCategories.length}getPluralFormsOfKey(e,t,r={}){return this.getSuffixes(e,r).map(e=>""+t+e)}getSuffixes(e,t={}){let r=this.getRule(e,t);return(r=r||this.getRule("dev",t))?r.resolvedOptions().pluralCategories.sort((e,t)=>At[e]-At[t]).map(e=>""+this.options.prepend+(t.ordinal?"ordinal"+this.options.prepend:"")+e):[]}getSuffix(e,t,r={}){var a=this.getRule(e,r);return a?""+this.options.prepend+(r.ordinal?"ordinal"+this.options.prepend:"")+a.select(t):(this.logger.warn("no plural rule found for: "+e),this.getSuffix("dev",t,r))}},Pt=(e,t,r,a=".",n=!0)=>{let o=ct(e,t,r);return o=!o&&n&&P(r)&&void 0===(o=bt(e,r,a))?bt(t,r,a):o},Lt=e=>e.replace(/\$/g,"$$$$"),Dt=class{constructor(e={}){this.logger=St.create("interpolator"),this.options=e,this.format=e?.interpolation?.format||(e=>e),this.init(e)}init(e={}){e.interpolation||(e.interpolation={escapeValue:!0});var{escape:e,escapeValue:t,useRawValueToEscape:r,prefix:a,prefixEscaped:n,suffix:o,suffixEscaped:i,formatSeparator:s,unescapeSuffix:l,unescapePrefix:c,nestingPrefix:d,nestingPrefixEscaped:u,nestingSuffix:p,nestingSuffixEscaped:m,nestingOptionsSeparator:h,maxReplaces:f,alwaysFormat:g}=e.interpolation;this.escape=void 0!==e?e:mt,this.escapeValue=void 0===t||t,this.useRawValueToEscape=void 0!==r&&r,this.prefix=a?ut(a):n||"{{",this.suffix=o?ut(o):i||"}}",this.formatSeparator=s||",",this.unescapePrefix=l?"":c||"-",this.unescapeSuffix=!this.unescapePrefix&&l||"",this.nestingPrefix=d?ut(d):u||ut("$t("),this.nestingSuffix=p?ut(p):m||ut(")"),this.nestingOptionsSeparator=h||",",this.maxReplaces=f||1e3,this.alwaysFormat=void 0!==g&&g,this.resetRegExp()}reset(){this.options&&this.init(this.options)}resetRegExp(){var e=(e,t)=>e?.source===t?(e.lastIndex=0,e):new RegExp(t,"g");this.regexp=e(this.regexp,this.prefix+"(.+?)"+this.suffix),this.regexpUnescape=e(this.regexpUnescape,""+this.prefix+this.unescapePrefix+"(.+?)"+this.unescapeSuffix+this.suffix),this.nestingRegexp=e(this.nestingRegexp,this.nestingPrefix+"(.+?)"+this.nestingSuffix)}interpolate(a,r,n,o){let i,s,l;const c=this.options&&this.options.interpolation&&this.options.interpolation.defaultVariables||{},d=e=>{var t;return e.indexOf(this.formatSeparator)<0?(t=Pt(r,c,e,this.options.keySeparator,this.options.ignoreJSONStructure),this.alwaysFormat?this.format(t,void 0,n,{...o,...r,interpolationkey:e}):t):(e=(t=e.split(this.formatSeparator)).shift().trim(),t=t.join(this.formatSeparator).trim(),this.format(Pt(r,c,e,this.options.keySeparator,this.options.ignoreJSONStructure),t,n,{...o,...r,interpolationkey:e}))},u=(this.resetRegExp(),o?.missingInterpolationHandler||this.options.missingInterpolationHandler),p=(void 0!==o?.interpolation?.skipOnVariables?o:this.options).interpolation.skipOnVariables;return[{regex:this.regexpUnescape,safeValue:e=>Lt(e)},{regex:this.regexp,safeValue:e=>this.escapeValue?Lt(this.escape(e)):Lt(e)}].forEach(e=>{for(l=0;i=e.regex.exec(a);){var t=i[1].trim();if(void 0===(s=d(t)))if("function"==typeof u){var r=u(a,i,o);s=P(r)?r:""}else{if(!o||!Object.prototype.hasOwnProperty.call(o,t)){if(p){s=i[0];continue}this.logger.warn(`missed to pass in variable ${t} for interpolating `+a)}s=""}else P(s)||this.useRawValueToEscape||(s=et(s));r=e.safeValue(s);if(a=a.replace(i[0],r),p?(e.regex.lastIndex+=s.length,e.regex.lastIndex-=i[0].length):e.regex.lastIndex=0,++l>=this.maxReplaces)break}}),a}nest(r,a,n={}){let o,i,s;for(var l,c=(r,e)=>{var a=this.nestingOptionsSeparator;if(!(r.indexOf(a)<0)){var n=r.split(new RegExp(a+"[ ]*{"));let t="{"+n[1];r=n[0];var n=(t=this.interpolate(t,s)).match(/'/g),o=t.match(/"/g);((n?.length??0)%2!=0||o)&&o.length%2==0||(t=t.replace(/'/g,'"'));try{s=JSON.parse(t),e&&(s={...e,...s})}catch(e){return this.logger.warn("failed parsing options string in nesting for key "+r,e),""+r+a+t}s.defaultValue&&-1<s.defaultValue.indexOf(this.prefix)&&delete s.defaultValue}return r};o=this.nestingRegexp.exec(r);){let e=[],t=((s=(s={...n}).replace&&!P(s.replace)?s.replace:s).applyPostProcessor=!1,delete s.defaultValue,!1);if(-1===o[0].indexOf(this.formatSeparator)||/{.*}/.test(o[1])||(l=o[1].split(this.formatSeparator).map(e=>e.trim()),o[1]=l.shift(),e=l,t=!0),(i=a(c.call(this,o[1].trim(),s),s))&&o[0]===r&&!P(i))return i;(i=P(i)?i:et(i))||(this.logger.warn(`missed to resolve ${o[1]} for nesting `+r),i=""),t&&(i=e.reduce((e,t)=>this.format(e,t,n.lng,{...n,interpolationkey:o[1].trim()}),i.trim())),r=r.replace(o[0],i),this.regexp.lastIndex=0}return r}},Rt=e=>{let t=e.toLowerCase().trim();const r={};return-1<e.indexOf("(")&&(e=e.split("("),t=e[0].toLowerCase().trim(),e=e[1].substring(0,e[1].length-1),"currency"===t&&e.indexOf(":")<0?r.currency||(r.currency=e.trim()):"relativetime"===t&&e.indexOf(":")<0?r.range||(r.range=e.trim()):e.split(";").forEach(e=>{var t;e&&([e,...t]=e.split(":"),t=t.join(":").trim().replace(/^'+|'+$/g,""),e=e.trim(),r[e]||(r[e]=t),"false"===t&&(r[e]=!1),"true"===t&&(r[e]=!0),isNaN(t)||(r[e]=parseInt(t,10)))})),{formatName:t,formatOptions:r}},Mt=i=>{const s={};return(e,t,r)=>{let a=r;r&&r.interpolationkey&&r.formatParams&&r.formatParams[r.interpolationkey]&&r[r.interpolationkey]&&(a={...a,[r.interpolationkey]:void 0});var n=t+JSON.stringify(a);let o=s[n];return o||(o=i(vt(t),r),s[n]=o),o(e)}},jt=a=>(e,t,r)=>a(vt(t),r)(e),_t=class{constructor(e={}){this.logger=St.create("formatter"),this.options=e,this.init(e)}init(e,t={interpolation:{}}){this.formatSeparator=t.interpolation.formatSeparator||",";t=t.cacheInBuiltFormats?Mt:jt;this.formats={number:t((e,t)=>{const r=new Intl.NumberFormat(e,{...t});return e=>r.format(e)}),currency:t((e,t)=>{const r=new Intl.NumberFormat(e,{...t,style:"currency"});return e=>r.format(e)}),datetime:t((e,t)=>{const r=new Intl.DateTimeFormat(e,{...t});return e=>r.format(e)}),relativetime:t((e,t)=>{const r=new Intl.RelativeTimeFormat(e,{...t});return e=>r.format(e,t.range||"day")}),list:t((e,t)=>{const r=new Intl.ListFormat(e,{...t});return e=>r.format(e)})}}add(e,t){this.formats[e.toLowerCase().trim()]=t}addCached(e,t){this.formats[e.toLowerCase().trim()]=Mt(t)}format(e,t,i,s={}){var t=t.split(this.formatSeparator),r=(1<t.length&&1<t[0].indexOf("(")&&t[0].indexOf(")")<0&&t.find(e=>-1<e.indexOf(")"))&&(r=t.findIndex(e=>-1<e.indexOf(")")),t[0]=[t[0],...t.splice(1,r)].join(this.formatSeparator)),t.reduce((t,r)=>{var{formatName:r,formatOptions:a}=Rt(r);if(this.formats[r]){let e=t;try{var n=s?.formatParams?.[s.interpolationkey]||{},o=n.locale||n.lng||s.locale||s.lng||i;e=this.formats[r](t,o,{...a,...s,...n})}catch(e){this.logger.warn(e)}return e}return this.logger.warn("there was no format function for "+r),t},e));return r}},Bt=(e,t)=>{void 0!==e.pending[t]&&(delete e.pending[t],e.pendingCount--)},$t=class extends n{constructor(e,t,r,a={}){super(),this.backend=e,this.store=t,this.services=r,this.languageUtils=r.languageUtils,this.options=a,this.logger=St.create("backendConnector"),this.waitingReads=[],this.maxParallelReads=a.maxParallelReads||10,this.readingCalls=0,this.maxRetries=0<=a.maxRetries?a.maxRetries:5,this.retryTimeout=1<=a.retryTimeout?a.retryTimeout:350,this.state={},this.queue=[],this.backend?.init?.(r,a.backend,a)}queueLoad(e,t,n,r){const o={},i={},s={},l={};return e.forEach(r=>{let a=!0;t.forEach(e=>{var t=r+"|"+e;!n.reload&&this.store.hasResourceBundle(r,e)?this.state[t]=2:this.state[t]<0||(1===this.state[t]?void 0===i[t]&&(i[t]=!0):(this.state[t]=1,a=!1,void 0===i[t]&&(i[t]=!0),void 0===o[t]&&(o[t]=!0),void 0===l[e]&&(l[e]=!0)))}),a||(s[r]=!0)}),(Object.keys(o).length||Object.keys(i).length)&&this.queue.push({pending:i,pendingCount:Object.keys(i).length,loaded:{},errors:[],callback:r}),{toLoad:Object.keys(o),pending:Object.keys(i),toLoadLanguages:Object.keys(s),toLoadNamespaces:Object.keys(l)}}loaded(e,t,r){var a=e.split("|");const n=a[0],o=a[1],i=(t&&this.emit("failedLoading",n,o,t),!t&&r&&this.store.addResourceBundle(n,o,r,void 0,void 0,{skipCopy:!0}),this.state[e]=t?-1:2,t&&r&&(this.state[e]=0),{});this.queue.forEach(r=>{st(r.loaded,[n],o),Bt(r,e),t&&r.errors.push(t),0!==r.pendingCount||r.done||(Object.keys(r.loaded).forEach(t=>{i[t]||(i[t]={});var e=r.loaded[t];e.length&&e.forEach(e=>{void 0===i[t][e]&&(i[t][e]=!0)})}),r.done=!0,r.errors.length?r.callback(r.errors):r.callback())}),this.emit("loaded",i),this.queue=this.queue.filter(e=>!e.done)}read(a,n,o,i=0,s=this.retryTimeout,l){if(!a.length)return l(null,{});if(this.readingCalls>=this.maxParallelReads)this.waitingReads.push({lng:a,ns:n,fcName:o,tried:i,wait:s,callback:l});else{this.readingCalls++;const r=(e,t)=>{var r;this.readingCalls--,0<this.waitingReads.length&&(r=this.waitingReads.shift(),this.read(r.lng,r.ns,r.fcName,r.tried,r.wait,r.callback)),e&&t&&i<this.maxRetries?setTimeout(()=>{this.read.call(this,a,n,o,i+1,2*s,l)},s):l(e,t)};var e=this.backend[o].bind(this.backend);if(2!==e.length)return e(a,n,r);try{var t=e(a,n);t&&"function"==typeof t.then?t.then(e=>r(null,e)).catch(r):r(null,t)}catch(e){r(e)}}}prepareLoading(e,t,r={},a){if(!this.backend)return this.logger.warn("No backend was added via i18next.use. Will not load resources."),a&&a();P(e)&&(e=this.languageUtils.toResolveHierarchy(e)),P(t)&&(t=[t]);e=this.queueLoad(e,t,r,a);if(!e.toLoad.length)return e.pending.length||a(),null;e.toLoad.forEach(e=>{this.loadOne(e)})}load(e,t,r){this.prepareLoading(e,t,{},r)}reload(e,t,r){this.prepareLoading(e,t,{reload:!0},r)}loadOne(r,a=""){var e=r.split("|");const n=e[0],o=e[1];this.read(n,o,"read",void 0,void 0,(e,t)=>{e&&this.logger.warn(`${a}loading namespace ${o} for language ${n} failed`,e),!e&&t&&this.logger.log(`${a}loaded namespace ${o} for language `+n,t),this.loaded(r,e,t)})}saveMissing(t,r,a,n,o,i={},s=()=>{}){if(this.services?.utils?.hasLoadedNamespace&&!this.services?.utils?.hasLoadedNamespace(r))this.logger.warn(`did not save key "${a}" as the namespace "${r}" was not yet loaded`,"This means something IS WRONG in your setup. You access the t function before i18next.init / i18next.loadNamespace / i18next.changeLanguage was done. Wait for the callback or Promise to resolve before accessing it!!!");else if(null!=a&&""!==a){if(this.backend?.create){i={...i,isUpdate:o},o=this.backend.create.bind(this.backend);if(o.length<6)try{let e;(e=5===o.length?o(t,r,a,n,i):o(t,r,a,n))&&"function"==typeof e.then?e.then(e=>s(null,e)).catch(s):s(null,e)}catch(e){s(e)}else o(t,r,a,n,s,i)}t&&t[0]&&this.store.addResource(t[0],r,a,n)}}},zt=()=>({debug:!1,initAsync:!0,ns:["translation"],defaultNS:["translation"],fallbackLng:["dev"],fallbackNS:!1,supportedLngs:!1,nonExplicitSupportedLngs:!1,load:"all",preload:!1,simplifyPluralSuffix:!0,keySeparator:".",nsSeparator:":",pluralSeparator:"_",contextSeparator:"_",partialBundledLanguages:!1,saveMissing:!1,updateMissing:!1,saveMissingTo:"fallback",saveMissingPlurals:!0,missingKeyHandler:!1,missingInterpolationHandler:!1,postProcess:!1,postProcessPassResolved:!1,returnNull:!1,returnEmptyString:!0,returnObjects:!1,joinArrays:!1,returnedObjectHandler:!1,parseMissingKeyHandler:!1,appendNamespaceToMissingKey:!1,appendNamespaceToCIMode:!1,overloadTranslationOptionHandler:e=>{let t={};if("object"==typeof e[1]&&(t=e[1]),P(e[1])&&(t.defaultValue=e[1]),P(e[2])&&(t.tDescription=e[2]),"object"==typeof e[2]||"object"==typeof e[3]){const r=e[3]||e[2];Object.keys(r).forEach(e=>{t[e]=r[e]})}return t},interpolation:{escapeValue:!0,format:e=>e,prefix:"{{",suffix:"}}",formatSeparator:",",unescapePrefix:"-",nestingPrefix:"$t(",nestingSuffix:")",nestingOptionsSeparator:",",maxReplaces:1e3,skipOnVariables:!0},cacheInBuiltFormats:!0}),Ft=e=>(P(e.ns)&&(e.ns=[e.ns]),P(e.fallbackLng)&&(e.fallbackLng=[e.fallbackLng]),P(e.fallbackNS)&&(e.fallbackNS=[e.fallbackNS]),e.supportedLngs?.indexOf?.("cimode")<0&&(e.supportedLngs=e.supportedLngs.concat(["cimode"])),"boolean"==typeof e.initImmediate&&(e.initAsync=e.initImmediate),e),Ut=()=>{},Gt=t=>{Object.getOwnPropertyNames(Object.getPrototypeOf(t)).forEach(e=>{"function"==typeof t[e]&&(t[e]=t[e].bind(t))})},Vt=class extends n{constructor(e={},t){if(super(),this.options=Ft(e),this.services={},this.logger=St,this.modules={external:[]},Gt(this),t&&!this.isInitialized&&!e.isClone){if(!this.options.initAsync)return this.init(e,t),this;setTimeout(()=>{this.init(e,t)},0)}}init(t={},r){this.isInitializing=!0,"function"==typeof t&&(r=t,t={}),null==t.defaultNS&&t.ns&&(P(t.ns)?t.defaultNS=t.ns:t.ns.indexOf("translation")<0&&(t.defaultNS=t.ns[0]));var a=zt(),t=(this.options={...a,...this.options,...Ft(t)},this.options.interpolation={...a.interpolation,...this.options.interpolation},void 0!==t.keySeparator&&(this.options.userDefinedKeySeparator=t.keySeparator),void 0!==t.nsSeparator&&(this.options.userDefinedNsSeparator=t.nsSeparator),e=>e?"function"==typeof e?new e:e:null);if(!this.options.isClone){this.modules.logger?St.init(t(this.modules.logger),this.options):St.init(null,this.options);let e;e=this.modules.formatter||_t;var n=new Nt(this.options),o=(this.store=new Et(this.options.resources,this.options),this.services);o.logger=St,o.resourceStore=this.store,o.languageUtils=n,o.pluralResolver=new Ot(n,{prepend:this.options.pluralSeparator,simplifyPluralSuffix:this.options.simplifyPluralSuffix}),!e||this.options.interpolation.format&&this.options.interpolation.format!==a.interpolation.format||(o.formatter=t(e),o.formatter.init(o,this.options),this.options.interpolation.format=o.formatter.format.bind(o.formatter)),o.interpolator=new Dt(this.options),o.utils={hasLoadedNamespace:this.hasLoadedNamespace.bind(this)},o.backendConnector=new $t(t(this.modules.backend),o.resourceStore,o,this.options),o.backendConnector.on("*",(e,...t)=>{this.emit(e,...t)}),this.modules.languageDetector&&(o.languageDetector=t(this.modules.languageDetector),o.languageDetector.init)&&o.languageDetector.init(o,this.options.detection,this.options),this.modules.i18nFormat&&(o.i18nFormat=t(this.modules.i18nFormat),o.i18nFormat.init)&&o.i18nFormat.init(this),this.translator=new It(this.services,this.options),this.translator.on("*",(e,...t)=>{this.emit(e,...t)}),this.modules.external.forEach(e=>{e.init&&e.init(this)})}this.format=this.options.interpolation.format,r=r||Ut,!this.options.fallbackLng||this.services.languageDetector||this.options.lng||0<(n=this.services.languageUtils.getFallbackCodes(this.options.fallbackLng)).length&&"dev"!==n[0]&&(this.options.lng=n[0]),this.services.languageDetector||this.options.lng||this.logger.warn("init: no languageDetector is used and no lng is defined");["getResource","hasResourceBundle","getResourceBundle","getDataByLanguage"].forEach(t=>{this[t]=(...e)=>this.store[t](...e)});["addResource","addResources","addResourceBundle","removeResourceBundle"].forEach(t=>{this[t]=(...e)=>(this.store[t](...e),this)});const i=Qe();a=()=>{var e=(e,t)=>{this.isInitializing=!1,this.isInitialized&&!this.initializedStoreOnce&&this.logger.warn("init: i18next is already initialized. You should call init just once!"),this.isInitialized=!0,this.options.isClone||this.logger.log("initialized",this.options),this.emit("initialized",this.options),i.resolve(t),r(e,t)};if(this.languages&&!this.isInitialized)return e(null,this.t.bind(this));this.changeLanguage(this.options.lng,e)};return this.options.resources||!this.options.initAsync?a():setTimeout(a,0),i}loadResources(e,t=Ut){let r=t;t=P(e)?e:this.language;if("function"==typeof e&&(r=e),!this.options.resources||this.options.partialBundledLanguages){if("cimode"===t?.toLowerCase()&&(!this.options.preload||0===this.options.preload.length))return r();const a=[],n=e=>{e&&"cimode"!==e&&this.services.languageUtils.toResolveHierarchy(e).forEach(e=>{"cimode"!==e&&a.indexOf(e)<0&&a.push(e)})};t?n(t):this.services.languageUtils.getFallbackCodes(this.options.fallbackLng).forEach(e=>n(e)),this.options.preload?.forEach?.(e=>n(e)),this.services.backendConnector.load(a,this.options.ns,e=>{e||this.resolvedLanguage||!this.language||this.setResolvedLanguage(this.language),r(e)})}else r(null)}reloadResources(e,t,r){const a=Qe();return"function"==typeof e&&(r=e,e=void 0),"function"==typeof t&&(r=t,t=void 0),e=e||this.languages,t=t||this.options.ns,r=r||Ut,this.services.backendConnector.reload(e,t,e=>{a.resolve(),r(e)}),a}use(e){if(!e)throw new Error("You are passing an undefined module! Please check the object you are passing to i18next.use()");if(e.type)return"backend"===e.type&&(this.modules.backend=e),("logger"===e.type||e.log&&e.warn&&e.error)&&(this.modules.logger=e),"languageDetector"===e.type&&(this.modules.languageDetector=e),"i18nFormat"===e.type&&(this.modules.i18nFormat=e),"postProcessor"===e.type&&kt.addPostProcessor(e),"formatter"===e.type&&(this.modules.formatter=e),"3rdParty"===e.type&&this.modules.external.push(e),this;throw new Error("You are passing a wrong module! Please check the object you are passing to i18next.use()")}setResolvedLanguage(e){if(e&&this.languages&&!(-1<["cimode","dev"].indexOf(e))){for(let e=0;e<this.languages.length;e++){var t=this.languages[e];if(!(-1<["cimode","dev"].indexOf(t))&&this.store.hasLanguageSomeTranslations(t)){this.resolvedLanguage=t;break}}!this.resolvedLanguage&&this.languages.indexOf(e)<0&&this.store.hasLanguageSomeTranslations(e)&&(this.resolvedLanguage=e,this.languages.unshift(e))}}changeLanguage(a,r){this.isLanguageChangingTo=a;const n=Qe(),o=(this.emit("languageChanging",a),e=>{this.language=e,this.languages=this.services.languageUtils.toResolveHierarchy(e),this.resolvedLanguage=void 0,this.setResolvedLanguage(e)}),i=(e,t)=>{t?this.isLanguageChangingTo===a&&(o(t),this.translator.changeLanguage(t),this.isLanguageChangingTo=void 0,this.emit("languageChanged",t),this.logger.log("languageChanged",t)):this.isLanguageChangingTo=void 0,n.resolve((...e)=>this.t(...e)),r&&r(e,(...e)=>this.t(...e))};var e=e=>{a||e||!this.services.languageDetector||(e=[]);var t=P(e)?e:e&&e[0];const r=this.store.hasLanguageSomeTranslations(t)?t:this.services.languageUtils.getBestMatchFromCodes(P(e)?[e]:e);r&&(this.language||o(r),this.translator.language||this.translator.changeLanguage(r),this.services.languageDetector?.cacheUserLanguage?.(r)),this.loadResources(r,e=>{i(e,r)})};return a||!this.services.languageDetector||this.services.languageDetector.async?!a&&this.services.languageDetector&&this.services.languageDetector.async?0===this.services.languageDetector.detect.length?this.services.languageDetector.detect().then(e):this.services.languageDetector.detect(e):e(a):e(this.services.languageDetector.detect()),n}getFixedT(e,t,i){const s=(e,t,...r)=>{let a;(a="object"!=typeof t?this.options.overloadTranslationOptionHandler([e,t].concat(r)):{...t}).lng=a.lng||s.lng,a.lngs=a.lngs||s.lngs,a.ns=a.ns||s.ns,""!==a.keyPrefix&&(a.keyPrefix=a.keyPrefix||i||s.keyPrefix);const n=this.options.keySeparator||".";let o;return o=a.keyPrefix&&Array.isArray(e)?e.map(e=>""+a.keyPrefix+n+e):a.keyPrefix?""+a.keyPrefix+n+e:e,this.t(o,a)};return P(e)?s.lng=e:s.lngs=e,s.ns=t,s.keyPrefix=i,s}t(...e){return this.translator?.translate(...e)}exists(...e){return this.translator?.exists(...e)}setDefaultNamespace(e){this.options.defaultNS=e}hasLoadedNamespace(e,t={}){if(!this.isInitialized)return this.logger.warn("hasLoadedNamespace: i18next was not initialized",this.languages),!1;if(!this.languages||!this.languages.length)return this.logger.warn("hasLoadedNamespace: i18n.languages were undefined or empty",this.languages),!1;var r=t.lng||this.resolvedLanguage||this.languages[0],a=!!this.options&&this.options.fallbackLng,n=this.languages[this.languages.length-1];if("cimode"===r.toLowerCase())return!0;var o=(e,t)=>{e=this.services.backendConnector.state[e+"|"+t];return-1===e||0===e||2===e};if(t.precheck){t=t.precheck(this,o);if(void 0!==t)return t}return!!this.hasResourceBundle(r,e)||!(this.services.backendConnector.backend&&(!this.options.resources||this.options.partialBundledLanguages)&&(!o(r,e)||a&&!o(n,e)))}loadNamespaces(e,t){const r=Qe();return this.options.ns?((e=P(e)?[e]:e).forEach(e=>{this.options.ns.indexOf(e)<0&&this.options.ns.push(e)}),this.loadResources(e=>{r.resolve(),t&&t(e)}),r):(t&&t(),Promise.resolve())}loadLanguages(e,t){const r=Qe(),a=(P(e)&&(e=[e]),this.options.preload||[]);e=e.filter(e=>a.indexOf(e)<0&&this.services.languageUtils.isSupportedCode(e));return e.length?(this.options.preload=a.concat(e),this.loadResources(e=>{r.resolve(),t&&t(e)}),r):(t&&t(),Promise.resolve())}dir(e){var t;return!(e=e||this.resolvedLanguage||(0<this.languages?.length?this.languages[0]:this.language))||(t=this.services?.languageUtils||new Nt(zt()),-1<["ar","shu","sqr","ssh","xaa","yhd","yud","aao","abh","abv","acm","acq","acw","acx","acy","adf","ads","aeb","aec","afb","ajp","apc","apd","arb","arq","ars","ary","arz","auz","avl","ayh","ayl","ayn","ayp","bbz","pga","he","iw","ps","pbt","pbu","pst","prp","prd","ug","ur","ydd","yds","yih","ji","yi","hbo","men","xmn","fa","jpr","peo","pes","prs","dv","sam","ckb"].indexOf(t.getLanguagePartFromCode(e)))||1<e.toLowerCase().indexOf("-arab")?"rtl":"ltr"}static createInstance(e={},t){return new Vt(e,t)}cloneInstance(e={},t=Ut){var r=e.forkResourceStore,a=(r&&delete e.forkResourceStore,{...this.options,...e,isClone:!0});const n=new Vt(a);void 0===e.debug&&void 0===e.prefix||(n.logger=n.logger.clone(e));return["store","services","language"].forEach(e=>{n[e]=this[e]}),n.services={...this.services},n.services.utils={hasLoadedNamespace:n.hasLoadedNamespace.bind(n)},r&&(e=Object.keys(this.store.data).reduce((r,a)=>(r[a]={...this.store.data[a]},r[a]=Object.keys(r[a]).reduce((e,t)=>(e[t]={...r[a][t]},e),r[a]),r),{}),n.store=new Et(e,a),n.services.resourceStore=n.store),n.translator=new It(n.services,a),n.translator.on("*",(e,...t)=>{n.emit(e,...t)}),n.init(a,t),n.translator.options=a,n.translator.backendConnector.services.utils={hasLoadedNamespace:n.hasLoadedNamespace.bind(n)},n}toJSON(){return{options:this.options,store:this.store,language:this.language,languages:this.languages,resolvedLanguage:this.resolvedLanguage}}},n=Vt.createInstance(),y=(n.createInstance=Vt.createInstance,n.createInstance,n.dir,n.init,n.loadResources,n.reloadResources,n.use,n.changeLanguage,n.getFixedT,n.t),{slice:Ht,forEach:qt}=(n.exists,n.setDefaultNamespace,n.hasLoadedNamespace,n.loadNamespaces,n.loadLanguages,[]);function Wt(r){return qt.call(Ht.call(arguments,1),e=>{if(e)for(const t in e)void 0===r[t]&&(r[t]=e[t])}),r}function Kt(t){if("string"==typeof t)return[/<\s*script.*?>/i,/<\s*\/\s*script\s*>/i,/<\s*img.*?on\w+\s*=/i,/<\s*\w+\s*on\w+\s*=.*?>/i,/javascript\s*:/i,/vbscript\s*:/i,/expression\s*\(/i,/eval\s*\(/i,/alert\s*\(/i,/document\.cookie/i,/document\.write\s*\(/i,/window\.location/i,/innerHTML/i].some(e=>e.test(t))}var Jt=/^[\u0009\u0020-\u007e\u0080-\u00ff]+$/,Yt=function(e,t,r,a){var n=4<arguments.length&&void 0!==arguments[4]?arguments[4]:{path:"/",sameSite:"strict"};r&&(n.expires=new Date,n.expires.setTime(n.expires.getTime()+60*r*1e3)),a&&(n.domain=a),document.cookie=function(e,t){var r=2<arguments.length&&void 0!==arguments[2]?arguments[2]:{path:"/"};let a=e+"="+encodeURIComponent(t);if(0<r.maxAge){var n=+r.maxAge;if(Number.isNaN(n))throw new Error("maxAge should be a Number");a+="; Max-Age="+Math.floor(n)}if(r.domain){if(!Jt.test(r.domain))throw new TypeError("option domain is invalid");a+="; Domain="+r.domain}if(r.path){if(!Jt.test(r.path))throw new TypeError("option path is invalid");a+="; Path="+r.path}if(r.expires){if("function"!=typeof r.expires.toUTCString)throw new TypeError("option expires is invalid");a+="; Expires="+r.expires.toUTCString()}if(r.httpOnly&&(a+="; HttpOnly"),r.secure&&(a+="; Secure"),r.sameSite)switch("string"==typeof r.sameSite?r.sameSite.toLowerCase():r.sameSite){case!0:a+="; SameSite=Strict";break;case"lax":a+="; SameSite=Lax";break;case"strict":a+="; SameSite=Strict";break;case"none":a+="; SameSite=None";break;default:throw new TypeError("option sameSite is invalid")}return r.partitioned&&(a+="; Partitioned"),a}(e,encodeURIComponent(t),n)},Zt=function(e){var r=e+"=",a=document.cookie.split(";");for(let t=0;t<a.length;t++){let e=a[t];for(;" "===e.charAt(0);)e=e.substring(1,e.length);if(0===e.indexOf(r))return e.substring(r.length,e.length)}return null},Xt={name:"cookie",lookup(e){e=e.lookupCookie;if(e&&"undefined"!=typeof document)return Zt(e)||void 0},cacheUserLanguage(e,t){var{lookupCookie:t,cookieMinutes:r,cookieDomain:a,cookieOptions:n}=t;t&&"undefined"!=typeof document&&Yt(t,e,r,a,n)}},Qt={name:"querystring",lookup(e){var t=e["lookupQuerystring"];let r;if("undefined"!=typeof window){let e=window.location["search"];var a=(e=!window.location.search&&-1<window.location.hash?.indexOf("?")?window.location.hash.substring(window.location.hash.indexOf("?")):e).substring(1).split("&");for(let e=0;e<a.length;e++){var n=a[e].indexOf("=");0<n&&a[e].substring(0,n)===t&&(r=a[e].substring(n+1))}}return r}},er=null,tr=()=>{if(null===er)try{if(!(er="undefined"!=typeof window&&null!==window.localStorage))return!1;var e="i18next.translate.boo";window.localStorage.setItem(e,"foo"),window.localStorage.removeItem(e)}catch(e){er=!1}return er},rr={name:"localStorage",lookup(e){e=e.lookupLocalStorage;if(e&&tr())return window.localStorage.getItem(e)||void 0},cacheUserLanguage(e,t){t=t.lookupLocalStorage;t&&tr()&&window.localStorage.setItem(t,e)}},ar=null,nr=()=>{if(null===ar)try{if(!(ar="undefined"!=typeof window&&null!==window.sessionStorage))return!1;var e="i18next.translate.boo";window.sessionStorage.setItem(e,"foo"),window.sessionStorage.removeItem(e)}catch(e){ar=!1}return ar},or={name:"sessionStorage",lookup(e){e=e.lookupSessionStorage;if(e&&nr())return window.sessionStorage.getItem(e)||void 0},cacheUserLanguage(e,t){t=t.lookupSessionStorage;t&&nr()&&window.sessionStorage.setItem(t,e)}},ir={name:"navigator",lookup(e){var t=[];if("undefined"!=typeof navigator){var{languages:r,userLanguage:a,language:n}=navigator;if(r)for(let e=0;e<r.length;e++)t.push(r[e]);a&&t.push(a),n&&t.push(n)}return 0<t.length?t:void 0}},sr={name:"htmlTag",lookup(e){e=e.htmlTag;let t;e=e||("undefined"!=typeof document?document.documentElement:null);return t=e&&"function"==typeof e.getAttribute?e.getAttribute("lang"):t}},lr={name:"path",lookup(e){e=e.lookupFromPathIndex;if("undefined"!=typeof window){var t=window.location.pathname.match(/\/([a-zA-Z-]*)/g);if(Array.isArray(t))return t["number"==typeof e?e:0]?.replace("/","")}}},cr={name:"subdomain",lookup(e){var e=e["lookupFromSubdomainIndex"],e="number"==typeof e?e+1:1,t="undefined"!=typeof window&&window.location?.hostname?.match(/^(\w{2,5})\.(([a-z0-9-]{1,63}\.[a-z]{2,6})|localhost)/i);if(t)return t[e]}},dr=!1;try{document.cookie,dr=!0}catch(e){}var ur,pr,mr=["querystring","cookie","localStorage","sessionStorage","navigator","htmlTag"],hr=(dr||mr.splice(1,1),()=>({order:mr,lookupQuerystring:"lng",lookupCookie:"i18next",lookupLocalStorage:"i18nextLng",lookupSessionStorage:"i18nextLng",caches:["localStorage"],excludeCacheFor:["cimode"],convertDetectedLanguage:e=>e})),dr=class{constructor(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{};this.type="languageDetector",this.detectors={},this.init(e,t)}init(){var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:{languageUtils:{}},t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{},r=2<arguments.length&&void 0!==arguments[2]?arguments[2]:{};this.services=e,this.options=Wt(t,this.options||{},hr()),"string"==typeof this.options.convertDetectedLanguage&&-1<this.options.convertDetectedLanguage.indexOf("15897")&&(this.options.convertDetectedLanguage=e=>e.replace("-","_")),this.options.lookupFromUrlIndex&&(this.options.lookupFromPathIndex=this.options.lookupFromUrlIndex),this.i18nOptions=r,this.addDetector(Xt),this.addDetector(Qt),this.addDetector(rr),this.addDetector(or),this.addDetector(ir),this.addDetector(sr),this.addDetector(lr),this.addDetector(cr)}addDetector(e){return this.detectors[e.name]=e,this}detect(){var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:this.options.order;let r=[];return e.forEach(t=>{if(this.detectors[t]){let e=this.detectors[t].lookup(this.options);(e=e&&"string"==typeof e?[e]:e)&&(r=r.concat(e))}}),r=r.filter(e=>null!=e&&!Kt(e)).map(e=>this.options.convertDetectedLanguage(e)),this.services&&this.services.languageUtils&&this.services.languageUtils.getBestMatchFromCodes?r:0<r.length?r[0]:null}cacheUserLanguage(t){var e=1<arguments.length&&void 0!==arguments[1]?arguments[1]:this.options.caches;!e||this.options.excludeCacheFor&&-1<this.options.excludeCacheFor.indexOf(t)||e.forEach(e=>{this.detectors[e]&&this.detectors[e].cacheUserLanguage(t,this.options)})}},fr=(dr.type="languageDetector",t(r())),gr=(t(r(),1),t(r(),1),t(_()),{}),br=(e,t,r,a)=>{Er(r)&&gr[r]||(Er(r)&&(gr[r]=new Date),e=e,r=[r=r,{code:t,...a||{}}],e?.services?.logger?.forward?e.services.logger.forward(r,"warn","react-i18next::",!0):(Er(r[0])&&(r[0]="react-i18next:: "+r[0]),e?.services?.logger?.warn?e.services.logger.warn(...r):console?.warn&&console.warn(...r)))},vr=(t,r)=>()=>{if(t.isInitialized)r();else{const e=()=>{setTimeout(()=>{t.off("initialized",e)},0),r()};t.on("initialized",e)}},yr=(e,t,r)=>{e.loadNamespaces(t,vr(e,r))},wr=(t,e,r,a)=>{if(Er(r)&&(r=[r]),t.options.preload&&-1<t.options.preload.indexOf(e))return yr(t,r,a);r.forEach(e=>{t.options.ns.indexOf(e)<0&&t.options.ns.push(e)}),t.loadLanguages(e,vr(t,a))},Sr=(r,e,a={})=>e.languages&&e.languages.length?e.hasLoadedNamespace(r,{lng:a.lng,precheck:(e,t)=>{if(-1<a.bindI18n?.indexOf("languageChanging")&&e.services.backendConnector.backend&&e.isLanguageChangingTo&&!t(e.isLanguageChangingTo,r))return!1}}):(br(e,"NO_LANGUAGES","i18n.languages were undefined or empty",{languages:e.languages}),!0),Er=e=>"string"==typeof e,kr=e=>"object"==typeof e&&null!==e,Cr=/&(?:amp|#38|lt|#60|gt|#62|apos|#39|quot|#34|nbsp|#160|copy|#169|reg|#174|hellip|#8230|#x2F|#47);/g,xr={"&amp;":"&","&#38;":"&","&lt;":"<","&#60;":"<","&gt;":">","&#62;":">","&apos;":"'","&#39;":"'","&quot;":'"',"&#34;":'"',"&nbsp;":" ","&#160;":" ","&copy;":"©","&#169;":"©","&reg;":"®","&#174;":"®","&hellip;":"…","&#8230;":"…","&#x2F;":"/","&#47;":"/"},Ir=e=>xr[e],Nr={bindI18n:"languageChanged",bindI18nStore:"",transEmptyNodeValue:"",transSupportBasicHtmlNodes:!0,transWrapTextNodes:"",transKeepBasicHtmlNodesFor:["br","strong","i","p"],useSuspense:!0,unescape:e=>e.replace(Cr,Ir)},Ar=()=>Nr,Tr=()=>ur,_={type:"3rdParty",init(e){var t;[t={}]=[e.options.react],Nr={...Nr,...t},ur=e}},Or=(0,t(r(),1).createContext)(),Pr=class{constructor(){this.usedNamespaces={}}addUsedNamespaces(e){e.forEach(e=>{this.usedNamespaces[e]||(this.usedNamespaces[e]=!0)})}getUsedNamespaces(){return Object.keys(this.usedNamespaces)}},Lr=t(r(),1),Dr=(e,t)=>{const r=(0,Lr.useRef)();return(0,Lr.useEffect)(()=>{r.current=t?r.current:e},[e,t]),r.current},Rr=(e,t,r,a)=>e.getFixedT(t,r,a),Mr=(e,t,r,a)=>(0,Lr.useCallback)(Rr(e,t,r,a),[e,t,r,a]),jr=t(r(),1),_r=(i,s={})=>function(o){function r({forwardedRef:e,...t}){var[r,a,n]=((e,a)=>{var t=a["i18n"],{i18n:r,defaultNS:n}=(0,Lr.useContext)(Or)||{};const o=t||r||Tr();if(o&&!o.reportNamespaces&&(o.reportNamespaces=new Pr),!o)return br(o,"NO_I18NEXT_INSTANCE","useTranslation: You will need to pass in an i18next instance by using initReactI18next"),(r=[t=(e,t)=>Er(t)?t:kr(t)&&Er(t.defaultValue)?t.defaultValue:Array.isArray(e)?e[e.length-1]:e,{},!1]).t=t,r.i18n={},r.ready=!1,r;o.options.react?.wait&&br(o,"DEPRECATED_OPTION","useTranslation: It seems you are still using the old wait option, you may migrate to the new useSuspense behaviour.");const i={...Ar(),...o.options.react,...a},{useSuspense:s,keyPrefix:l}=i;let c=e||n||o.options?.defaultNS;c=Er(c)?[c]:c||["translation"],o.reportNamespaces.addUsedNamespaces?.(c);const d=(o.isInitialized||o.initializedStoreOnce)&&c.every(e=>Sr(e,o,i)),u=Mr(o,a.lng||null,"fallback"===i.nsMode?c:c[0],l),p=()=>u,m=()=>Rr(o,a.lng||null,"fallback"===i.nsMode?c:c[0],l),[h,f]=(0,Lr.useState)(p);let g=c.join();a.lng&&(g=""+a.lng+g);const b=Dr(g),v=(0,Lr.useRef)(!0);(0,Lr.useEffect)(()=>{const{bindI18n:e,bindI18nStore:t}=i,r=(v.current=!0,d||s||(a.lng?wr(o,a.lng,c,()=>{v.current&&f(m)}):yr(o,c,()=>{v.current&&f(m)})),d&&b&&b!==g&&v.current&&f(m),()=>{v.current&&f(m)});return e&&o?.on(e,r),t&&o?.store.on(t,r),()=>{v.current=!1,o&&e?.split(" ").forEach(e=>o.off(e,r)),t&&o&&t.split(" ").forEach(e=>o.store.off(e,r))}},[o,g]),(0,Lr.useEffect)(()=>{v.current&&d&&f(p)},[o,l,d]);t=[h,o,d];if(t.t=h,t.i18n=o,(t.ready=d)||!d&&!s)return t;throw new Promise(e=>{a.lng?wr(o,a.lng,c,()=>e()):yr(o,c,()=>e())})})(i,{...t,keyPrefix:s.keyPrefix}),t={...t,t:r,i18n:a,tReady:n};return s.withRef&&e?t.ref=e:!s.withRef&&e&&(t.forwardedRef=e),(0,jr.createElement)(o,t)}var e;r.displayName=`withI18nextTranslation(${e=o,e.displayName||e.name||(Er(e)&&0<e.length?e:"Unknown")})`,r.WrappedComponent=o;return s.withRef?(0,jr.forwardRef)((e,t)=>(0,jr.createElement)(r,Object.assign({},e,{forwardedRef:t}))):r},o=(t(r(),1),t(r(),1),t(r(),1),t(r())),Br=t(a()),$r="1.0.6",a="marketplace",x={installedExtensions:a+":installed-extensions",installedSnippets:a+":installed-snippets",installedThemes:a+":installed-themes",activeTab:a+":active-tab",tabs:a+":tabs",sort:a+":sort",themeInstalled:a+":theme-installed",localTheme:a+":local-theme",albumArtBasedColor:a+":albumArtBasedColors",albumArtBasedColorMode:a+":albumArtBasedColorsMode",albumArtBasedColorVibrancy:a+":albumArtBasedColorsVibrancy",colorShift:a+":colorShift"},zr=[{name:"Extensions",enabled:!0},{name:"Themes",enabled:!0},{name:"Snippets",enabled:!0},{name:"Apps",enabled:!0},{name:"Installed",enabled:!0}],Fr=100,Ur="/marketplace",Gr="https://github.com/spicetify/marketplace/releases",Vr="https://api.github.com/repos/spicetify/marketplace/releases/latest",{min:Hr,max:qr}=Math,Wr=(e,t=0,r=1)=>Hr(qr(t,e),r),Kr=t=>{t._clipped=!1,t._unclipped=t.slice(0);for(let e=0;e<=3;e++)e<3?((t[e]<0||255<t[e])&&(t._clipped=!0),t[e]=Wr(t[e],0,255)):3===e&&(t[e]=Wr(t[e],0,1));return t},Jr={};for(pr of["Boolean","Number","String","Function","Array","Date","RegExp","Undefined","Null"])Jr[`[object ${pr}]`]=pr.toLowerCase();function C(e){return Jr[Object.prototype.toString.call(e)]||"object"}var h=(t,e=null)=>3<=t.length?Array.prototype.slice.call(t):"object"==C(t[0])&&e?e.split("").filter(e=>void 0!==t[0][e]).map(e=>t[0][e]):t[0].slice(0),Yr=e=>{var t;return!(e.length<2)&&"string"==C(e[t=e.length-1])?e[t].toLowerCase():null},{PI:a,min:Zr,max:Xr}=Math,Qr=e=>Math.round(100*e)/100,ea=e=>Math.round(100*e)/100,ta=2*a,ra=a/3,aa=a/180,na=180/a,i={format:{},autodetect:[]},g=class{constructor(...e){if("object"===C(e[0])&&e[0].constructor&&e[0].constructor===this.constructor)return e[0];let t=Yr(e),r=!1;if(!t){r=!0,i.sorted||(i.autodetect=i.autodetect.sort((e,t)=>t.p-e.p),i.sorted=!0);for(var a of i.autodetect)if(t=a.test(...e))break}if(!i.format[t])throw new Error("unknown format: "+e);var n=i.format[t].apply(null,r?e:e.slice(0,-1));this._rgb=Kr(n),3===this._rgb.length&&this._rgb.push(1)}toString(){return"function"==C(this.hex)?this.hex():`[${this._rgb.join(",")}]`}},a=(...e)=>new g(...e),I=(a.version="3.1.1",a),oa={aliceblue:"#f0f8ff",antiquewhite:"#faebd7",aqua:"#00ffff",aquamarine:"#7fffd4",azure:"#f0ffff",beige:"#f5f5dc",bisque:"#ffe4c4",black:"#000000",blanchedalmond:"#ffebcd",blue:"#0000ff",blueviolet:"#8a2be2",brown:"#a52a2a",burlywood:"#deb887",cadetblue:"#5f9ea0",chartreuse:"#7fff00",chocolate:"#d2691e",coral:"#ff7f50",cornflowerblue:"#6495ed",cornsilk:"#fff8dc",crimson:"#dc143c",cyan:"#00ffff",darkblue:"#00008b",darkcyan:"#008b8b",darkgoldenrod:"#b8860b",darkgray:"#a9a9a9",darkgreen:"#006400",darkgrey:"#a9a9a9",darkkhaki:"#bdb76b",darkmagenta:"#8b008b",darkolivegreen:"#556b2f",darkorange:"#ff8c00",darkorchid:"#9932cc",darkred:"#8b0000",darksalmon:"#e9967a",darkseagreen:"#8fbc8f",darkslateblue:"#483d8b",darkslategray:"#2f4f4f",darkslategrey:"#2f4f4f",darkturquoise:"#00ced1",darkviolet:"#9400d3",deeppink:"#ff1493",deepskyblue:"#00bfff",dimgray:"#696969",dimgrey:"#696969",dodgerblue:"#1e90ff",firebrick:"#b22222",floralwhite:"#fffaf0",forestgreen:"#228b22",fuchsia:"#ff00ff",gainsboro:"#dcdcdc",ghostwhite:"#f8f8ff",gold:"#ffd700",goldenrod:"#daa520",gray:"#808080",green:"#008000",greenyellow:"#adff2f",grey:"#808080",honeydew:"#f0fff0",hotpink:"#ff69b4",indianred:"#cd5c5c",indigo:"#4b0082",ivory:"#fffff0",khaki:"#f0e68c",laserlemon:"#ffff54",lavender:"#e6e6fa",lavenderblush:"#fff0f5",lawngreen:"#7cfc00",lemonchiffon:"#fffacd",lightblue:"#add8e6",lightcoral:"#f08080",lightcyan:"#e0ffff",lightgoldenrod:"#fafad2",lightgoldenrodyellow:"#fafad2",lightgray:"#d3d3d3",lightgreen:"#90ee90",lightgrey:"#d3d3d3",lightpink:"#ffb6c1",lightsalmon:"#ffa07a",lightseagreen:"#20b2aa",lightskyblue:"#87cefa",lightslategray:"#778899",lightslategrey:"#778899",lightsteelblue:"#b0c4de",lightyellow:"#ffffe0",lime:"#00ff00",limegreen:"#32cd32",linen:"#faf0e6",magenta:"#ff00ff",maroon:"#800000",maroon2:"#7f0000",maroon3:"#b03060",mediumaquamarine:"#66cdaa",mediumblue:"#0000cd",mediumorchid:"#ba55d3",mediumpurple:"#9370db",mediumseagreen:"#3cb371",mediumslateblue:"#7b68ee",mediumspringgreen:"#00fa9a",mediumturquoise:"#48d1cc",mediumvioletred:"#c71585",midnightblue:"#191970",mintcream:"#f5fffa",mistyrose:"#ffe4e1",moccasin:"#ffe4b5",navajowhite:"#ffdead",navy:"#000080",oldlace:"#fdf5e6",olive:"#808000",olivedrab:"#6b8e23",orange:"#ffa500",orangered:"#ff4500",orchid:"#da70d6",palegoldenrod:"#eee8aa",palegreen:"#98fb98",paleturquoise:"#afeeee",palevioletred:"#db7093",papayawhip:"#ffefd5",peachpuff:"#ffdab9",peru:"#cd853f",pink:"#ffc0cb",plum:"#dda0dd",powderblue:"#b0e0e6",purple:"#800080",purple2:"#7f007f",purple3:"#a020f0",rebeccapurple:"#663399",red:"#ff0000",rosybrown:"#bc8f8f",royalblue:"#4169e1",saddlebrown:"#8b4513",salmon:"#fa8072",sandybrown:"#f4a460",seagreen:"#2e8b57",seashell:"#fff5ee",sienna:"#a0522d",silver:"#c0c0c0",skyblue:"#87ceeb",slateblue:"#6a5acd",slategray:"#708090",slategrey:"#708090",snow:"#fffafa",springgreen:"#00ff7f",steelblue:"#4682b4",tan:"#d2b48c",teal:"#008080",thistle:"#d8bfd8",tomato:"#ff6347",turquoise:"#40e0d0",violet:"#ee82ee",wheat:"#f5deb3",white:"#ffffff",whitesmoke:"#f5f5f5",yellow:"#ffff00",yellowgreen:"#9acd32"},ia=/^#?([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/,sa=/^#?([A-Fa-f0-9]{8}|[A-Fa-f0-9]{4})$/,la=e=>{var t;if(e.match(ia))return 3===(e=4!==e.length&&7!==e.length?e:e.substr(1)).length&&(e=(e=e.split(""))[0]+e[0]+e[1]+e[1]+e[2]+e[2]),[(t=parseInt(e,16))>>16,t>>8&255,255&t,1];if(e.match(sa))return 4===(e=5!==e.length&&9!==e.length?e:e.substr(1)).length&&(e=(e=e.split(""))[0]+e[0]+e[1]+e[1]+e[2]+e[2]+e[3]+e[3]),[(t=parseInt(e,16))>>24&255,t>>16&255,t>>8&255,Math.round((255&t)/255*100)/100];throw new Error("unknown hex color: "+e)},ca=Math["round"],da=(...e)=>{let[t,r,a,n]=h(e,"rgba"),o=Yr(e)||"auto";void 0===n&&(n=1),"auto"===o&&(o=n<1?"rgba":"rgb");let i="000000"+(ca(t)<<16|ca(r)<<8|ca(a)).toString(16),s=(i=i.substr(i.length-6),"0"+ca(255*n).toString(16));switch(s=s.substr(s.length-2),o.toLowerCase()){case"rgba":return"#"+i+s;case"argb":return"#"+s+i;default:return"#"+i}},ua=(g.prototype.name=function(){var e,t=da(this._rgb,"rgb");for(e of Object.keys(oa))if(oa[e]===t)return e.toLowerCase();return t},i.format.named=e=>{if(e=e.toLowerCase(),oa[e])return la(oa[e]);throw new Error("unknown color name: "+e)},i.autodetect.push({p:5,test:(e,...t)=>{if(!t.length&&"string"===C(e)&&oa[e.toLowerCase()])return"named"}}),g.prototype.alpha=function(e,t=!1){return void 0!==e&&"number"===C(e)?t?(this._rgb[3]=e,this):new g([this._rgb[0],this._rgb[1],this._rgb[2],e],"rgb"):this._rgb[3]},g.prototype.clipped=function(){return this._rgb._clipped||!1},{Kn:18,labWhitePoint:"d65",Xn:.95047,Yn:1,Zn:1.08883,t0:.137931034,t1:.206896552,t2:.12841855,t3:.008856452,kE:216/24389,kKE:8,kK:24389/27,RefWhiteRGB:{X:.95047,Y:1,Z:1.08883},MtxRGB2XYZ:{m00:.4124564390896922,m01:.21267285140562253,m02:.0193338955823293,m10:.357576077643909,m11:.715152155287818,m12:.11919202588130297,m20:.18043748326639894,m21:.07217499330655958,m22:.9503040785363679},MtxXYZ2RGB:{m00:3.2404541621141045,m01:-.9692660305051868,m02:.055643430959114726,m10:-1.5371385127977166,m11:1.8760108454466942,m12:-.2040259135167538,m20:-.498531409556016,m21:.041556017530349834,m22:1.0572251882231791},As:.9414285350000001,Bs:1.040417467,Cs:1.089532651,MtxAdaptMa:{m00:.8951,m01:-.7502,m02:.0389,m10:.2664,m11:1.7135,m12:-.0685,m20:-.1614,m21:.0367,m22:1.0296},MtxAdaptMaI:{m00:.9869929054667123,m01:.43230526972339456,m02:-.008528664575177328,m10:-.14705425642099013,m11:.5183602715367776,m12:.04004282165408487,m20:.15996265166373125,m21:.0492912282128556,m22:.9684866957875502}}),pa=ua,ma=new Map([["a",[1.0985,.35585]],["b",[1.0985,.35585]],["c",[.98074,1.18232]],["d50",[.96422,.82521]],["d55",[.95682,.92149]],["d65",[.95047,1.08883]],["e",[1,1,1]],["f2",[.99186,.67393]],["f7",[.95041,1.08747]],["f11",[1.00962,.6435]],["icc",[.96422,.82521]]]);function ha(e){var t=ma.get(String(e).toLowerCase());if(!t)throw new Error("unknown Lab illuminant "+e);ua.labWhitePoint=e,ua.Xn=t[0],ua.Zn=t[1]}function fa(){return ua.labWhitePoint}var ga=e=>{var t=Math.sign(e);return((e=Math.abs(e))<=.0031308?12.92*e:1.055*Math.pow(e,1/2.4)-.055)*t},ba=(e,t,r)=>{var{MtxAdaptMa:a,MtxAdaptMaI:n,MtxXYZ2RGB:o,RefWhiteRGB:i,Xn:s,Yn:l,Zn:c}=pa,d=s*a.m00+l*a.m10+c*a.m20,u=s*a.m01+l*a.m11+c*a.m21,s=s*a.m02+l*a.m12+c*a.m22,l=i.X*a.m00+i.Y*a.m10+i.Z*a.m20,c=i.X*a.m01+i.Y*a.m11+i.Z*a.m21,i=i.X*a.m02+i.Y*a.m12+i.Z*a.m22,l=(e*a.m00+t*a.m10+r*a.m20)*(l/d),d=(e*a.m01+t*a.m11+r*a.m21)*(c/u),c=(e*a.m02+t*a.m12+r*a.m22)*(i/s),u=l*n.m00+d*n.m10+c*n.m20,e=l*n.m01+d*n.m11+c*n.m21,t=l*n.m02+d*n.m12+c*n.m22;return[255*ga(u*o.m00+e*o.m10+t*o.m20),255*ga(u*o.m01+e*o.m11+t*o.m21),255*ga(u*o.m02+e*o.m12+t*o.m22)]},va=(...e)=>{var[t,r,a]=e=h(e,"lab"),[t,r,a]=((e,t,r)=>{const{kE:a,kK:n,kKE:o,Xn:i,Yn:s,Zn:l}=pa,c=(e+16)/116,d=.002*t+c,u=c-.005*r,p=d*d*d,m=u*u*u,h=p>a?p:(116*d-16)/n,f=e>o?Math.pow((e+16)/116,3):e/n,g=m>a?m:(116*u-16)/n,b=h*i,v=f*s,y=g*l;return[b,v,y]})(t,r,a),[t,r,a]=ba(t,r,a);return[t,r,a,3<e.length?e[3]:1]};function ya(e){var t=Math.sign(e);return((e=Math.abs(e))<=.04045?e/12.92:Math.pow((e+.055)/1.055,2.4))*t}var wa=(e,t,r)=>{e=ya(e/255),t=ya(t/255),r=ya(r/255);var{MtxRGB2XYZ:a,MtxAdaptMa:n,MtxAdaptMaI:o,Xn:i,Yn:s,Zn:l,As:c,Bs:d,Cs:u}=pa,p=e*a.m00+t*a.m10+r*a.m20,m=e*a.m01+t*a.m11+r*a.m21,e=e*a.m02+t*a.m12+r*a.m22,t=i*n.m00+s*n.m10+l*n.m20,r=i*n.m01+s*n.m11+l*n.m21,a=i*n.m02+s*n.m12+l*n.m22,i=p*n.m00+m*n.m10+e*n.m20,s=p*n.m01+m*n.m11+e*n.m21,l=p*n.m02+m*n.m12+e*n.m22;return[(i*=t/c)*o.m00+(s*=r/d)*o.m10+(l*=a/u)*o.m20,i*o.m01+s*o.m11+l*o.m21,i*o.m02+s*o.m12+l*o.m22]},Sa=(...e)=>{var[e,t,r,...a]=h(e,"rgb"),[e,t,r]=wa(e,t,r),[e,t,r]=function(e,t,r){var{Xn:a,Yn:n,Zn:o,kE:i,kK:s}=pa,e=e/a,a=t/n,t=r/o,n=i<e?Math.pow(e,1/3):(s*e+16)/116,r=i<a?Math.pow(a,1/3):(s*a+16)/116,o=i<t?Math.pow(t,1/3):(s*t+16)/116;return[116*r-16,500*(n-r),200*(r-o)]}(e,t,r);return[e,t,r,...0<a.length&&a[0]<1?[a[0]]:[]]},Ea=(g.prototype.lab=function(){return Sa(this._rgb)},(Object.assign(I,{lab:(...e)=>new g(...e,"lab"),getLabWhitePoint:fa,setLabWhitePoint:ha}),i.format.lab=va,i.autodetect.push({p:2,test:(...e)=>{if("array"===C(e=h(e,"lab"))&&3===e.length)return"lab"}}),g.prototype.darken=function(e=1){var t=this.lab();return t[0]-=pa.Kn*e,new g(t,"lab").alpha(this.alpha(),!0)},g.prototype.brighten=function(e=1){return this.darken(-e)},g.prototype.darker=g.prototype.darken,g.prototype.brighter=g.prototype.brighten,g.prototype.get=function(e){var[e,t]=e.split("."),r=this[e]();if(t){var a=e.indexOf(t)-("ok"===e.substr(0,2)?2:0);if(-1<a)return r[a];throw new Error(`unknown channel ${t} in mode `+e)}return r},Math)["pow"]),ka=(g.prototype.luminance=function(o,i="rgb"){if(void 0===o||"number"!==C(o))return ka(...this._rgb.slice(0,3));{if(0===o)return new g([0,0,0,this._rgb[3]],"rgb");if(1===o)return new g([255,255,255,this._rgb[3]],"rgb");var e=this.luminance();let n=20;const s=(e,t)=>{var r=e.interpolate(t,.5,i),a=r.luminance();return Math.abs(o-a)<1e-7||!n--?r:o<a?s(e,r):s(r,t)};e=(o<e?s(new g([0,0,0]),this):s(this,new g([255,255,255]))).rgb();return new g([...e,this._rgb[3]])}},(e,t,r)=>.2126*(e=Ca(e))+.7152*(t=Ca(t))+.0722*(r=Ca(r))),Ca=e=>(e/=255)<=.03928?e/12.92:Ea((e+.055)/1.055,2.4),s={},xa=(e,t,r=.5,...a)=>{let n=a[0]||"lrgb";if(s[n]||a.length||(n=Object.keys(s)[0]),s[n])return"object"!==C(e)&&(e=new g(e)),"object"!==C(t)&&(t=new g(t)),s[n](e,t,r).alpha(e.alpha()+r*(t.alpha()-e.alpha()));throw new Error(`interpolation mode ${n} is not defined`)},{sin:Ia,cos:Na}=(g.prototype.mix=g.prototype.interpolate=function(e,t=.5,...r){return xa(this,e,t,...r)},g.prototype.premultiply=function(e=!1){var t=this._rgb,r=t[3];return e?(this._rgb=[t[0]*r,t[1]*r,t[2]*r,r],this):new g([t[0]*r,t[1]*r,t[2]*r,r],"rgb")},Math),Aa=(...e)=>{let[t,r,a]=h(e,"lch");return isNaN(a)&&(a=0),a*=aa,[t,Na(a)*r,Ia(a)*r]},Ta=(...e)=>{var[t,r,a]=e=h(e,"lch"),[t,r,a]=Aa(t,r,a),[t,r,a]=va(t,r,a);return[t,r,a,3<e.length?e[3]:1]},a=(...e)=>{e=h(e,"hcl").reverse();return Ta(...e)},{sqrt:Oa,atan2:Pa,round:La}=Math,Da=(...e)=>{var[e,t,r]=h(e,"lab"),a=Oa(t*t+r*r);let n=(Pa(r,t)*na+360)%360;return[e,a,n=0===La(1e4*a)?Number.NaN:n]},Ra=(...e)=>{var[e,t,r,...a]=h(e,"rgb"),[e,t,r]=Sa(e,t,r),[e,t,r]=Da(e,t,r);return[e,t,r,...0<a.length&&a[0]<1?[a[0]]:[]]},{sqrt:Ma,pow:ja}=(g.prototype.lch=function(){return Ra(this._rgb)},g.prototype.hcl=function(){return Ra(this._rgb).reverse()},Object.assign(I,{lch:(...e)=>new g(...e,"lch"),hcl:(...e)=>new g(...e,"hcl")}),i.format.lch=Ta,i.format.hcl=a,["lch","hcl"].forEach(t=>i.autodetect.push({p:2,test:(...e)=>{if("array"===C(e=h(e,t))&&3===e.length)return t}})),g.prototype.saturate=function(e=1){var t=this.lch();return t[1]+=pa.Kn*e,t[1]<0&&(t[1]=0),new g(t,"lch").alpha(this.alpha(),!0)},g.prototype.desaturate=function(e=1){return this.saturate(-e)},g.prototype.set=function(e,t,r=!1){var[e,a]=e.split("."),n=this[e]();if(a){var o=e.indexOf(a)-("ok"===e.substr(0,2)?2:0);if(-1<o){if("string"==C(t))switch(t.charAt(0)){case"+":case"-":n[o]+=+t;break;case"*":n[o]*=+t.substr(1);break;case"/":n[o]/=+t.substr(1);break;default:n[o]=+t}else{if("number"!==C(t))throw new Error("unsupported value for Color.set");n[o]=t}var i=new g(n,e);return r?(this._rgb=i._rgb,this):i}throw new Error(`unknown channel ${a} in mode `+e)}return n},g.prototype.tint=function(e=.5,...t){return xa(this,"white",e,...t)},g.prototype.shade=function(e=.5,...t){return xa(this,"black",e,...t)},s.rgb=(e,t,r)=>{e=e._rgb,t=t._rgb;return new g(e[0]+r*(t[0]-e[0]),e[1]+r*(t[1]-e[1]),e[2]+r*(t[2]-e[2]),"rgb")},Math),_a=(s.lrgb=(e,t,r)=>{var[e,a,n]=e._rgb,[t,o,i]=t._rgb;return new g(Ma(ja(e,2)*(1-r)+ja(t,2)*r),Ma(ja(a,2)*(1-r)+ja(o,2)*r),Ma(ja(n,2)*(1-r)+ja(i,2)*r),"rgb")},s.lab=(e,t,r)=>{e=e.lab(),t=t.lab();return new g(e[0]+r*(t[0]-e[0]),e[1]+r*(t[1]-e[1]),e[2]+r*(t[2]-e[2]),"lab")},(e,t,r,a)=>{let n,o;"hsl"===a?(n=e.hsl(),o=t.hsl()):"hsv"===a?(n=e.hsv(),o=t.hsv()):"hcg"===a?(n=e.hcg(),o=t.hcg()):"hsi"===a?(n=e.hsi(),o=t.hsi()):"lch"===a||"hcl"===a?(a="hcl",n=e.hcl(),o=t.hcl()):"oklch"===a&&(n=e.oklch().reverse(),o=t.oklch().reverse());let i,s,l,c,d,u;"h"!==a.substr(0,1)&&"oklch"!==a||([i,l,d]=n,[s,c,u]=o);let p,m,h,f;return isNaN(i)||isNaN(s)?isNaN(i)?isNaN(s)?m=Number.NaN:(m=s,1!=d&&0!=d||"hsv"==a||(p=c)):(m=i,1!=u&&0!=u||"hsv"==a||(p=l)):(f=s>i&&180<s-i?s-(i+360):s<i&&180<i-s?s+360-i:s-i,m=i+r*f),void 0===p&&(p=l+r*(c-l)),h=d+r*(u-d),new g("oklch"===a?[h,p,m]:[m,p,h],a)}),a=(e,t,r)=>_a(e,t,r,"lch"),a=(s.lch=a,s.hcl=a,e=>{if("number"==C(e)&&0<=e&&e<=16777215)return[e>>16,e>>8&255,255&e,1];throw new Error("unknown num color: "+e)}),Ba=(...e)=>{var[e,t,r]=h(e,"rgb");return(e<<16)+(t<<8)+r},$a=(g.prototype.num=function(){return Ba(this._rgb)},Object.assign(I,{num:(...e)=>new g(...e,"num")}),i.format.num=a,i.autodetect.push({p:5,test:(...e)=>{if(1===e.length&&"number"===C(e[0])&&0<=e[0]&&e[0]<=16777215)return"num"}}),(s.num=(e,t,r)=>{e=e.num(),t=t.num();return new g(e+r*(t-e),"num")},Math)["floor"]),a=(...e)=>{let[t,r,a]=e=h(e,"hcg"),n,o,i;a*=255;var s=255*r;if(0===r)n=o=i=a;else{360<(t=360===t?0:t)&&(t-=360),t<0&&(t+=360),t/=60;var l=$a(t),c=t-l,d=a*(1-r),u=d+s*(1-c),p=d+s*c,m=d+s;switch(l){case 0:[n,o,i]=[m,p,d];break;case 1:[n,o,i]=[u,m,d];break;case 2:[n,o,i]=[d,m,p];break;case 3:[n,o,i]=[d,u,m];break;case 4:[n,o,i]=[p,d,m];break;case 5:[n,o,i]=[m,d,u]}}return[n,o,i,3<e.length?e[3]:1]},za=(...e)=>{var[e,t,r]=h(e,"rgb"),a=Zr(e,t,r),n=Xr(e,t,r),o=n-a;let i;return 0==o?i=Number.NaN:(e===n&&(i=(t-r)/o),t===n&&(i=2+(r-e)/o),r===n&&(i=4+(e-t)/o),(i*=60)<0&&(i+=360)),[i,100*o/255,a/(255-o)*100]},Fa=(g.prototype.hcg=function(){return za(this._rgb)},I.hcg=(...e)=>new g(...e,"hcg"),i.format.hcg=a,i.autodetect.push({p:1,test:(...e)=>{if("array"===C(e=h(e,"hcg"))&&3===e.length)return"hcg"}}),(s.hcg=(e,t,r)=>_a(e,t,r,"hcg"),Math)["cos"]),a=(...e)=>{let[t,r,a]=e=h(e,"hsi"),n,o,i;return isNaN(t)&&(t=0),isNaN(r)&&(r=0),360<t&&(t-=360),t<0&&(t+=360),(t/=360)<1/3?(i=(1-r)/3,n=(1+r*Fa(ta*t)/Fa(ra-ta*t))/3,o=1-(i+n)):t<2/3?(t-=1/3,n=(1-r)/3,o=(1+r*Fa(ta*t)/Fa(ra-ta*t))/3,i=1-(n+o)):(t-=2/3,o=(1-r)/3,i=(1+r*Fa(ta*t)/Fa(ra-ta*t))/3,n=1-(o+i)),n=Wr(a*n*3),o=Wr(a*o*3),i=Wr(a*i*3),[255*n,255*o,255*i,3<e.length?e[3]:1]},{min:Ua,sqrt:Ga,acos:Va}=Math,Ha=(...e)=>{var[e,t,r]=h(e,"rgb");let a;var n=Ua(e/=255,t/=255,r/=255),o=(e+t+r)/3,n=0<o?1-n/o:0;return 0==n?a=NaN:(a=(e-t+(e-r))/2,a/=Ga((e-t)*(e-t)+(e-r)*(t-r)),a=Va(a),t<r&&(a=ta-a),a/=ta),[360*a,n,o]},qa=(g.prototype.hsi=function(){return Ha(this._rgb)},I.hsi=(...e)=>new g(...e,"hsi"),i.format.hsi=a,i.autodetect.push({p:2,test:(...e)=>{if("array"===C(e=h(e,"hsi"))&&3===e.length)return"hsi"}}),s.hsi=(e,t,r)=>_a(e,t,r,"hsi"),(...e)=>{var[t,r,a]=e=h(e,"hsl");let n,o,i;if(0===r)n=o=i=255*a;else{var s=[0,0,0],l=[0,0,0],c=a<.5?a*(1+r):a+r-a*r,d=2*a-c,r=t/360;s[0]=r+1/3,s[1]=r,s[2]=r-1/3;for(let e=0;e<3;e++)s[e]<0&&(s[e]+=1),1<s[e]&&--s[e],6*s[e]<1?l[e]=d+6*(c-d)*s[e]:2*s[e]<1?l[e]=c:3*s[e]<2?l[e]=d+(c-d)*(2/3-s[e])*6:l[e]=d;[n,o,i]=[255*l[0],255*l[1],255*l[2]]}return 3<e.length?[n,o,i,e[3]]:[n,o,i,1]}),Wa=(...e)=>{var[t,r,a]=e=h(e,"rgba"),n=Zr(t/=255,r/=255,a/=255),o=Xr(t,r,a),i=(o+n)/2;let s,l;return o===n?(s=0,l=Number.NaN):s=i<.5?(o-n)/(o+n):(o-n)/(2-o-n),t==o?l=(r-a)/(o-n):r==o?l=2+(a-t)/(o-n):a==o&&(l=4+(t-r)/(o-n)),(l*=60)<0&&(l+=360),3<e.length&&void 0!==e[3]?[l,s,i,e[3]]:[l,s,i]},Ka=(g.prototype.hsl=function(){return Wa(this._rgb)},I.hsl=(...e)=>new g(...e,"hsl"),i.format.hsl=qa,i.autodetect.push({p:2,test:(...e)=>{if("array"===C(e=h(e,"hsl"))&&3===e.length)return"hsl"}}),(s.hsl=(e,t,r)=>_a(e,t,r,"hsl"),Math)["floor"]),a=(...e)=>{let[t,r,a]=e=h(e,"hsv"),n,o,i;if(a*=255,0===r)n=o=i=a;else{360<(t=360===t?0:t)&&(t-=360),t<0&&(t+=360),t/=60;var s=Ka(t),l=t-s,c=a*(1-r),d=a*(1-r*l),u=a*(1-r*(1-l));switch(s){case 0:[n,o,i]=[a,u,c];break;case 1:[n,o,i]=[d,a,c];break;case 2:[n,o,i]=[c,a,u];break;case 3:[n,o,i]=[c,d,a];break;case 4:[n,o,i]=[u,c,a];break;case 5:[n,o,i]=[a,c,d]}}return[n,o,i,3<e.length?e[3]:1]},{min:Ja,max:Ya}=Math,Za=(...e)=>{var[e,t,r]=e=h(e,"rgb"),a=Ja(e,t,r),n=Ya(e,t,r),a=n-a;let o,i;return 0===n?(o=Number.NaN,i=0):(i=a/n,e===n&&(o=(t-r)/a),t===n&&(o=2+(r-e)/a),r===n&&(o=4+(e-t)/a),(o*=60)<0&&(o+=360)),[o,i,n/255]};g.prototype.hsv=function(){return Za(this._rgb)},I.hsv=(...e)=>new g(...e,"hsv"),i.format.hsv=a,i.autodetect.push({p:2,test:(...e)=>{if("array"===C(e=h(e,"hsv"))&&3===e.length)return"hsv"}});function Xa(e,r){var t=e.length,a=(Array.isArray(e[0])||(e=[e]),(r=Array.isArray(r[0])?r:r.map(e=>[e]))[0].length);let n=r[0].map((e,t)=>r.map(e=>e[t])),o=e.map(r=>n.map(a=>Array.isArray(r)?r.reduce((e,t,r)=>e+t*(a[r]||0),0):a.reduce((e,t)=>e+t*r,0)));return 1===t&&(o=o[0]),1===a?o.map(e=>e[0]):o}s.hsv=(e,t,r)=>_a(e,t,r,"hsv");var Qa=(...e)=>{var[e,t,r,...a]=e=h(e,"lab"),[e,t,r]=function(e){e=Xa([[1,.3963377773761749,.2158037573099136],[1,-.1055613458156586,-.0638541728258133],[1,-.0894841775298119,-1.2914855480194092]],e);return Xa([[1.2268798758459243,-.5578149944602171,.2813910456659647],[-.0405757452148008,1.112286803280317,-.0717110580655164],[-.0763729366746601,-.4214933324022432,1.5869240198367816]],e.map(e=>e**3))}([e,t,r]),[e,t,r]=ba(e,t,r);return[e,t,r,...0<a.length&&a[0]<1?[a[0]]:[]]};var en=(...e)=>{var[e,t,r,...a]=h(e,"rgb");return[...function(e){e=Xa([[.819022437996703,.3619062600528904,-.1288737815209879],[.0329836539323885,.9292868615863434,.0361446663506424],[.0481771893596242,.2642395317527308,.6335478284694309]],e);return Xa([[.210454268309314,.7936177747023054,-.0040720430116193],[1.9779985324311684,-2.42859224204858,.450593709617411],[.0259040424655478,.7827717124575296,-.8086757549230774]],e.map(e=>Math.cbrt(e)))}(wa(e,t,r)),...0<a.length&&a[0]<1?[a[0]]:[]]},{pow:tn,sqrt:rn,PI:an,cos:nn,sin:on,atan2:sn}=(g.prototype.oklab=function(){return en(this._rgb)},Object.assign(I,{oklab:(...e)=>new g(...e,"oklab")}),i.format.oklab=Qa,i.autodetect.push({p:2,test:(...e)=>{if("array"===C(e=h(e,"oklab"))&&3===e.length)return"oklab"}}),s.oklab=(e,t,r)=>{e=e.oklab(),t=t.oklab();return new g(e[0]+r*(t[0]-e[0]),e[1]+r*(t[1]-e[1]),e[2]+r*(t[2]-e[2]),"oklab")},s.oklch=(e,t,r)=>_a(e,t,r,"oklch"),Math),ln=Math["pow"];function cn(o){let i="rgb",s=I("#ccc"),t=0,l=[0,1],c=[],d=[0,0],u=!1,p=[],r=!1,m=0,h=1,a,f={},g=!0,b=1;function n(t){if((t=t||["#fff","#000"])&&"string"===C(t)&&I.brewer&&I.brewer[t.toLowerCase()]&&(t=I.brewer[t.toLowerCase()]),"array"===C(t)){t=(t=1===t.length?[t[0],t[0]]:t).slice(0);for(let e=0;e<t.length;e++)t[e]=I(t[e]);for(let e=c.length=0;e<t.length;e++)c.push(e/(t.length-1))}E(),p=t}const v=function(t){if(null==u)return 0;{var r=u.length-1;let e=0;for(;e<r&&t>=u[e];)e++;return e-1}};let y=e=>e,w=e=>e;function S(e,t){let r,a;if(null==t&&(t=!1),isNaN(e)||null===e)return s;if(a=t?e:u&&2<u.length?v(e)/(u.length-2):h!==m?(e-m)/(h-m):1,a=w(a),t||(a=y(a)),1!==b&&(a=ln(a,b)),a=d[0]+a*(1-d[0]-d[1]),a=Wr(a,0,1),e=Math.floor(1e4*a),g&&f[e])r=f[e];else{if("array"===C(p))for(let e=0;e<c.length;e++){var n=c[e];if(a<=n){r=p[e];break}if(a>=n&&e===c.length-1){r=p[e];break}if(a>n&&a<c[e+1]){a=(a-n)/(c[e+1]-n),r=I.interpolate(p[e],p[e+1],a,i);break}}else"function"===C(p)&&(r=p(a));g&&(f[e]=r)}return r}var E=()=>f={};n(o);function k(e){return e=I(S(e)),r&&e[r]?e[r]():e}return k.classes=function(e){var t;return null!=e?("array"===C(e)?(u=e,l=[e[0],e[e.length-1]]):(t=I.analyze(l),u=0===e?[t.min,t.max]:I.limits(t,"e",e)),k):u},k.domain=function(r){if(!arguments.length)return l;m=r[0],h=r[r.length-1],c=[];var t=p.length;if(r.length===t&&m!==h)for(var e of Array.from(r))c.push((e-m)/(h-m));else{for(let e=0;e<t;e++)c.push(e/(t-1));if(2<r.length){const a=r.map((e,t)=>t/(r.length-1)),n=r.map(e=>(e-m)/(h-m));n.every((e,t)=>a[t]===e)||(w=e=>{if(e<=0||1<=e)return e;let t=0;for(;e>=n[t+1];)t++;var r=(e-n[t])/(n[t+1]-n[t]);return a[t]+r*(a[t+1]-a[t])})}}return l=[m,h],k},k.mode=function(e){return arguments.length?(i=e,E(),k):i},k.range=function(e,t){return n(e),k},k.out=function(e){return r=e,k},k.spread=function(e){return arguments.length?(t=e,k):t},k.correctLightness=function(e){return null==e&&(e=!0),a=e,E(),y=a?function(e){var t=S(0,!0).lab()[0],r=S(1,!0).lab()[0];const a=r<t;let n=S(e,!0).lab()[0];const o=t+(r-t)*e;let i=n-o,s=0,l=1,c=20;for(;.01<Math.abs(i)&&0<c--;)a&&(i*=-1),i<0?(s=e,e+=.5*(l-e)):(l=e,e+=.5*(s-e)),n=S(e,!0).lab()[0],i=n-o;return e}:e=>e,k},k.padding=function(e){return null!=e?("number"===C(e)&&(e=[e,e]),d=e,k):d},k.colors=function(t,r){arguments.length<2&&(r="hex");let e=[];if(0===arguments.length)e=p.slice(0);else if(1===t)e=[k(.5)];else if(1<t){const a=l[0],n=l[1]-a;e=function(t,e,r){var a=[],n=t<e,o=r?n?e+1:e-1:e;for(let e=t;n?e<o:e>o;n?e++:e--)a.push(e);return a}(0,t,!1).map(e=>k(a+e/(t-1)*n))}else{o=[];let a=[];if(u&&2<u.length)for(let e=1,t=u.length,r=1<=t;r?e<t:e>t;r?e++:e--)a.push(.5*(u[e-1]+u[e]));else a=l;e=a.map(e=>k(e))}return e=I[r]?e.map(e=>e[r]()):e},k.cache=function(e){return null!=e?(g=e,k):g},k.gamma=function(e){return null!=e?(b=e,k):b},k.nodata=function(e){return null!=e?(s=I(e),k):s},k}var dn=function(t){let r=[1,1];for(let e=1;e<t;e++){var a=[1];for(let e=1;e<=r.length;e++)a[e]=(r[e]||0)+r[e-1];r=a}return r},un=Math["round"],pn=(g.prototype.rgb=function(e=!0){return!1===e?this._rgb.slice(0,3):this._rgb.slice(0,3).map(un)},g.prototype.rgba=function(r=!0){return this._rgb.slice(0,4).map((e,t)=>!(t<3)||!1===r?e:un(e))},Object.assign(I,{rgb:(...e)=>new g(...e,"rgb")}),i.format.rgb=(...e)=>{e=h(e,"rgba");return void 0===e[3]&&(e[3]=1),e},i.autodetect.push({p:3,test:(...e)=>{if("array"===C(e=h(e,"rgba"))&&(3===e.length||4===e.length&&"number"==C(e[3])&&0<=e[3]&&e[3]<=1))return"rgb"}}),(e,t,r)=>{if(pn[r])return pn[r](e,t);throw new Error("unknown blend mode "+r)}),a=r=>(e,t)=>{t=I(t).rgb(),e=I(e).rgb();return I.rgb(r(t,e))},mn=a=>(e,t)=>{var r=[];return r[0]=a(e[0],t[0]),r[1]=a(e[1],t[1]),r[2]=a(e[2],t[2]),r},a=(pn.normal=a(mn(e=>e)),pn.multiply=a(mn((e,t)=>e*t/255)),pn.screen=a(mn((e,t)=>255*(1-(1-e/255)*(1-t/255)))),pn.overlay=a(mn((e,t)=>t<128?2*e*t/255:255*(1-2*(1-e/255)*(1-t/255)))),pn.darken=a(mn((e,t)=>t<e?t:e)),pn.lighten=a(mn((e,t)=>t<e?e:t)),pn.dodge=a(mn((e,t)=>255===e||255<(e=t/255*255/(1-e/255))?255:e)),pn.burn=a(mn((e,t)=>255*(1-(1-t/255)/(e/255)))),pn),{pow:hn,sin:fn,cos:gn}=Math;var{floor:bn,random:vn}=Math,{log:yn,pow:wn,floor:Sn,abs:En}=Math;function kn(e,t=null){const r={min:Number.MAX_VALUE,max:-1*Number.MAX_VALUE,sum:0,values:[],count:0};return(e="object"===C(e)?Object.values(e):e).forEach(e=>{null==(e=t&&"object"===C(e)?e[t]:e)||isNaN(e)||(r.values.push(e),r.sum+=e,e<r.min&&(r.min=e),e>r.max&&(r.max=e),r.count+=1)}),r.domain=[r.min,r.max],r.limits=(e,t)=>Cn(r,e,t),r}function Cn(e,t="equal",o=7){var{min:i,max:s}=e="array"==C(e)?kn(e):e,l=e.values.sort((e,t)=>e-t);if(1===o)return[i,s];var c=[];if("c"===t.substr(0,1)&&(c.push(i),c.push(s)),"e"===t.substr(0,1)){c.push(i);for(let e=1;e<o;e++)c.push(i+e/o*(s-i));c.push(s)}else if("l"===t.substr(0,1)){if(i<=0)throw new Error("Logarithmic scales are only possible for values > 0");var r=Math.LOG10E*yn(i),a=Math.LOG10E*yn(s);c.push(i);for(let e=1;e<o;e++)c.push(wn(10,r+e/o*(a-r)));c.push(s)}else if("q"===t.substr(0,1)){c.push(i);for(let e=1;e<o;e++){var n=(l.length-1)*e/o,d=Sn(n);c.push(d===n?l[d]:l[d]*(1-(n=n-d))+l[d+1]*n)}c.push(s)}else if("k"===t.substr(0,1)){let t;var u=l.length,p=new Array(u),m=new Array(o);let r=!0,e=0,n=null;(n=[]).push(i);for(let e=1;e<o;e++)n.push(i+e/o*(s-i));for(n.push(s);r;){for(let e=0;e<o;e++)m[e]=0;for(let a=0;a<u;a++){var h=l[a];let t=Number.MAX_VALUE,r;for(let e=0;e<o;e++){var f=En(n[e]-h);f<t&&(t=f,r=e),m[r]++,p[a]=r}}var g=new Array(o);for(let e=0;e<o;e++)g[e]=null;for(let e=0;e<u;e++)null===g[t=p[e]]?g[t]=l[e]:g[t]+=l[e];for(let e=0;e<o;e++)g[e]*=1/m[e];r=!1;for(let e=0;e<o;e++)if(g[e]!==n[e]){r=!0;break}n=g,200<++e&&(r=!1)}var b={};for(let e=0;e<o;e++)b[e]=[];for(let e=0;e<u;e++)b[t=p[e]].push(l[e]);let a=[];for(let e=0;e<o;e++)a.push(b[e][0]),a.push(b[e][b[e].length-1]);a=a.sort((e,t)=>e-t),c.push(a[0]);for(let e=1;e<a.length;e+=2){var v=a[e];isNaN(v)||-1!==c.indexOf(v)||c.push(v)}}return c}function xn(e,t,r){return.2126729*Math.pow(e/255,2.4)+.7151522*Math.pow(t/255,2.4)+.072175*Math.pow(r/255,2.4)}var{sqrt:In,pow:b,min:Nn,max:An,atan2:Tn,abs:On,cos:Pn,sin:Ln,exp:Dn,PI:Rn}=Math;var mn={cool(){return cn([I.hsl(180,1,.9),I.hsl(250,.7,.4)])},hot(){return cn(["#000","#f00","#ff0","#fff"]).mode("rgb")}},Mn={OrRd:["#fff7ec","#fee8c8","#fdd49e","#fdbb84","#fc8d59","#ef6548","#d7301f","#b30000","#7f0000"],PuBu:["#fff7fb","#ece7f2","#d0d1e6","#a6bddb","#74a9cf","#3690c0","#0570b0","#045a8d","#023858"],BuPu:["#f7fcfd","#e0ecf4","#bfd3e6","#9ebcda","#8c96c6","#8c6bb1","#88419d","#810f7c","#4d004b"],Oranges:["#fff5eb","#fee6ce","#fdd0a2","#fdae6b","#fd8d3c","#f16913","#d94801","#a63603","#7f2704"],BuGn:["#f7fcfd","#e5f5f9","#ccece6","#99d8c9","#66c2a4","#41ae76","#238b45","#006d2c","#00441b"],YlOrBr:["#ffffe5","#fff7bc","#fee391","#fec44f","#fe9929","#ec7014","#cc4c02","#993404","#662506"],YlGn:["#ffffe5","#f7fcb9","#d9f0a3","#addd8e","#78c679","#41ab5d","#238443","#006837","#004529"],Reds:["#fff5f0","#fee0d2","#fcbba1","#fc9272","#fb6a4a","#ef3b2c","#cb181d","#a50f15","#67000d"],RdPu:["#fff7f3","#fde0dd","#fcc5c0","#fa9fb5","#f768a1","#dd3497","#ae017e","#7a0177","#49006a"],Greens:["#f7fcf5","#e5f5e0","#c7e9c0","#a1d99b","#74c476","#41ab5d","#238b45","#006d2c","#00441b"],YlGnBu:["#ffffd9","#edf8b1","#c7e9b4","#7fcdbb","#41b6c4","#1d91c0","#225ea8","#253494","#081d58"],Purples:["#fcfbfd","#efedf5","#dadaeb","#bcbddc","#9e9ac8","#807dba","#6a51a3","#54278f","#3f007d"],GnBu:["#f7fcf0","#e0f3db","#ccebc5","#a8ddb5","#7bccc4","#4eb3d3","#2b8cbe","#0868ac","#084081"],Greys:["#ffffff","#f0f0f0","#d9d9d9","#bdbdbd","#969696","#737373","#525252","#252525","#000000"],YlOrRd:["#ffffcc","#ffeda0","#fed976","#feb24c","#fd8d3c","#fc4e2a","#e31a1c","#bd0026","#800026"],PuRd:["#f7f4f9","#e7e1ef","#d4b9da","#c994c7","#df65b0","#e7298a","#ce1256","#980043","#67001f"],Blues:["#f7fbff","#deebf7","#c6dbef","#9ecae1","#6baed6","#4292c6","#2171b5","#08519c","#08306b"],PuBuGn:["#fff7fb","#ece2f0","#d0d1e6","#a6bddb","#67a9cf","#3690c0","#02818a","#016c59","#014636"],Viridis:["#440154","#482777","#3f4a8a","#31678e","#26838f","#1f9d8a","#6cce5a","#b6de2b","#fee825"],Spectral:["#9e0142","#d53e4f","#f46d43","#fdae61","#fee08b","#ffffbf","#e6f598","#abdda4","#66c2a5","#3288bd","#5e4fa2"],RdYlGn:["#a50026","#d73027","#f46d43","#fdae61","#fee08b","#ffffbf","#d9ef8b","#a6d96a","#66bd63","#1a9850","#006837"],RdBu:["#67001f","#b2182b","#d6604d","#f4a582","#fddbc7","#f7f7f7","#d1e5f0","#92c5de","#4393c3","#2166ac","#053061"],PiYG:["#8e0152","#c51b7d","#de77ae","#f1b6da","#fde0ef","#f7f7f7","#e6f5d0","#b8e186","#7fbc41","#4d9221","#276419"],PRGn:["#40004b","#762a83","#9970ab","#c2a5cf","#e7d4e8","#f7f7f7","#d9f0d3","#a6dba0","#5aae61","#1b7837","#00441b"],RdYlBu:["#a50026","#d73027","#f46d43","#fdae61","#fee090","#ffffbf","#e0f3f8","#abd9e9","#74add1","#4575b4","#313695"],BrBG:["#543005","#8c510a","#bf812d","#dfc27d","#f6e8c3","#f5f5f5","#c7eae5","#80cdc1","#35978f","#01665e","#003c30"],RdGy:["#67001f","#b2182b","#d6604d","#f4a582","#fddbc7","#ffffff","#e0e0e0","#bababa","#878787","#4d4d4d","#1a1a1a"],PuOr:["#7f3b08","#b35806","#e08214","#fdb863","#fee0b6","#f7f7f7","#d8daeb","#b2abd2","#8073ac","#542788","#2d004b"],Set2:["#66c2a5","#fc8d62","#8da0cb","#e78ac3","#a6d854","#ffd92f","#e5c494","#b3b3b3"],Accent:["#7fc97f","#beaed4","#fdc086","#ffff99","#386cb0","#f0027f","#bf5b17","#666666"],Set1:["#e41a1c","#377eb8","#4daf4a","#984ea3","#ff7f00","#ffff33","#a65628","#f781bf","#999999"],Set3:["#8dd3c7","#ffffb3","#bebada","#fb8072","#80b1d3","#fdb462","#b3de69","#fccde5","#d9d9d9","#bc80bd","#ccebc5","#ffed6f"],Dark2:["#1b9e77","#d95f02","#7570b3","#e7298a","#66a61e","#e6ab02","#a6761d","#666666"],Paired:["#a6cee3","#1f78b4","#b2df8a","#33a02c","#fb9a99","#e31a1c","#fdbf6f","#ff7f00","#cab2d6","#6a3d9a","#ffff99","#b15928"],Pastel2:["#b3e2cd","#fdcdac","#cbd5e8","#f4cae4","#e6f5c9","#fff2ae","#f1e2cc","#cccccc"],Pastel1:["#fbb4ae","#b3cde3","#ccebc5","#decbe4","#fed9a6","#ffffcc","#e5d8bd","#fddaec","#f2f2f2"]},jn=Object.keys(Mn),_n=new Map(jn.map(e=>[e.toLowerCase(),e])),Mn="function"==typeof Proxy?new Proxy(Mn,{get(e,t){t=t.toLowerCase();if(_n.has(t))return e[_n.get(t)]},getOwnPropertyNames(){return Object.getOwnPropertyNames(jn)}}):Mn,l=(...e)=>{var[t,r,a,n]=e=h(e,"cmyk"),e=4<e.length?e[4]:1;return 1===n?[0,0,0,e]:[1<=t?0:255*(1-t)*(1-n),1<=r?0:255*(1-r)*(1-n),1<=a?0:255*(1-a)*(1-n),e]},Bn=Math["max"],$n=(...e)=>{var[e,t,r]=h(e,"rgb"),a=1-Bn(e/=255,Bn(t/=255,r/=255)),n=a<1?1/(1-a):0;return[(1-e-a)*n,(1-t-a)*n,(1-r-a)*n,a]},zn=(g.prototype.cmyk=function(){return $n(this._rgb)},Object.assign(I,{cmyk:(...e)=>new g(...e,"cmyk")}),i.format.cmyk=l,i.autodetect.push({p:2,test:(...e)=>{if("array"===C(e=h(e,"cmyk"))&&4===e.length)return"cmyk"}}),(...e)=>{var t=h(e,"hsla");let r=Yr(e)||"lsa";return t[0]=Qr(t[0]||0)+"deg",t[1]=Qr(100*t[1])+"%",t[2]=Qr(100*t[2])+"%","hsla"===r||3<t.length&&t[3]<1?(t[3]="/ "+(3<t.length?t[3]:1),r="hsla"):t.length=3,`${r.substr(0,3)}(${t.join(" ")})`}),Fn=(...e)=>{var t=h(e,"lab"),e=Yr(e)||"lab";return t[0]=Qr(t[0])+"%",t[1]=Qr(t[1]),t[2]=Qr(t[2]),"laba"===e||3<t.length&&t[3]<1?t[3]="/ "+(3<t.length?t[3]:1):t.length=3,`lab(${t.join(" ")})`},Un=(...e)=>{var t=h(e,"lch"),e=Yr(e)||"lab";return t[0]=Qr(t[0])+"%",t[1]=Qr(t[1]),t[2]=isNaN(t[2])?"none":Qr(t[2])+"deg","lcha"===e||3<t.length&&t[3]<1?t[3]="/ "+(3<t.length?t[3]:1):t.length=3,`lch(${t.join(" ")})`},Gn=(...e)=>{e=h(e,"lab");return e[0]=Qr(100*e[0])+"%",e[1]=ea(e[1]),e[2]=ea(e[2]),3<e.length&&e[3]<1?e[3]="/ "+(3<e.length?e[3]:1):e.length=3,`oklab(${e.join(" ")})`},Vn=(...e)=>{var[e,t,r,...a]=h(e,"rgb"),[e,t,r]=en(e,t,r),[e,t,r]=Da(e,t,r);return[e,t,r,...0<a.length&&a[0]<1?[a[0]]:[]]},Hn=(...e)=>{e=h(e,"lch");return e[0]=Qr(100*e[0])+"%",e[1]=ea(e[1]),e[2]=isNaN(e[2])?"none":Qr(e[2])+"deg",3<e.length&&e[3]<1?e[3]="/ "+(3<e.length?e[3]:1):e.length=3,`oklch(${e.join(" ")})`},qn=Math["round"],Wn=(...e)=>{var t,r=h(e,"rgba");let a=Yr(e)||"rgb";return"hsl"===a.substr(0,3)?zn(Wa(r),a):"lab"===a.substr(0,3)?(e=fa(),ha("d50"),t=Fn(Sa(r),a),ha(e),t):"lch"===a.substr(0,3)?(e=fa(),ha("d50"),t=Un(Ra(r),a),ha(e),t):"oklab"===a.substr(0,5)?Gn(en(r)):"oklch"===a.substr(0,5)?Hn(Vn(r)):(r[0]=qn(r[0]),r[1]=qn(r[1]),r[2]=qn(r[2]),("rgba"===a||3<r.length&&r[3]<1)&&(r[3]="/ "+(3<r.length?r[3]:1),a="rgba"),`${a.substr(0,3)}(${r.slice(0,"rgb"===a?3:4).join(" ")})`)},Kn=(...e)=>{var[e,t,r,...a]=e=h(e,"lch"),[e,t,r]=Aa(e,t,r),[e,t,r]=Qa(e,t,r);return[e,t,r,...0<a.length&&a[0]<1?[a[0]]:[]]},l=/((?:-?\d+)|(?:-?\d+(?:\.\d+)?)%|none)/.source,c=/((?:-?(?:\d+(?:\.\d*)?|\.\d+)%?)|none)/.source,Jn=/((?:-?(?:\d+(?:\.\d*)?|\.\d+)%)|none)/.source,d=/\s*/.source,Yn=/\s+/.source,Zn=/\s*,\s*/.source,Xn=/((?:-?(?:\d+(?:\.\d*)?|\.\d+)(?:deg)?)|none)/.source,Qn=/\s*(?:\/\s*((?:[01]|[01]?\.\d+)|\d+(?:\.\d+)?%))?/.source,eo=new RegExp("^rgba?\\("+d+[l,l,l].join(Yn)+Qn+"\\)$"),to=new RegExp("^rgb\\("+d+[l,l,l].join(Zn)+d+"\\)$"),ro=new RegExp("^rgba\\("+d+[l,l,l,c].join(Zn)+d+"\\)$"),ao=new RegExp("^hsla?\\("+d+[Xn,Jn,Jn].join(Yn)+Qn+"\\)$"),no=new RegExp("^hsl?\\("+d+[Xn,Jn,Jn].join(Zn)+d+"\\)$"),oo=/^hsla\(\s*(-?\d+(?:\.\d+)?),\s*(-?\d+(?:\.\d+)?)%\s*,\s*(-?\d+(?:\.\d+)?)%\s*,\s*([01]|[01]?\.\d+)\)$/,io=new RegExp("^lab\\("+d+[c,c,c].join(Yn)+Qn+"\\)$"),so=new RegExp("^lch\\("+d+[c,c,Xn].join(Yn)+Qn+"\\)$"),lo=new RegExp("^oklab\\("+d+[c,c,c].join(Yn)+Qn+"\\)$"),co=new RegExp("^oklch\\("+d+[c,c,Xn].join(Yn)+Qn+"\\)$"),uo=Math["round"],po=e=>e.map((e,t)=>t<=2?Wr(uo(e),0,255):e),u=(e,t=0,r=100,a=!1)=>("string"==typeof e&&e.endsWith("%")&&(e=parseFloat(e.substring(0,e.length-1))/100,e=a?t+.5*(e+1)*(r-t):t+e*(r-t)),+e),mo=(e,t)=>"none"===e?t:e,l=e=>{if("transparent"===(e=e.toLowerCase().trim()))return[0,0,0,0];let r;if(i.format.named)try{return i.format.named(e)}catch(e){}if(r=(r=e.match(eo))||e.match(to)){let t=r.slice(1,4);for(let e=0;e<3;e++)t[e]=+u(mo(t[e],0),0,255);t=po(t);var a=void 0!==r[4]?+u(r[4],0,1):1;return t[3]=a,t}if(r=e.match(ro)){var t=r.slice(1,5);for(let e=0;e<4;e++)t[e]=+u(t[e],0,255);return t}if(r=(r=e.match(ao))||e.match(no))return(a=r.slice(1,4))[0]=+mo(a[0].replace("deg",""),0),a[1]=.01*+u(mo(a[1],0),0,100),a[2]=.01*+u(mo(a[2],0),0,100),a=po(qa(a)),n=void 0!==r[4]?+u(r[4],0,1):1,a[3]=n,a;if(r=e.match(oo)){var n=r.slice(1,4),o=(n[1]*=.01,n[2]*=.01,qa(n));for(let e=0;e<3;e++)o[e]=uo(o[e]);return o[3]=+r[4],o}return(r=e.match(io))?((a=r.slice(1,4))[0]=u(mo(a[0],0),0,100),a[1]=u(mo(a[1],0),-125,125,!0),a[2]=u(mo(a[2],0),-125,125,!0),n=fa(),ha("d50"),a=po(va(a)),ha(n),n=void 0!==r[4]?+u(r[4],0,1):1,a[3]=n,a):(r=e.match(so))?((n=r.slice(1,4))[0]=u(n[0],0,100),n[1]=u(mo(n[1],0),0,150,!1),n[2]=+mo(n[2].replace("deg",""),0),a=fa(),ha("d50"),n=po(Ta(n)),ha(a),a=void 0!==r[4]?+u(r[4],0,1):1,n[3]=a,n):(r=e.match(lo))?((a=r.slice(1,4))[0]=u(mo(a[0],0),0,1),a[1]=u(mo(a[1],0),-.4,.4,!0),a[2]=u(mo(a[2],0),-.4,.4,!0),n=po(Qa(a)),a=void 0!==r[4]?+u(r[4],0,1):1,n[3]=a,n):(r=e.match(co))?((a=r.slice(1,4))[0]=u(mo(a[0],0),0,1),a[1]=u(mo(a[1],0),0,.4,!1),a[2]=+mo(a[2].replace("deg",""),0),n=po(Kn(a)),e=void 0!==r[4]?+u(r[4],0,1):1,n[3]=e,n):void 0},ho=(l.test=e=>eo.test(e)||ao.test(e)||io.test(e)||so.test(e)||lo.test(e)||co.test(e)||to.test(e)||ro.test(e)||no.test(e)||oo.test(e)||"transparent"===e,l),fo=(g.prototype.css=function(e){return Wn(this._rgb,e)},I.css=(...e)=>new g(...e,"css"),i.format.css=ho,i.autodetect.push({p:5,test:(e,...t)=>{if(!t.length&&"string"===C(e)&&ho.test(e))return"css"}}),i.format.gl=(...e)=>{e=h(e,"rgba");return e[0]*=255,e[1]*=255,e[2]*=255,e},I.gl=(...e)=>new g(...e,"gl"),g.prototype.gl=function(){var e=this._rgb;return[e[0]/255,e[1]/255,e[2]/255,e[3]]},g.prototype.hex=function(e){return da(this._rgb,e)},(I.hex=(...e)=>new g(...e,"hex"),i.format.hex=la,i.autodetect.push({p:4,test:(e,...t)=>{if(!t.length&&"string"===C(e)&&0<=[3,4,5,6,7,8,9].indexOf(e.length))return"hex"}}),Math)["log"]),go=e=>{e/=100;let t,r,a;return a=e<66?(t=255,r=e<6?0:-155.25485562709179-.44596950469579133*(r=e-2)+104.49216199393888*fo(r),e<20?0:.8274096064007395*(a=e-10)-254.76935184120902+115.67994401066147*fo(a)):(t=351.97690566805693+.114206453784165*(t=e-55)-40.25366309332127*fo(t),r=325.4494125711974+.07943456536662342*(r=e-50)-28.0852963507957*fo(r),255),[t,r,a,1]},bo=Math["round"],vo=(...e)=>{var e=h(e,"rgb"),t=e[0],r=e[2];let a=1e3,n=4e4;let o;for(;.4<n-a;){o=.5*(n+a);var i=go(o);i[2]/i[0]>=r/t?n=o:a=o}return bo(o)},Jn=(g.prototype.temp=g.prototype.kelvin=g.prototype.temperature=function(){return vo(this._rgb)},(...e)=>new g(...e,"temp")),N=(Object.assign(I,{temp:Jn,kelvin:Jn,temperature:Jn}),i.format.temp=i.format.kelvin=i.format.temperature=go,g.prototype.oklch=function(){return Vn(this._rgb)},Object.assign(I,{oklch:(...e)=>new g(...e,"oklch")}),i.format.oklch=Kn,i.autodetect.push({p:2,test:(...e)=>{if("array"===C(e=h(e,"oklch"))&&3===e.length)return"oklch"}}),Object.assign(I,{analyze:kn,average:(e,s="lrgb",l=null)=>{var t=e.length;const r=t/(l=l||Array.from(new Array(t)).map(()=>1)).reduce(function(e,t){return e+t});if(l.forEach((e,t)=>{l[t]*=r}),e=e.map(e=>new g(e)),"lrgb"===s){var a=e,n=l,o=a.length,i=[0,0,0,0];for(let e=0;e<a.length;e++){var c=a[e],d=n[e]/o,c=c._rgb;i[0]+=tn(c[0],2)*d,i[1]+=tn(c[1],2)*d,i[2]+=tn(c[2],2)*d,i[3]+=c[3]*d}return i[0]=rn(i[0]),i[1]=rn(i[1]),i[2]=rn(i[2]),.9999999<i[3]&&(i[3]=1),new g(Kr(i))}{var u,p=e.shift();const m=p.get(s),h=[];let n=0,o=0;for(let e=0;e<m.length;e++)m[e]=(m[e]||0)*l[0],h.push(isNaN(m[e])?0:l[0]),"h"!==s.charAt(e)||isNaN(m[e])||(u=m[e]/180*an,n+=nn(u)*l[0],o+=on(u)*l[0]);let i=p.alpha()*l[0];e.forEach((e,t)=>{var r,a=e.get(s);i+=e.alpha()*l[t+1];for(let e=0;e<m.length;e++)isNaN(a[e])||(h[e]+=l[t+1],"h"===s.charAt(e)?(r=a[e]/180*an,n+=nn(r)*l[t+1],o+=on(r)*l[t+1]):m[e]+=a[e]*l[t+1])});for(let t=0;t<m.length;t++)if("h"===s.charAt(t)){let e=sn(o/h[t],n/h[t])/an*180;for(;e<0;)e+=360;for(;360<=e;)e-=360;m[t]=e}else m[t]=m[t]/h[t];return i/=t,new g(m,s).alpha(.99999<i?1:i,!0)}},bezier:e=>{const t=function(e){let a,n,o,i;if(2===(e=e.map(e=>new g(e))).length)[n,o]=e.map(e=>e.lab()),a=function(t){var e=[0,1,2].map(e=>n[e]+t*(o[e]-n[e]));return new g(e,"lab")};else if(3===e.length)[n,o,i]=e.map(e=>e.lab()),a=function(t){var e=[0,1,2].map(e=>(1-t)*(1-t)*n[e]+2*(1-t)*t*o[e]+t*t*i[e]);return new g(e,"lab")};else if(4===e.length){let r;[n,o,i,r]=e.map(e=>e.lab()),a=function(t){var e=[0,1,2].map(e=>(1-t)*(1-t)*(1-t)*n[e]+3*(1-t)*(1-t)*t*o[e]+3*(1-t)*t*t*i[e]+t*t*t*r[e]);return new g(e,"lab")}}else{if(!(5<=e.length))throw new RangeError("No point in running bezier with only one color.");{let t,i,s;t=e.map(e=>e.lab()),s=e.length-1,i=dn(s),a=function(n){const o=1-n;var e=[0,1,2].map(a=>t.reduce((e,t,r)=>e+i[r]*o**(s-r)*n**r*t[a],0));return new g(e,"lab")}}}return a}(e);return t.scale=()=>cn(t),t},blend:a,brewer:Mn,Color:g,colors:oa,contrast:(e,t)=>{e=new g(e),t=new g(t);e=e.luminance(),t=t.luminance();return t<e?(e+.05)/(t+.05):(t+.05)/(e+.05)},contrastAPCA:(e,t)=>{e=new g(e),t=new g(t);var e=xn(...(e=e.alpha()<1?xa(t,e,e.alpha(),"rgb"):e).rgb()),t=xn(...t.rgb()),e=.022<=e?e:e+Math.pow(.022-e,1.414),t=.022<=t?t:t+Math.pow(.022-t,1.414),r=Math.pow(t,.56)-Math.pow(e,.57),a=Math.pow(t,.65)-Math.pow(e,.62),e=Math.abs(t-e)<5e-4?0:e<t?1.14*r:1.14*a;return 100*(Math.abs(e)<.1?0:0<e?e-.027:.027+e)},cubehelix:function(n=300,o=-1.5,i=1,s=1,l=[0,1]){let c=0,d;function t(e){var t=ta*((n+120)/360+o*e),r=hn(l[0]+d*e,s),e=(0!==c?i[0]+e*c:i)*r*(1-r)/2,a=gn(t),t=fn(t);return I(Kr([255*(r+e*(-.14861*a+1.78277*t)),255*(r+e*(-.29227*a-.90649*t)),255*(r+1.97294*a*e),1]))}return"array"===C(l)?d=l[1]-l[0]:(d=0,l=[l,l]),t.start=function(e){return null==e?n:(n=e,t)},t.rotations=function(e){return null==e?o:(o=e,t)},t.gamma=function(e){return null==e?s:(s=e,t)},t.hue=function(e){return null==e?i:("array"===C(i=e)?0===(c=i[1]-i[0])&&(i=i[1]):c=0,t)},t.lightness=function(e){return null==e?l:(d="array"===C(e)?(l=e)[1]-e[0]:(l=[e,e],0),t)},t.scale=()=>I.scale(t),t.hue(i),t},deltaE:function(e,t,r=1,a=1,n=1){function o(e){return 360*e/(2*Rn)}function i(e){return 2*Rn*e/360}e=new g(e),t=new g(t);var[e,s,l]=Array.from(e.lab()),[t,c,d]=Array.from(t.lab()),u=(e+t)/2,p=(In(b(s,2)+b(l,2))+In(b(c,2)+b(d,2)))/2,p=.5*(1-In(b(p,7)/(b(p,7)+b(25,7)))),c=c*(1+p),p=In(b(s=s*(1+p),2)+b(l,2)),m=In(b(c,2)+b(d,2)),h=(p+m)/2,l=o(Tn(l,s)),s=o(Tn(d,c)),l=180<On((d=0<=l?l:l+360)-(c=0<=s?s:s+360))?(d+c+360)/2:(d+c)/2,s=1-.17*Pn(i(l-30))+.24*Pn(i(2*l))+.32*Pn(i(3*l+6))-.2*Pn(i(4*l-63)),f=On(f=c-d)<=180?f:c<=d?360+f:f-360,c=(f=2*In(p*m)*Ln(i(f)/2),t-e),d=m-p,t=1+.015*b(u-50,2)/In(20+b(u-50,2)),e=1+.045*h,m=1+.015*h*s,p=30*Dn(-b((l-275)/25,2)),u=-(2*In(b(h,7)/(b(h,7)+b(25,7))))*Ln(2*i(p)),s=In(b(c/(r*t),2)+b(d/(a*e),2)+b(f/(n*m),2)+d/(a*e)*u*(f/(n*m)));return An(0,Nn(100,s))},distance:function(e,t,r="lab"){e=new g(e),t=new g(t);var a,n=e.get(r),o=t.get(r);let i=0;for(a in n){var s=(n[a]||0)-(o[a]||0);i+=s*s}return Math.sqrt(i)},input:i,interpolate:xa,limits:Cn,mix:xa,random:()=>{let t="#";for(let e=0;e<6;e++)t+="0123456789abcdef".charAt(bn(16*vn()));return new g(t,"hex")},scale:cn,scales:mn,valid:(...e)=>{try{return new g(...e),!0}catch(e){return!1}}}),(t,e)=>{t=localStorage.getItem(t);if(!t)return e;try{return JSON.parse(t)}catch(e){return t}}),yo=e=>{var t,r,a={section:/^\s*\[\s*([^\]]*)\s*\]\s*$/,param:/^\s*([^=]+?)\s*=\s*(.*?)\s*$/,comment:/^\s*;.*$/},n={};let o=null;for(const i of e.split(/[\r\n]+/))a.comment.test(i)||(a.param.test(i)?i.includes("xrdb")||(t=i.match(a.param),o&&t&&3===t.length&&(r=t[1].trim(),t=t[2].split(";")[0].trim(),n[o]||(n[o]={}),n[o][r]=t)):a.section.test(i)&&(r=i.match(a.section))&&(n[o=r[1]]={}));return n},wo=e=>{var t=document.querySelector("style.marketplaceSnippets"),t=(t&&t.remove(),document.createElement("style")),e=e.reduce((e,t)=>`${e}/* ${t.title} - ${t.description} */
${t.code}
`,"");t.innerHTML=e,t.classList.add("marketplaceSnippets"),document.body.appendChild(t)},So=(e,t)=>{let r=[];return e&&0<e.length?r=e.map(e=>({name:e.name,url:Po(e.url)})):r.push({name:t,url:"https://github.com/"+t}),r},Eo=e=>e?Object.keys(e).map(e=>({key:e,value:e})):[],ko=e=>[{key:"stars",value:e("grid.sort.stars")},{key:"newest",value:e("grid.sort.newest")},{key:"oldest",value:e("grid.sort.oldest")},{key:"lastUpdated",value:e("grid.sort.lastUpdated")},{key:"mostStale",value:e("grid.sort.mostStale")},{key:"a-z",value:e("grid.sort.aToZ")},{key:"z-a",value:e("grid.sort.zToA")}],Co=(...e)=>{console.debug("Resetting Marketplace");var t=[];if(0===e.length)for(const r in localStorage)r.startsWith("marketplace:")&&t.push(r);for(const a of e)switch(a){case"extensions":t.push(...N(x.installedExtensions,[])),t.push(x.installedExtensions);break;case"snippets":t.push(...N(x.installedSnippets,[])),t.push(x.installedSnippets);break;case"theme":t.push(...N(x.installedThemes,[])),t.push(x.installedThemes),t.push(x.themeInstalled);break;default:console.error("Unknown category: "+a)}for(const n of t)localStorage.removeItem(n),console.debug("Removed "+n);console.debug("Marketplace has been reset"),location.reload()},xo=t=>{var r=document.querySelector("style.marketplaceCSS.marketplaceScheme");if(r&&r.remove(),t){r=document.createElement("style");r.classList.add("marketplaceCSS"),r.classList.add("marketplaceScheme");let e=":root {";for(const a of Object.keys(t))e=(e+=`--spice-${a}: #${t[a]};`)+`--spice-rgb-${a}: ${(e=>{e=3===e.length?e.split("").map(e=>e+e).join(""):e;if(6!==e.length)throw"Only 3- or 6-digit hex colours are allowed.";if(e.match(/[^0-9a-f]/i))throw"Only hex colours are allowed.";e=e.match(/.{1,2}/g);if(e&&3===e.length)return[Number.parseInt(e[0],16),Number.parseInt(e[1],16),Number.parseInt(e[2],16)];throw"Could not parse hex colour."})(t[a])};`;e+="}",r.innerHTML=e,document.body.appendChild(r)}},Io=e=>{try{var t,r,a=document.querySelector("link[href='user.css']"),n=(a&&a.remove(),document.querySelector("style.marketplaceCSS.marketplaceUserCSS"));n&&n.remove(),e?((t=document.createElement("style")).classList.add("marketplaceCSS"),t.classList.add("marketplaceUserCSS"),t.innerHTML=e,document.body.appendChild(t)):((r=document.createElement("link")).setAttribute("rel","stylesheet"),r.setAttribute("href","user.css"),r.classList.add("userCSS"),document.body.appendChild(r))}catch(e){console.warn(e)}},No=async(e,t)=>{if(!e.cssURL)throw new Error("No CSS URL provided");var t=t||await async function(){for(const e of["net","xyz"])try{if("opaqueredirect"===(await fetch("https://cdn.jsdelivr."+e,{redirect:"manual",cache:"no-cache"})).type)return e}catch(e){console.error(e)}}(),t=(e=>{const t=new URL(e);return t.host,t.host==="raw.githubusercontent.com"})(e.cssURL)?`https://cdn.jsdelivr.${t}/gh/${e.user}/${e.repo}@${e.branch}/`+e.manifest.usercss:e.cssURL,r=t.replace("/user.css","/assets/");console.debug("Parsing CSS: ",t);let a=await fetch(t+"?time="+Date.now()).then(e=>e.text());for(const i of a.matchAll(/url\(['|"](?<path>.+?)['|"]\)/gm)||[]){var n,o=i?.groups?.path;!o||o.startsWith("http")||o.startsWith("data")||(n=r+o.replace(/\.\//g,""),a=a.replace(o,n))}return a};function Ao(e,t){if(e)for(const n of e){var r=t||n.user+"-"+n.repo,a=window.sessionStorage.getItem(r),a=a?JSON.parse(a):[];a.push(n),window.sessionStorage.setItem(r,JSON.stringify(a))}}async function To(e,t,r){try{var a={text:e,context:t+"/"+r,mode:"gfm"},n=await fetch("https://api.github.com/markdown",{method:"POST",body:JSON.stringify(a)});if(n.ok)return await n.text();throw Spicetify.showNotification(y("notifications.markdownParsingError",{status:n.status}),!0)}catch(e){return null}}function Oo(e){var t="snippet"===e.type?"snippet:":`${e.item.user}/${e.item.repo}/`;let r;switch(e.type){case"snippet":r=e.item.title.replaceAll(" ","-");break;case"theme":r=e.item.manifest?.usercss||"";break;case"extension":r=e.item.manifest?.main||"";break;case"app":r=e.item.manifest?.name?.replaceAll(" ","-")||""}return"marketplace:installed:"+t+r}var Po=e=>{var t=decodeURI(e).trim().toLowerCase();return t.startsWith("javascript:")||t.startsWith("data:")||t.startsWith("vbscript:")?"about:blank":e},Lo=(e,t)=>{e=e.title||e?.manifest?.name||"",t=t.title||t?.manifest?.name||"";return e.localeCompare(t)},Do=(e,t)=>{return void 0===e.created||void 0===t.created?0:(e=new Date(e.created),new Date(t.created).getTime()-e.getTime())},Ro=(e,t)=>{return void 0===e.lastUpdated||void 0===t.lastUpdated?0:(e=new Date(e.lastUpdated),new Date(t.lastUpdated).getTime()-e.getTime())},Mo=(e,t)=>{switch(t){case"a-z":e.sort((e,t)=>Lo(e,t));break;case"z-a":e.sort((e,t)=>Lo(t,e));break;case"newest":e.sort((e,t)=>Do(e,t));break;case"oldest":e.sort((e,t)=>Do(t,e));break;case"lastUpdated":e.sort((e,t)=>Ro(e,t));break;case"mostStale":e.sort((e,t)=>Ro(t,e));break;default:e.sort((e,t)=>t.stars-e.stars)}};async function jo(e,t=1,r=[],a=!1){let n=`https://api.github.com/search/repositories?q=${encodeURIComponent("topic:"+e)}&per_page=100`;t&&(n+="&page="+t);var o=JSON.parse(window.sessionStorage.getItem(e+"-page-"+t)||"null")||await fetch(n).then(e=>e.json()).catch(()=>null);return o?.items?(window.sessionStorage.setItem(e+"-page-"+t,JSON.stringify(o)),{...o,page_count:o.items.length,items:o.items.filter(e=>!r.includes(e.html_url)&&(a||!e.archived))}):(Spicetify.showNotification(y("notifications.tooManyRequests"),!0,5e3),{items:[]})}var Zn=new Blob([`
  self.addEventListener('message', async (event) => {
    const url = event.data;
    const response = await fetch(url);
    const data = await response.json().catch(() => null);
    self.postMessage(data);
  });
`],{type:"application/javascript"}),_o=URL.createObjectURL(Zn);async function Bo(e,t,r){var a=e+"-"+t,n=window.sessionStorage.getItem(a),o=JSON.parse(window.sessionStorage.getItem("noManifests")||"[]");if(n)return JSON.parse(n);n=`https://raw.githubusercontent.com/${e}/${t}/${r}/manifest.json`;if(o.includes(n))return null;let i=await async function(e){const a=new Worker(_o);return new Promise(t=>{const r=e=>{a.terminate(),t(e)};a.postMessage(e),a.addEventListener("message",e=>r(e.data),{once:!0}),a.addEventListener("error",()=>r(null),{once:!0})})}(n);return i?(Ao(i=Array.isArray(i)?i:[i],a),i):Ao([n],"noManifests")}async function $o(e,a,n,o=!1){try{var t=e.match(/https:\/\/api\.github\.com\/repos\/(?<user>.+)\/(?<repo>.+)\/contents/);if(!t||!t.groups)return null;const{user:i,repo:s}=t.groups;return(await Bo(i,s,a)).reduce((e,t)=>{var r;return t?.name&&t.description&&t.main&&(r=t.branch||a,r={manifest:t,title:t.name,subtitle:t.description,authors:So(t.authors,i),user:i,repo:s,branch:r,imageURL:t.preview?.startsWith("http")?t.preview:`https://raw.githubusercontent.com/${i}/${s}/${r}/`+t.preview,extensionURL:t.main.startsWith("http")?t.main:`https://raw.githubusercontent.com/${i}/${s}/${r}/`+t.main,readmeURL:t.readme?.startsWith("http")?t.readme:`https://raw.githubusercontent.com/${i}/${s}/${r}/`+t.readme,stars:n,tags:t.tags},o&&localStorage.getItem(`marketplace:installed:${i}/${s}/`+t.main)||e.push(r)),e},[])}catch{return null}}async function zo(e,a,n){try{var t=e.match(/https:\/\/api\.github\.com\/repos\/(?<user>.+)\/(?<repo>.+)\/contents/);if(!t||!t.groups)return null;const{user:o,repo:i}=t.groups;return(await Bo(o,i,a)).reduce((e,t)=>{var r;return t?.name&&t?.usercss&&t?.description&&(r=t.branch||a,r={manifest:t,title:t.name,subtitle:t.description,authors:So(t.authors,o),user:o,repo:i,branch:r,imageURL:t.preview?.startsWith("http")?t.preview:`https://raw.githubusercontent.com/${o}/${i}/${r}/`+t.preview,readmeURL:t.readme?.startsWith("http")?t.readme:`https://raw.githubusercontent.com/${o}/${i}/${r}/`+t.readme,stars:n,tags:t.tags,cssURL:t.usercss.startsWith("http")?t.usercss:`https://raw.githubusercontent.com/${o}/${i}/${r}/`+t.usercss,schemesURL:t.schemes?t.schemes.startsWith("http")?t.schemes:`https://raw.githubusercontent.com/${o}/${i}/${r}/`+t.schemes:null,include:t.include},e.push(r)),e},[])}catch{return null}}async function Fo(e,a,n){try{var t=e.match(/https:\/\/api\.github\.com\/repos\/(?<user>.+)\/(?<repo>.+)\/contents/);if(!t||!t.groups)return null;const{user:o,repo:i}=t.groups;return(await Bo(o,i,a)).reduce((e,t)=>{var r;return t?.name&&t.description&&!t.main&&!t.usercss&&(r=t.branch||a,r={manifest:t,title:t.name,subtitle:t.description,authors:So(t.authors,o),user:o,repo:i,branch:r,imageURL:t.preview?.startsWith("http")?t.preview:`https://raw.githubusercontent.com/${o}/${i}/${r}/`+t.preview,readmeURL:t.readme?.startsWith("http")?t.readme:`https://raw.githubusercontent.com/${o}/${i}/${r}/`+t.readme,stars:n,tags:t.tags},e.push(r)),e},[])}catch{return null}}var Uo=async()=>{return(await fetch("https://raw.githubusercontent.com/spicetify/marketplace/main/resources/blacklist.json").then(e=>e.json()).catch(()=>({}))).repos},Go=async(r=!1)=>{var e=await fetch("https://raw.githubusercontent.com/spicetify/marketplace/main/resources/snippets.json").then(e=>e.json()).catch(()=>[]);return e.length?e.reduce((e,t)=>{t={...t};return t.preview&&(t.imageURL=t.preview.startsWith("http")?t.preview:"https://raw.githubusercontent.com/spicetify/spicetify-marketplace/main/"+t.preview,t.preview=void 0),r&&localStorage.getItem("marketplace:installed:snippet:"+t.title.replaceAll(" ","-"))||e.push(t),e},[]):[]},Vo=t(r()),Ho=t(F()),qo=t(r()),Wo=t(U()),Ko=(Prism.languages.json={property:{pattern:/(^|[^\\])"(?:\\.|[^\\"\r\n])*"(?=\s*:)/,lookbehind:!0,greedy:!0},string:{pattern:/(^|[^\\])"(?:\\.|[^\\"\r\n])*"(?!\s*:)/,lookbehind:!0,greedy:!0},comment:{pattern:/\/\/.*|\/\*[\s\S]*?(?:\*\/|$)/,greedy:!0},number:/-?\b\d+(?:\.\d+)?(?:e[+-]?\d+)?\b/i,punctuation:/[{}[\],]/,operator:/:/,boolean:/\b(?:false|true)\b/,null:{pattern:/\bnull\b/,alias:"keyword"}},Prism.languages.webmanifest=Prism.languages.json,t(r())),Jo="button-module__button___hf2qg_marketplace",Yo="button-module__circle___EZ88P_marketplace",Zo=e=>{var t=e.type||"round",r=[Jo];return"circle"===t&&r.push(Yo),e.classes&&r.push(...e.classes),Ko.default.createElement("button",{className:r.join(" "),onClick:e.onClick,"aria-label":e.label||"",disabled:e.disabled},e.children)},Xo=()=>{const[e,t]=qo.default.useState("");const r=t=>{if(t){let e;try{e=JSON.parse(t)}catch(e){return void Spicetify.showNotification(y("backupModal.invalidJSON"))}var r=e;console.debug("Importing Marketplace"),Co();for(const a in r)localStorage.setItem(a,r[a]),console.debug("Imported "+a);location.reload()}else Spicetify.showNotification(y("backupModal.noDataPasted"))};return qo.default.createElement("div",{id:"marketplace-backup-container"},qo.default.createElement("div",{className:"marketplace-backup-input-container"},qo.default.createElement("label",{htmlFor:"marketplace-backup"},y("backupModal.inputLabel")),qo.default.createElement("div",{className:"marketplace-code-editor-wrapper marketplace-code-editor"},qo.default.createElement(Wo.default,{value:e,onValueChange:e=>t(e),highlight:e=>(0,Ho.highlight)(e,Ho.languages.css),textareaId:"marketplace-import-text",textareaClassName:"import-textarea",readOnly:!1,className:"marketplace-code-editor-textarea",placeholder:y("backupModal.inputPlaceholder"),style:{}}))),qo.default.createElement(qo.default.Fragment,null,qo.default.createElement(Zo,{classes:["marketplace-backup-button"],onClick:()=>{var e=(()=>{var e={};for(const t in localStorage)t.startsWith("marketplace:")&&(e[t]=localStorage.getItem(t));return e})();Spicetify.Platform.ClipboardAPI.copy(JSON.stringify(e)),Spicetify.showNotification(y("backupModal.settingsCopied")),Spicetify.PopupModal.hide()}},y("backupModal.exportBtn")),qo.default.createElement(Zo,{classes:["marketplace-backup-button"],onClick:()=>{r(e)}},y("backupModal.importBtn")),qo.default.createElement(Zo,{classes:["marketplace-backup-button"],onClick:async()=>{var e=await(await(await window.showOpenFilePicker())[0].getFile()).text();r(e)}},y("backupModal.fileImportBtn"))))},Qo=t(r()),ei=()=>Qo.default.createElement("div",{id:"marketplace-reload-container"},Qo.default.createElement("p",null,y("reloadModal.description")),Qo.default.createElement("div",{className:"marketplace-reload-modal__button-container"},Qo.default.createElement(Zo,{onClick:()=>{Spicetify.PopupModal.hide(),location.reload()}},y("reloadModal.reloadNow")),Qo.default.createElement(Zo,{onClick:()=>{Spicetify.PopupModal.hide()}},y("reloadModal.reloadLater")))),p=t(r()),ti=t(r()),ri=t(r()),ai=()=>ri.default.createElement("svg",{height:"16",width:"16",className:"Svg-sc-ytk21e-0 uPxdw nW1RKQOkzcJcX6aDCZB4",viewBox:"0 0 16 16",role:"img","aria-label":"Tooltip Icon"},ri.default.createElement("path",{d:"M8 1.5a6.5 6.5 0 100 13 6.5 6.5 0 000-13zM0 8a8 8 0 1116 0A8 8 0 010 8z"}),ri.default.createElement("path",{d:"M7.25 12.026v-1.5h1.5v1.5h-1.5zm.884-7.096A1.125 1.125 0 007.06 6.39l-1.431.448a2.625 2.625 0 115.13-.784c0 .54-.156 1.015-.503 1.488-.3.408-.7.652-.973.818l-.112.068c-.185.116-.26.203-.302.283-.046.087-.097.245-.097.57h-1.5c0-.47.072-.898.274-1.277.206-.385.507-.645.827-.846l.147-.092c.285-.177.413-.257.526-.41.169-.23.213-.397.213-.602 0-.622-.503-1.125-1.125-1.125z"})),ni=t(r()),oi=t(Be()),ii=t=>{var e=t.sortBoxOptions.map(e=>({value:e.key,label:e.value})),r=t.sortBoxOptions.find(t.sortBySelectedFn);return ni.default.createElement("div",{className:"marketplace-sortBox"},ni.default.createElement("div",{className:"marketplace-sortBox-header"},ni.default.createElement("div",{className:"marketplace-sortBox-header-title"}),ni.default.createElement(oi.default,{placeholder:"Select an option",options:e,value:r?.key,onChange:e=>{t.onChange(e.value)}})))},si=t(r()),li="toggle-module__toggle-wrapper___ocE5z_marketplace",ci="toggle-module__disabled___OYAYf_marketplace",di="toggle-module__toggle-input___ceLM4_marketplace",ui="toggle-module__toggle-indicator-wrapper___6Lcp0_marketplace",pi="toggle-module__toggle-indicator___nCxwE_marketplace",mi=e=>{var t="toggle:"+e.storageKey,r=[li];return!1===e.clickable&&r.push(ci),si.default.createElement("label",{className:r.join(" ")},si.default.createElement("input",{className:di,type:"checkbox",checked:e.enabled,"data-storage-key":e.storageKey,id:t,title:"Toggle for "+e.storageKey,onChange:e.onChange}),si.default.createElement("span",{className:ui},si.default.createElement("span",{className:pi})))},hi=t(r());function fi({children:e,...t}){return Spicetify.ReactComponent.TooltipWrapper?hi.default.createElement(Spicetify.ReactComponent.TooltipWrapper,{...t},e):e}window.Spicetify;var gi=r=>{var e=r.type,t="dropdown"===e?"dropdown:"+r.storageKey:"toggle:"+r.storageKey,a=!!r.modalConfig.visual[r.storageKey];return void 0!==r.description&&null!==r.description||(r.description=""),"dropdown"===e&&r.options?ti.default.createElement("div",{className:"settings-row"},ti.default.createElement("label",{htmlFor:t,className:"col description"},r.name),ti.default.createElement("div",{className:"col action"},ti.default.createElement(ii,{sortBoxOptions:r.options.map(e=>({key:e,value:e})),onChange:e=>{return e=e,t=r.storageKey,r.modalConfig.visual[t]=e,localStorage.setItem("marketplace:"+t,String(e)),void r.updateConfig(r.modalConfig);var t},sortBySelectedFn:e=>e.key===r.modalConfig.visual[r.storageKey]}),ti.default.createElement(fi,{label:r.description.split("\n").map(e=>ti.default.createElement("span",{key:e},e,ti.default.createElement("br",null))),renderInline:!0,showDelay:10,placement:"top",labelClassName:"marketplace-settings-tooltip",disabled:!1},ti.default.createElement("div",{className:"marketplace-tooltip-icon"},ti.default.createElement(ai,null))))):ti.default.createElement("div",{className:"settings-row"},ti.default.createElement("label",{htmlFor:t,className:"col description"},r.name),ti.default.createElement("div",{className:"col action"},ti.default.createElement(mi,{name:r.name,storageKey:r.storageKey,enabled:a,onChange:e=>{var t=e.target.checked,e=e.target.dataset.storageKey;r.modalConfig.visual[e]=t,console.debug(`toggling ${e} to `+t),localStorage.setItem("marketplace:"+e,String(t)),r.updateConfig(r.modalConfig)}})))},bi=t(r()),W=t(r());function vi(e,t){return(vi=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function yi(e,t){e.prototype=Object.create(t.prototype),vi(e.prototype.constructor=e,t)}function j(){return(j=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r,a=arguments[t];for(r in a)Object.prototype.hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(this,arguments)}function wi(e){return(wi="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function Si(e){e=function(e,t){if("object"!=wi(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0===r)return("string"===t?String:Number)(e);if("object"!=wi(r=r.call(e,t||"default")))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}(e,"string");return"symbol"==wi(e)?e:e+""}function Ei(t,e){var r,a=Object.keys(t);return Object.getOwnPropertySymbols&&(r=Object.getOwnPropertySymbols(t),e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),a.push.apply(a,r)),a}function ki(a){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?Ei(Object(n),!0).forEach(function(e){var t,r;t=a,r=n[e=e],(e=Si(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(n)):Ei(Object(n)).forEach(function(e){Object.defineProperty(a,e,Object.getOwnPropertyDescriptor(n,e))})}return a}function Ci(){return Math.random().toString(36).substring(7).split("").join(".")}var xi="function"==typeof Symbol&&Symbol.observable||"@@observable",Ii={INIT:"@@redux/INIT"+Ci(),REPLACE:"@@redux/REPLACE"+Ci(),PROBE_UNKNOWN_ACTION:function(){return"@@redux/PROBE_UNKNOWN_ACTION"+Ci()}};function Ni(e){if(void 0===e)return"undefined";if(null===e)return"null";var t=typeof e;switch(t){case"boolean":case"string":case"number":case"symbol":case"function":return t}if(Array.isArray(e))return"array";if((r=e)instanceof Date||"function"==typeof r.toDateString&&"function"==typeof r.getDate&&"function"==typeof r.setDate)return"date";if((r=e)instanceof Error||"string"==typeof r.message&&r.constructor&&"number"==typeof r.constructor.stackTraceLimit)return"error";var r,a="function"==typeof(r=e).constructor?r.constructor.name:null;switch(a){case"Symbol":case"Promise":case"WeakMap":case"WeakSet":case"Map":case"Set":return a}return t.slice(8,-1).toLowerCase().replace(/\s/g,"")}function Ai(e){return Ni(e)}function Ti(e,t,r){var a;if("function"==typeof t&&"function"==typeof r||"function"==typeof r&&"function"==typeof arguments[3])throw new Error("It looks like you are passing several store enhancers to createStore(). This is not supported. Instead, compose them together to a single function. See https://redux.js.org/tutorials/fundamentals/part-4-store#creating-a-store-with-enhancers for an example.");if("function"==typeof t&&void 0===r&&(r=t,t=void 0),void 0!==r){if("function"!=typeof r)throw new Error("Expected the enhancer to be a function. Instead, received: '"+Ai(r)+"'");return r(Ti)(e,t)}if("function"!=typeof e)throw new Error("Expected the root reducer to be a function. Instead, received: '"+Ai(e)+"'");var n=e,o=t,i=[],s=i,l=!1;function c(){s===i&&(s=i.slice())}function d(){if(l)throw new Error("You may not call store.getState() while the reducer is executing. The reducer has already received the state as an argument. Pass it down from the top reducer instead of reading it from the store.");return o}function u(t){if("function"!=typeof t)throw new Error("Expected the listener to be a function. Instead, received: '"+Ai(t)+"'");if(l)throw new Error("You may not call store.subscribe() while the reducer is executing. If you would like to be notified after the store has been updated, subscribe from a component and invoke store.getState() in the callback to access the latest state. See https://redux.js.org/api/store#subscribelistener for more details.");var r=!0;return c(),s.push(t),function(){if(r){if(l)throw new Error("You may not unsubscribe from a store listener while the reducer is executing. See https://redux.js.org/api/store#subscribelistener for more details.");r=!1,c();var e=s.indexOf(t);s.splice(e,1),i=null}}}function p(e){if(!function(e){if("object"==typeof e&&null!==e){for(var t=e;null!==Object.getPrototypeOf(t);)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(e)===t}}(e))throw new Error("Actions must be plain objects. Instead, the actual type was: '"+Ai(e)+"'. You may need to add middleware to your store setup to handle dispatching other values, such as 'redux-thunk' to handle dispatching functions. See https://redux.js.org/tutorials/fundamentals/part-4-store#middleware and https://redux.js.org/tutorials/fundamentals/part-6-async-logic#using-the-redux-thunk-middleware for examples.");if(void 0===e.type)throw new Error('Actions may not have an undefined "type" property. You may have misspelled an action type string constant.');if(l)throw new Error("Reducers may not dispatch actions.");try{l=!0,o=n(o,e)}finally{l=!1}for(var t=i=s,r=0;r<t.length;r++)(0,t[r])();return e}return p({type:Ii.INIT}),(a={dispatch:p,subscribe:u,getState:d,replaceReducer:function(e){if("function"!=typeof e)throw new Error("Expected the nextReducer to be a function. Instead, received: '"+Ai(e));n=e,p({type:Ii.REPLACE})}})[xi]=function(){var r=u,e={subscribe:function(e){if("object"!=typeof e||null===e)throw new Error("Expected the observer to be an object. Instead, received: '"+Ai(e)+"'");function t(){e.next&&e.next(d())}return t(),{unsubscribe:r(t)}}};return e[xi]=function(){return this},e},a}function Oi(e,t){return function(){return t(e.apply(this,arguments))}}function Pi(e,t){if("function"==typeof e)return Oi(e,t);if("object"!=typeof e||null===e)throw new Error("bindActionCreators expected an object or a function, but instead received: '"+Ai(e)+`'. Did you write "import ActionCreators from" instead of "import * as ActionCreators from"?`);var r,a={};for(r in e){var n=e[r];"function"==typeof n&&(a[r]=Oi(n,t))}return a}function Li(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return 0===t.length?function(e){return e}:1===t.length?t[0]:t.reduce(function(e,t){return function(){return e(t.apply(void 0,arguments))}})}var Di=t(r()),d=t(qe()),Ri=t(r()).default.createContext(null);Ri.displayName="ReactRedux";function Mi(){return ji}var ji=function(e){e()};var _i={notify:function(){},get:function(){return[]}};function Bi(t,r){var o,i=_i;function s(){e.onStateChange&&e.onStateChange()}function a(){var e,a,n;o||(o=r?r.addNestedSub(s):t.subscribe(s),e=Mi(),n=a=null,i={clear:function(){n=a=null},notify:function(){e(function(){for(var e=a;e;)e.callback(),e=e.next})},get:function(){for(var e=[],t=a;t;)e.push(t),t=t.next;return e},subscribe:function(e){var t=!0,r=n={callback:e,next:null,prev:n};return r.prev?r.prev.next=r:a=r,function(){t&&null!==a&&(t=!1,r.next?r.next.prev=r.prev:n=r.prev,r.prev?r.prev.next=r.next:a=r.next)}}})}var e={addNestedSub:function(e){return a(),i.subscribe(e)},notifyNestedSubs:function(){i.notify()},handleChangeWrapper:s,isSubscribed:function(){return Boolean(o)},trySubscribe:a,tryUnsubscribe:function(){o&&(o(),o=void 0,i.clear(),i=_i)},getListeners:function(){return i}};return e}var c=t(r()),$i="undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement?c.useLayoutEffect:c.useEffect;function zi(e){var t=e.store,r=e.context,e=e.children,a=(0,Di.useMemo)(function(){var e=Bi(t);return{store:t,subscription:e}},[t]),n=(0,Di.useMemo)(function(){return t.getState()},[t]),r=($i(function(){var e=a.subscription;return e.onStateChange=e.notifyNestedSubs,e.trySubscribe(),n!==t.getState()&&e.notifyNestedSubs(),function(){e.tryUnsubscribe(),e.onStateChange=null}},[a,n]),r||Ri);return Di.default.createElement(r.Provider,{value:a},e)}zi.propTypes={store:d.default.shape({subscribe:d.default.func.isRequired,dispatch:d.default.func.isRequired,getState:d.default.func.isRequired}),context:d.default.object,children:d.default.any};var Fi=zi;function Ui(e,t){if(null==e)return{};for(var r,a={},n=Object.keys(e),o=0;o<n.length;o++)r=n[o],0<=t.indexOf(r)||(a[r]=e[r]);return a}var Gi=t(We()),A=t(r()),Vi=t(Je()),Hi=["getDisplayName","methodName","renderCountProp","shouldHandleStateChanges","storeKey","withRef","forwardRef","context"],qi=["reactReduxForwardedRef"],Wi=[],Ki=[null,null],Ji=function(t){try{return JSON.stringify(t)}catch(e){return String(t)}};function Yi(e,t){e=e[1];return[t.payload,e+1]}function Zi(e,t,r){$i(function(){return e.apply(void 0,t)},r)}function Xi(e,t,r,a,n,o,i){e.current=a,t.current=n,r.current=!1,o.current&&(o.current=null,i())}function Qi(e,a,t,n,o,i,s,l,c,d){var u,p;if(e)return u=!1,p=null,t.onStateChange=e=function(){if(!u){var e,t,r=a.getState();try{e=n(r,o.current)}catch(e){p=t=e}t||(p=null),e===i.current?s.current||c():(i.current=e,l.current=e,s.current=!0,d({type:"STORE_UPDATED",payload:{error:t}}))}},t.trySubscribe(),e(),function(){if(u=!0,t.tryUnsubscribe(),t.onStateChange=null,p)throw p}}var es=function(){return[null,0]};function ts(C,e){var e=e=void 0===e?{}:e,t=e.getDisplayName,a=void 0===t?function(e){return"ConnectAdvanced("+e+")"}:t,t=e.methodName,n=void 0===t?"connectAdvanced":t,t=e.renderCountProp,o=void 0===t?void 0:t,t=e.shouldHandleStateChanges,x=void 0===t||t,t=e.storeKey,i=void 0===t?"store":t,t=e.withRef,t=void 0!==t&&t,r=e.forwardRef,s=void 0!==r&&r,r=e.context,r=void 0===r?Ri:r,l=Ui(e,Hi);if(void 0!==o)throw new Error("renderCountProp is removed. render counting is built into the latest React Dev Tools profiling extension");if(t)throw new Error("withRef is removed. To access the wrapped instance, use a ref on the connected component");if("store"!==i)throw new Error("storeKey has been removed and does not do anything. To use a custom Redux store for specific components, create a custom React context with React.createContext(), and pass the context object to React Redux's Provider and specific components like: <Provider context={MyContext}><ConnectedComponent context={MyContext} /></Provider>. You may also pass a {context : MyContext} option to connect");var I=r;return function(w){var S,E,k,r,e;if((0,Vi.isValidElementType)(w))return e=w.displayName||w.name||"Component",S=a(e),E=j({},l,{getDisplayName:a,methodName:n,renderCountProp:o,shouldHandleStateChanges:x,storeKey:i,displayName:S,wrappedComponentName:e,WrappedComponent:w}),e=l.pure,k=e?A.useMemo:function(e){return e()},(r=e?A.default.memo(t):t).WrappedComponent=w,r.displayName=t.displayName=S,s?((e=A.default.forwardRef(function(e,t){return A.default.createElement(r,j({},e,{reactReduxForwardedRef:t}))})).displayName=S,e.WrappedComponent=w,(0,Gi.default)(e,w)):(0,Gi.default)(r,w);throw new Error("You must pass a component to the function returned by "+n+". Instead received "+Ji(w));function t(r){var e=(0,A.useMemo)(function(){var e=r.reactReduxForwardedRef,t=Ui(r,qi);return[r.context,e,t]},[r]),t=e[0],a=e[1],n=e[2],o=(0,A.useMemo)(function(){return t&&t.Consumer&&(0,Vi.isContextConsumer)(A.default.createElement(t.Consumer,null))?t:I},[t,I]),i=(0,A.useContext)(o),s=Boolean(r.store)&&Boolean(r.store.getState)&&Boolean(r.store.dispatch),e=Boolean(i)&&Boolean(i.store);if(!s&&!e)throw new Error('Could not find "store" in the context of "'+S+'". Either wrap the root component in a <Provider>, or pass a custom React context provider to <Provider> and the corresponding React context consumer to '+S+" in connect options.");var l=(s?r:i).store,c=(0,A.useMemo)(function(){return C(l.dispatch,E)},[l]),e=(0,A.useMemo)(function(){var e,t;return x?(t=(e=Bi(l,s?null:i.subscription)).notifyNestedSubs.bind(e),[e,t]):Ki},[l,s,i]),d=e[0],e=e[1],u=(0,A.useMemo)(function(){return s?i:j({},i,{subscription:d})},[s,i,d]),p=(0,A.useReducer)(Yi,Wi,es),m=p[0][0],p=p[1];if(m&&m.error)throw m.error;var h=(0,A.useRef)(),f=(0,A.useRef)(n),g=(0,A.useRef)(),b=(0,A.useRef)(!1),v=k(function(){return g.current&&n===f.current?g.current:c(l.getState(),n)},[l,m,n]),y=(Zi(Xi,[f,h,b,n,v,g,e]),Zi(Qi,[x,l,d,c,f,h,b,g,e,p],[l,d,c]),(0,A.useMemo)(function(){return A.default.createElement(w,j({},v,{ref:a}))},[a,w,v]));return(0,A.useMemo)(function(){return x?A.default.createElement(o.Provider,{value:u},y):y},[o,y,u])}}}function rs(e,t){return e===t?0!==e||0!==t||1/e==1/t:e!=e&&t!=t}function as(e,t){if(!rs(e,t)){if("object"!=typeof e||null===e||"object"!=typeof t||null===t)return!1;var r=Object.keys(e),a=Object.keys(t);if(r.length!==a.length)return!1;for(var n=0;n<r.length;n++)if(!Object.prototype.hasOwnProperty.call(t,r[n])||!rs(e[r[n]],t[r[n]]))return!1}return!0}function ns(r,a){var e,n={};for(e in r)!function(e){var t=r[e];"function"==typeof t&&(n[e]=function(){return a(t.apply(void 0,arguments))})}(e);return n}function os(e){"undefined"!=typeof console&&"function"==typeof console.error&&console.error(e);try{throw new Error(e)}catch(e){}}function is(e,t,r){!function(e){if("object"==typeof e&&null!==e){e=Object.getPrototypeOf(e);if(null===e)return 1;for(var t=e;null!==Object.getPrototypeOf(t);)t=Object.getPrototypeOf(t);return e===t}}(e)&&os(r+"() in "+t+" must return a plain object. Instead received "+e+".")}function ss(n){return function(e,t){var r=n(e,t);function a(){return r}return a.dependsOnOwnProps=!1,a}}function ls(e){return null!==e.dependsOnOwnProps&&void 0!==e.dependsOnOwnProps?Boolean(e.dependsOnOwnProps):1!==e.length}function cs(o,i){return function(e,t){var a=t.displayName,n=function(e,t){return n.dependsOnOwnProps?n.mapToProps(e,t):n.mapToProps(e)};return n.dependsOnOwnProps=!0,n.mapToProps=function(e,t){n.mapToProps=o,n.dependsOnOwnProps=ls(o);var r=n(e,t);return"function"==typeof r&&(n.mapToProps=r,n.dependsOnOwnProps=ls(r),r=n(e,t)),is(r,a,i),r},n}}var ds=[function(e){return"function"==typeof e?cs(e,"mapDispatchToProps"):void 0},function(e){return e?void 0:ss(function(e){return{dispatch:e}})},function(t){return t&&"object"==typeof t?ss(function(e){return ns(t,e)}):void 0}];var us=[function(e){return"function"==typeof e?cs(e,"mapStateToProps"):void 0},function(e){return e?void 0:ss(function(){return{}})}];function ps(e,t,r){return j({},r,e,t)}var ms=[function(e){return"function"==typeof e?(l=e,function(e,t){var a,n=t.displayName,o=t.pure,i=t.areMergedPropsEqual,s=!1;return function(e,t,r){e=l(e,t,r);return s?o&&i(e,a)||(a=e):(s=!0,is(a=e,n,"mergeProps")),a}}):void 0;var l},function(e){return e?void 0:function(){return ps}}];function hs(e,t,r){if(!e)throw new Error("Unexpected value for "+t+" in "+r+".");"mapStateToProps"!==t&&"mapDispatchToProps"!==t||Object.prototype.hasOwnProperty.call(e,"dependsOnOwnProps")||os("The selector for "+t+" of "+r+" did not specify a value for dependsOnOwnProps.")}var fs=["initMapStateToProps","initMapDispatchToProps","initMergeProps"];function gs(r,a,n,o){return function(e,t){return n(r(e,t),a(o,t),t)}}function bs(n,o,i,s,e){var l,c,d,u,p,m=e.areStatesEqual,h=e.areOwnPropsEqual,f=e.areStatePropsEqual,r=!1;function a(e,t){var r=!h(t,c),a=!m(e,l,t,c);return l=e,c=t,r&&a?(d=n(l,c),o.dependsOnOwnProps&&(u=o(s,c)),p=i(d,u,c)):r?(n.dependsOnOwnProps&&(d=n(l,c)),o.dependsOnOwnProps&&(u=o(s,c)),p=i(d,u,c)):a?(e=n(l,c),t=!f(e,d),d=e,p=t?i(d,u,c):p):p}return function(e,t){return r?a(e,t):(d=n(l=e,c=t),u=o(s,c),p=i(d,u,c),r=!0,p)}}function vs(e,t){var r,a,n,o=t.initMapStateToProps,i=t.initMapDispatchToProps,s=t.initMergeProps,t=Ui(t,fs),o=o(e,t),i=i(e,t),s=s(e,t);return r=i,a=s,n=t.displayName,hs(o,"mapStateToProps",n),hs(r,"mapDispatchToProps",n),hs(a,"mergeProps",n),(t.pure?bs:gs)(o,i,s,e,t)}var ys=["pure","areStatesEqual","areOwnPropsEqual","areStatePropsEqual","areMergedPropsEqual"];function ws(r,e,a){for(var t=e.length-1;0<=t;t--){var n=e[t](r);if(n)return n}return function(e,t){throw new Error("Invalid value of type "+typeof r+" for "+a+" argument when connecting component "+t.wrappedComponentName+".")}}function Ss(e,t){return e===t}function Es(e){var e=void 0===e?{}:e,t=e.connectHOC,d=void 0===t?ts:t,t=e.mapStateToPropsFactories,u=void 0===t?us:t,t=e.mapDispatchToPropsFactories,p=void 0===t?ds:t,t=e.mergePropsFactories,m=void 0===t?ms:t,t=e.selectorFactory,h=void 0===t?vs:t;return function(e,t,r,a){var a=a=void 0===a?{}:a,n=a.pure,n=void 0===n||n,o=a.areStatesEqual,o=void 0===o?Ss:o,i=a.areOwnPropsEqual,i=void 0===i?as:i,s=a.areStatePropsEqual,s=void 0===s?as:s,l=a.areMergedPropsEqual,l=void 0===l?as:l,a=Ui(a,ys),c=ws(e,u,"mapStateToProps"),t=ws(t,p,"mapDispatchToProps"),r=ws(r,m,"mergeProps");return d(h,j({methodName:"connect",getDisplayName:function(e){return"Connect("+e+")"},shouldHandleStateChanges:Boolean(e),initMapStateToProps:c,initMapDispatchToProps:t,initMergeProps:r,pure:n,areStatesEqual:o,areOwnPropsEqual:i,areStatePropsEqual:s,areMergedPropsEqual:l},a))}}var Xn=Es(),Yn=(t(r()),t(r()),t(r()),t(e())),ks=(Qn=Yn.unstable_batchedUpdates,ji=Qn,t(r()));function Cs(e,t){var r=(0,ks.useState)(function(){return{inputs:t,result:e()}})[0],a=(0,ks.useRef)(!0),n=(0,ks.useRef)(r),o=a.current||Boolean(t&&n.current.inputs&&function(e,t){if(e.length!==t.length)return!1;for(var r=0;r<e.length;r++)if(e[r]!==t[r])return!1;return!0}(t,n.current.inputs))?n.current:{inputs:t,result:e()};return(0,ks.useEffect)(function(){a.current=!1,n.current=o},[o]),o.result}var K=Cs,J=function(e,t){return Cs(function(){return e},t)},xs="Invariant failed";function Is(e,t){if(!e)throw t=(e="function"==typeof t?t():t)?"".concat(xs,": ").concat(e):xs,new Error(t)}function Ns(e,t){return{top:e.top-t.top,left:e.left-t.left,bottom:e.bottom+t.bottom,right:e.right+t.right}}function As(e,t){return{top:e.top+t.top,left:e.left+t.left,bottom:e.bottom-t.bottom,right:e.right-t.right}}function Ts(e,t){return void 0===t&&(t=js()),_s(e,t)}function Os(e,t){var r={top:Ms(t.marginTop),right:Ms(t.marginRight),bottom:Ms(t.marginBottom),left:Ms(t.marginLeft)},a={top:Ms(t.paddingTop),right:Ms(t.paddingRight),bottom:Ms(t.paddingBottom),left:Ms(t.paddingLeft)},t={top:Ms(t.borderTopWidth),right:Ms(t.borderRightWidth),bottom:Ms(t.borderBottomWidth),left:Ms(t.borderLeftWidth)};return Rs({borderBox:e,margin:r,padding:a,border:t})}function Ps(e){var t=e.getBoundingClientRect(),e=window.getComputedStyle(e);return Os(t,e)}var Ls=function(e){var t=e.top,r=e.right,a=e.bottom,e=e.left;return{top:t,right:r,bottom:a,left:e,width:r-e,height:a-t,x:e,y:t,center:{x:(r+e)/2,y:(a+t)/2}}},Ds={top:0,right:0,bottom:0,left:0},Rs=function(e){var t=e.borderBox,r=e.margin,r=void 0===r?Ds:r,a=e.border,a=void 0===a?Ds:a,e=e.padding,e=void 0===e?Ds:e,n=Ls(Ns(t,r)),o=Ls(As(t,a)),i=Ls(As(o,e));return{marginBox:n,borderBox:Ls(t),paddingBox:o,contentBox:i,margin:r,border:a,padding:e}},Ms=function(e){var t,r=e.slice(0,-2);return"px"!==e.slice(-2)?0:(t=Number(r),isNaN(t)&&Is(!1,"Could not parse value [raw: "+e+", without suffix: "+r+"]"),t)},js=function(){return{x:window.pageXOffset,y:window.pageYOffset}},_s=function(e,t){var r=e.borderBox,a=e.border,n=e.margin,e=e.padding,r={top:r.top+t.y,left:r.left+t.x,bottom:r.bottom+t.y,right:r.right+t.x};return Rs({borderBox:r,border:a,margin:n,padding:e})},Bs=Number.isNaN||function(e){return"number"==typeof e&&e!=e};function $s(e,t){if(e.length!==t.length)return!1;for(var r,a,n=0;n<e.length;n++)if(r=e[n],a=t[n],r!==a&&(!Bs(r)||!Bs(a)))return!1;return!0}function zs(e){return e.replace(Gs," ").replace(Vs,"").trim()}var Y=function(r,a){void 0===a&&(a=$s);var n,o,i=[],s=!1;return function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return s&&n===this&&a(e,i)||(o=r.apply(this,e),s=!0,n=this,i=e),o}},Fs=function(a){function e(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];n=t,o=o||requestAnimationFrame(function(){o=null,a.apply(void 0,n)})}var n,o=null;return e.cancel=function(){o&&(cancelAnimationFrame(o),o=null)},e},Us=t(e()),Gs=/[ \t]{2,}/g,Vs=/^[ \t]*/gm,Hs=function(e){return zs("\n  %creact-beautiful-dnd\n\n  %c"+zs(e)+"\n\n  %c👷‍ This is a development only message. It will be removed in production builds.\n")};function qs(e,t){var r;"undefined"!=typeof window&&window["__react-beautiful-dnd-disable-dev-warnings"]||(r=console)[e].apply(r,[Hs(t),"color: #00C584; font-size: 1.2em; font-weight: bold;","line-height: 1.5","color: #723874;"])}var Z=qs.bind(null,"warn"),Ws=qs.bind(null,"error");function Ks(){}function Js(a,e,n){var t=e.map(function(e){t=e.options;var t,r=j({},n,{},t);return a.addEventListener(e.eventName,e.fn,r),function(){a.removeEventListener(e.eventName,e.fn,r)}});return function(){t.forEach(function(e){e()})}}var Ys=!1,Zs="Invariant failed";function Xs(e){this.message=e}function X(e,t){if(!e)throw new Xs(Ys?Zs:Zs+": "+(t||""))}Xs.prototype.toString=function(){return this.message};yi(tl,Qs=W.default.Component),(l=tl.prototype).componentDidMount=function(){this.unbind=Js(window,[{eventName:"error",fn:this.onWindowError}])},l.componentDidCatch=function(e){if(!(e instanceof Xs))throw e;Ws(e.message),this.setState({})},l.componentWillUnmount=function(){this.unbind()},l.render=function(){return this.props.children(this.setCallbacks)};var Qs,el=tl;function tl(){for(var r,e=arguments.length,t=new Array(e),a=0;a<e;a++)t[a]=arguments[a];return(r=Qs.call.apply(Qs,[this].concat(t))||this).callbacks=null,r.unbind=Ks,r.onWindowError=function(e){var t=r.getCallbacks(),t=(t.isDragging()&&(t.tryAbort(),Z("\n        An error was caught by our window 'error' event listener while a drag was occurring.\n        The active drag has been aborted.\n      ")),e.error);t instanceof Xs&&(e.preventDefault(),Ws(t.message))},r.getCallbacks=function(){if(r.callbacks)return r.callbacks;throw new Error("Unable to find AppCallbacks in <ErrorBoundary/>")},r.setCallbacks=function(e){r.callbacks=e},r}function rl(e,t){var r=e.droppableId===t.droppableId,a=gl(e.index),n=gl(t.index);return r?"\n      You have moved the item from position "+a+"\n      to position "+n+"\n    ":"\n    You have moved the item from position "+a+"\n    in list "+e.droppableId+"\n    to list "+t.droppableId+"\n    in position "+n+"\n  "}function al(e,t,r){return t.droppableId===r.droppableId?"\n      The item "+e+"\n      has been combined with "+r.draggableId:"\n      The item "+e+"\n      in list "+t.droppableId+"\n      has been combined with "+r.draggableId+"\n      in list "+r.droppableId+"\n    "}function nl(e){return"\n  The item has returned to its starting position\n  of "+gl(e.index)+"\n"}function m(e,t){return{x:e.x+t.x,y:e.y+t.y}}function ol(e,t){return{x:e.x-t.x,y:e.y-t.y}}function il(e,t){return e.x===t.x&&e.y===t.y}function sl(e,t,r){var a;return void 0===r&&(r=0),(a={})[e]=t,a["x"===e?"y":"x"]=r,a}function ll(e,t){return Math.sqrt(Math.pow(t.x-e.x,2)+Math.pow(t.y-e.y,2))}function cl(t,e){return Math.min.apply(Math,e.map(function(e){return ll(t,e)}))}function dl(t){return function(e){return{x:t(e.x),y:t(e.y)}}}function ul(e,t){return{top:e.top+t.y,left:e.left+t.x,bottom:e.bottom+t.y,right:e.right+t.x}}function pl(e){return[{x:e.left,y:e.top},{x:e.right,y:e.top},{x:e.left,y:e.bottom},{x:e.right,y:e.bottom}]}function ml(e,t){var r;return t&&t.shouldClipSubject?(t=t.pageMarginBox,r=e,(r=Ls({top:Math.max(r.top,t.top),right:Math.min(r.right,t.right),bottom:Math.min(r.bottom,t.bottom),left:Math.max(r.left,t.left)})).width<=0||r.height<=0?null:r):Ls(e)}function hl(e){var t,r,a=e.page,n=e.withPlaceholder,o=e.axis,e=e.frame,i=(t=a.marginBox,(i=e)?ul(t,i.scroll.diff.displacement):t),o=(t=i,i=o,(o=n)&&o.increasedBy?j({},t,((r={})[i.end]=t[i.end]+o.increasedBy[i.line],r)):t);return{page:a,withPlaceholder:n,active:ml(o,e)}}function fl(e,t){e.frame||X(!1);var r=e.frame,a=ol(t,r.scroll.initial),n=vl(a),t=j({},r,{scroll:{initial:r.scroll.initial,current:t,diff:{value:a,displacement:n},max:r.scroll.max}});return j({},e,{frame:t,subject:hl({page:e.subject.page,withPlaceholder:e.subject.withPlaceholder,axis:e.axis,frame:t})})}var gl=function(e){return e+1},bl={dragHandleUsageInstructions:"\n  Press space bar to start a drag.\n  When dragging you can use the arrow keys to move the item around and escape to cancel.\n  Some screen readers may require you to be in focus mode or to use your pass through key\n",onDragStart:function(e){return"\n  You have lifted an item in position "+gl(e.source.index)+"\n"},onDragUpdate:function(e){var t=e.destination;return t?rl(e.source,t):(t=e.combine)?al(e.draggableId,e.source,t):"You are over an area that cannot be dropped on"},onDragEnd:function(e){var t,r;return"CANCEL"===e.reason?"\n      Movement cancelled.\n      "+nl(e.source)+"\n    ":(t=e.destination,r=e.combine,t?"\n      You have dropped the item.\n      "+rl(e.source,t)+"\n    ":r?"\n      You have dropped the item.\n      "+al(e.draggableId,e.source,r)+"\n    ":"\n    The item has been dropped while not over a drop area.\n    "+nl(e.source)+"\n  ")}},Q={x:0,y:0},vl=function(e){return{x:0!==e.x?-e.x:0,y:0!==e.y?-e.y:0}};function yl(t){return Object.values?Object.values(t):Object.keys(t).map(function(e){return t[e]})}function wl(e,t){if(e.findIndex)return e.findIndex(t);for(var r=0;r<e.length;r++)if(t(e[r]))return r;return-1}function Sl(e,t){return e.find?e.find(t):-1!==(t=wl(e,t))?e[t]:void 0}function El(e){return Array.prototype.slice.call(e)}var kl=Y(function(e){return e.reduce(function(e,t){return e[t.descriptor.id]=t,e},{})}),Cl=Y(function(e){return e.reduce(function(e,t){return e[t.descriptor.id]=t,e},{})}),xl=Y(yl),Il=Y(yl),Nl=Y(function(t,e){return Il(e).filter(function(e){return t===e.descriptor.droppableId}).sort(function(e,t){return e.descriptor.index-t.descriptor.index})});function Al(e){return e.at&&"REORDER"===e.at.type?e.at.destination:null}function Tl(e){return e.at&&"COMBINE"===e.at.type?e.at.combine:null}function Ol(e,t){return e.descriptor.droppableId===t.descriptor.id}function Pl(t,r){return function(e){return t<=e&&e<=r}}function Ll(e){var t=Pl(e.top,e.bottom),r=Pl(e.left,e.right);return function(e){return t(e.top)&&t(e.bottom)&&r(e.left)&&r(e.right)}}function Dl(e){return Ul(j({},e,{isVisibleThroughFrameFn:Ll}))}function Rl(e){return Ul(j({},e,{isVisibleThroughFrameFn:(a=e.destination.axis,function(e){var t=Pl(e.top,e.bottom),r=Pl(e.left,e.right);return function(e){return a===zl?t(e.top)&&t(e.bottom):r(e.left)&&r(e.right)}})}));var a}var Ml=Y(function(t,e){return e.filter(function(e){return e.descriptor.id!==t.descriptor.id})}),jl={point:Q,value:0},_l={invisible:{},visible:{},all:[]},Bl={displaced:_l,displacedBy:jl,at:null},$l=function(n){var o=Pl(n.top,n.bottom),i=Pl(n.left,n.right);return function(e){var t,r,a;return!!(o(e.top)&&o(e.bottom)&&i(e.left)&&i(e.right))||(t=o(e.top)||o(e.bottom),r=i(e.left)||i(e.right),!(!t||!r))||(a=e.top<n.top&&e.bottom>n.bottom,e=e.left<n.left&&e.right>n.right,a&&e)||a&&r||e&&t}},zl={direction:"vertical",line:"y",crossAxisLine:"x",start:"top",end:"bottom",size:"height",crossAxisStart:"left",crossAxisEnd:"right",crossAxisSize:"width"},Fl={direction:"horizontal",line:"x",crossAxisLine:"y",start:"left",end:"right",size:"width",crossAxisStart:"top",crossAxisEnd:"bottom",crossAxisSize:"height"},Ul=function(e){var t,r=e.target,a=e.destination,n=e.viewport,o=e.withDroppableDisplacement,e=e.isVisibleThroughFrameFn,o=o?function(e,t){t=t.frame?t.frame.scroll.diff.displacement:Q;return ul(e,t)}(r,a):r;return r=o,t=e,!!(a=a).subject.active&&t(a.subject.active)(r)&&(t=o,e(n)(t))};function Gl(e){var t=e.afterDragging,i=e.destination,s=e.displacedBy,l=e.viewport,c=e.forceShouldAnimate,d=e.last;return t.reduce(function(e,t){o=s,n=(n=t).page.marginBox,o={top:s.point.y,right:0,bottom:0,left:s.point.x};var r,a,n=Ls(Ns(n,o)),o=t.descriptor.id;return e.all.push(o),Ul(j({},{target:n,destination:i,viewport:l,withDroppableDisplacement:!0},{isVisibleThroughFrameFn:$l}))?(r=(n=o,r=d,"boolean"==typeof(a=c)?a:!r||(a=r.invisible,r=r.visible,!a[n]&&(!(a=r[n])||a.shouldAnimate))),e.visible[o]={draggableId:o,shouldAnimate:r}):e.invisible[t.descriptor.id]=!0,e},{all:[],visible:{},invisible:{}})}function Vl(e){var t=e.insideDestination,r=e.inHomeList,a=e.displacedBy,e=e.destination,r=(r={inHomeList:r},(t=t).length?(t=t[t.length-1].descriptor.index,r.inHomeList?t:t+1):0);return{displaced:_l,displacedBy:a,at:{type:"REORDER",destination:{droppableId:e.descriptor.id,index:r}}}}function Hl(e){var t,r=e.draggable,a=e.insideDestination,n=e.destination,o=e.viewport,i=e.displacedBy,s=e.last,l=e.index,e=e.forceShouldAnimate,c=Ol(r,n);return null!=l&&(t=Sl(a,function(e){return e.descriptor.index===l}))?(r=Ml(r,a),t=a.indexOf(t),{displaced:Gl({afterDragging:r.slice(t),destination:n,displacedBy:i,last:s,viewport:o.frame,forceShouldAnimate:e}),displacedBy:i,at:{type:"REORDER",destination:{droppableId:n.descriptor.id,index:l}}}):Vl({insideDestination:a,inHomeList:c,displacedBy:i,destination:n})}function ql(e,t){return Boolean(t.effected[e])}function Wl(e){var t,r,a,n,o=e.isMovingForward,i=e.isInHomeList,s=e.draggable,l=e.draggables,c=e.destination,d=e.insideDestination,u=e.previousImpact,p=e.viewport,e=e.afterCritical,m=u.at;return m||X(!1,"Cannot move in direction without previous impact location"),"REORDER"===m.type?(i={isMovingForward:o,isInHomeList:i,location:m.destination,insideDestination:d},n=i.isMovingForward,r=i.isInHomeList,a=i.insideDestination,i=i.location,null==(i=!a.length||(i=i.index,t=a[0].descriptor.index,a=a[a.length-1].descriptor.index,(n=n?i+1:i-1)<t)||(r?a:a+1)<n?null:n)?null:Hl({draggable:s,insideDestination:d,destination:c,viewport:p,last:u.displaced,displacedBy:u.displacedBy,index:i})):(t={isMovingForward:o,destination:c,displaced:u.displaced,draggables:l,combine:m.combine,afterCritical:e},r=t.isMovingForward,a=t.destination,n=t.draggables,i=t.combine,t=t.afterCritical,null==(o=a.isCombineEnabled?(n=n[a=i.draggableId].descriptor.index,ql(a,t)?r?n:n-1:r?n+1:n):null)?null:Hl({draggable:s,insideDestination:d,destination:c,viewport:p,last:u.displaced,displacedBy:u.displacedBy,index:o}))}function Kl(e){var t=e.afterCritical,r=e.impact,e=e.draggables,a=((a=Tl(r))||X(!1),a.draggableId),e=e[a].page.borderBox.center,n=(t={displaced:r.displaced,afterCritical:t,combineWith:a,displacedBy:r.displacedBy},a=t.displaced,r=t.afterCritical,n=t.combineWith,t=t.displacedBy,a=Boolean(a.visible[n]||a.invisible[n]),ql(n,r)?a?Q:vl(t.point):a?t.point:Q);return m(e,n)}function Jl(e,t){return t.margin[e.start]+t.borderBox[e.size]/2}function Yl(e,t,r){return t[e.crossAxisStart]+r.margin[e.crossAxisStart]+r.borderBox[e.crossAxisSize]/2}function Zl(e){var t=e.axis,r=e.moveRelativeTo,e=e.isMoving;return sl(t.line,r.marginBox[t.end]+Jl(t,e),Yl(t,r.marginBox,e))}function Xl(e){var t=e.axis,r=e.moveRelativeTo,e=e.isMoving;return sl(t.line,r.marginBox[t.start]-(e.margin[t.end]+e.borderBox[t.size]/2),Yl(t,r.marginBox,e))}function Ql(e){var t,r=e.impact,a=e.draggable,n=e.draggables,o=e.droppable,e=e.afterCritical,i=Nl(o.descriptor.id,n),s=a.page,l=o.axis;return i.length?(t=r.displaced,r=r.displacedBy,(t=t.all[0])?(n=n[t],ql(t,e)?Xl({axis:l,moveRelativeTo:n.page,isMoving:s}):(t=_s(n.page,r.point),Xl({axis:l,moveRelativeTo:t,isMoving:s}))):(n=i[i.length-1]).descriptor.id===a.descriptor.id?s.borderBox.center:ql(n.descriptor.id,e)?(r=_s(n.page,vl(e.displacedBy.point)),Zl({axis:l,moveRelativeTo:r,isMoving:s})):Zl({axis:l,moveRelativeTo:n.page,isMoving:s})):(t={axis:l,moveInto:o.page,isMoving:s},i=t.axis,a=t.moveInto,t=t.isMoving,sl(i.line,a.contentBox[i.start]+Jl(i,t),Yl(i,a.contentBox,t)))}function ec(e,t){return(e=e.frame)?m(t,e.scroll.diff.displacement):t}function tc(e){r=(t=e).impact,s=t.draggable,a=t.droppable,n=t.draggables,t=t.afterCritical,o=s.page.borderBox.center,i=r.at;var t,r,a,n,o,i=a&&i?"REORDER"===i.type?Ql({impact:r,draggable:s,draggables:n,droppable:a,afterCritical:t}):Kl({impact:r,draggables:n,afterCritical:t}):o,s=e.droppable;return s?ec(s,i):i}function rc(e,t){var r=ol(t,e.scroll.initial),a=vl(r);return{frame:Ls({top:t.y,bottom:t.y+e.frame.height,left:t.x,right:t.x+e.frame.width}),scroll:{initial:e.scroll.initial,max:e.scroll.max,current:t,diff:{value:r,displacement:a}}}}function ac(e,t){return e.map(function(e){return t[e]})}function nc(e){var t=e.impact,r=e.viewport,a=e.destination,n=e.draggables,e=e.maxScrollChange,o=rc(r,m(r.scroll.current,e)),e=a.frame?fl(a,m(a.frame.scroll.current,e)):a,i=t.displaced,a=Gl({afterDragging:ac(i.all,n),destination:a,displacedBy:t.displacedBy,viewport:o.frame,last:i,forceShouldAnimate:!1}),o=Gl({afterDragging:ac(i.all,n),destination:e,displacedBy:t.displacedBy,viewport:r.frame,last:i,forceShouldAnimate:!1}),s={},l={},c=[i,a,o];return i.all.forEach(function(e){var t=function(e,t){for(var r=0;r<t.length;r++){var a=t[r].visible[e];if(a)return a}return null}(e,c);t?l[e]=t:s[e]=!0}),j({},t,{displaced:{all:i.all,invisible:s,visible:l}})}function oc(e){var t=e.pageBorderBoxCenter,r=e.draggable,e=e.viewport,e=m(e.scroll.diff.displacement,t),t=ol(e,r.page.borderBox.center);return m(r.client.borderBox.center,t)}function ic(e){var t=e.draggable,r=e.destination,a=e.newPageBorderBoxCenter,n=e.viewport,o=e.withDroppableDisplacement,e=e.onlyOnMainAxis,a=ol(a,t.page.borderBox.center),t={target:ul(t.page.borderBox,a),destination:r,withDroppableDisplacement:o,viewport:n};return(void 0!==e&&e?Rl:Dl)(t)}function sc(e){var t,r,a,n,o,i,s,l,c,d=e.isMovingForward,u=e.draggable,p=e.destination,m=e.draggables,h=e.previousImpact,f=e.viewport,g=e.previousPageBorderBoxCenter,b=e.previousClientSelection,e=e.afterCritical;return p.isEnabled&&(t=Nl(p.descriptor.id,m),r=Ol(u,p),l=(a={isMovingForward:d,draggable:u,destination:p,insideDestination:t,previousImpact:h}).isMovingForward,c=a.draggable,o=a.destination,i=a.insideDestination,s=a.previousImpact,i=(o.isCombineEnabled&&Al(s)?(a=s.displaced.all,n=a.length?a[0]:null,l?n?v(n):null:(a=Ml(c,i),n?(-1===(l=wl(a,function(e){return e.descriptor.id===n}))&&X(!1,"Could not find displaced item in set"),(c=l-1)<0?null:v(a[c].descriptor.id)):a.length?v(a[a.length-1].descriptor.id):null)):null)||Wl({isMovingForward:d,isInHomeList:r,draggable:u,draggables:m,destination:p,insideDestination:t,previousImpact:h,viewport:f,afterCritical:e}))?(l=tc({impact:i,draggable:u,droppable:p,draggables:m,afterCritical:e}),ic({draggable:u,destination:p,newPageBorderBoxCenter:l,viewport:f.frame,withDroppableDisplacement:!1,onlyOnMainAxis:!0})?{clientSelection:oc({pageBorderBoxCenter:l,draggable:u,viewport:f}),impact:i,scrollJumpRequest:null}:(c=ol(l,g),{clientSelection:b,impact:nc({impact:i,viewport:f,destination:p,draggables:m,maxScrollChange:c}),scrollJumpRequest:c})):null;function v(e){e={type:"COMBINE",combine:{draggableId:e,droppableId:o.descriptor.id}};return j({},s,{at:e})}}function lc(e){return(e=e.subject.active)||X(!1,"Cannot get clipped area from droppable"),e}function cc(e,t){var r=e.page.borderBox.center;return ql(e.descriptor.id,t)?ol(r,t.displacedBy.point):r}function dc(e){var n=e.pageBorderBoxCenter,a=e.viewport,o=e.destination,t=e.insideDestination,i=e.afterCritical;return t.filter(function(e){return Dl({target:(t=i,r=e.page.borderBox,ql(e.descriptor.id,t)?ul(r,vl(t.displacedBy.point)):r),destination:o,viewport:a.frame,withDroppableDisplacement:!0});var t,r}).sort(function(e,t){var r=ll(n,ec(o,cc(e,i))),a=ll(n,ec(o,cc(t,i)));return r<a?-1:a<r?1:e.descriptor.index-t.descriptor.index})[0]||null}function uc(e,t){return j({},e,{scroll:j({},e.scroll,{max:t})})}function pc(e,t,r){var a,n,o,i=e.frame,t=(Ol(t,e)&&X(!1,"Should not add placeholder space to home list"),e.subject.withPlaceholder&&X(!1,"Cannot add placeholder size to a subject when it already has one"),fc(e.axis,t.displaceBy).point),r=(s=t,r=r,a=(o=e).axis,"virtual"===o.descriptor.mode?sl(a.line,s[a.line]):(n=o.subject.page.contentBox[a.size],(o=Nl(o.descriptor.id,r).reduce(function(e,t){return e+t.client.marginBox[a.size]},0)+s[a.line]-n)<=0?null:sl(a.line,o))),s={placeholderSize:t,increasedBy:r,oldFrameMaxScroll:e.frame?e.frame.scroll.max:null};return i?(n=r?m(i.scroll.max,r):i.scroll.max,o=uc(i,n),j({},e,{subject:hl({page:e.subject.page,withPlaceholder:s,axis:e.axis,frame:o}),frame:o})):j({},e,{subject:hl({page:e.subject.page,withPlaceholder:s,axis:e.axis,frame:e.frame})})}function mc(e){var n,r,a,o,t,i,s,l,c,d,u,p,m,h=e.isMovingForward,f=e.previousPageBorderBoxCenter,g=e.draggable,b=e.isOver,v=e.draggables,y=e.droppables,w=e.viewport,e=e.afterCritical;return a=(h={isMovingForward:h,pageBorderBoxCenter:f,source:b,droppables:y,viewport:w}).isMovingForward,o=h.pageBorderBoxCenter,t=h.source,b=h.droppables,i=h.viewport,(y=(s=t.subject.active)&&(n=t.axis,r=Pl(s[n.start],s[n.end]),(h=xl(b).filter(function(e){return e!==t}).filter(function(e){return e.isEnabled}).filter(function(e){return Boolean(e.subject.active)}).filter(function(e){return $l(i.frame)(lc(e))}).filter(function(e){e=lc(e);return a?s[n.crossAxisEnd]<e[n.crossAxisEnd]:e[n.crossAxisStart]<s[n.crossAxisStart]}).filter(function(e){var e=lc(e),t=Pl(e[n.start],e[n.end]);return r(e[n.start])||r(e[n.end])||t(s[n.start])||t(s[n.end])}).sort(function(e,t){e=lc(e)[n.crossAxisStart],t=lc(t)[n.crossAxisStart];return a?e-t:t-e}).filter(function(e,t,r){return lc(e)[n.crossAxisStart]===lc(r[0])[n.crossAxisStart]})).length)?(1===h.length?h:1===(b=h.filter(function(e){return Pl(lc(e)[n.start],lc(e)[n.end])(o[n.line])})).length?b:1<b.length?b.sort(function(e,t){return lc(e)[n.start]-lc(t)[n.start]}):h.sort(function(e,t){var r=cl(o,pl(lc(e))),a=cl(o,pl(lc(t)));return r!==a?r-a:lc(e)[n.start]-lc(t)[n.start]}))[0]:null)&&(b=Nl(y.descriptor.id,v),h=dc({pageBorderBoxCenter:f,viewport:w,destination:y,insideDestination:b,afterCritical:e}),h=(f={previousPageBorderBoxCenter:f,destination:y,draggable:g,draggables:v,moveRelativeTo:h,insideDestination:b,viewport:w,afterCritical:e}).previousPageBorderBoxCenter,b=f.moveRelativeTo,c=f.insideDestination,d=f.draggable,u=f.draggables,p=f.destination,m=f.viewport,f=f.afterCritical,c=b?(h=Boolean(h[p.axis.line]<=b.page.borderBox.center[p.axis.line]),l=b.descriptor.index,b=b.descriptor.id===d.descriptor.id||h?l:l+1,h=fc(p.axis,d.displaceBy),Hl({draggable:d,insideDestination:c,destination:p,viewport:m,displacedBy:h,last:_l,index:b})):!c.length&&(l={displaced:_l,displacedBy:jl,at:{type:"REORDER",destination:{droppableId:p.descriptor.id,index:0}}},h=tc({impact:l,draggable:d,droppable:p,draggables:u,afterCritical:f}),b=Ol(d,p)?p:pc(p,d,u),ic({draggable:d,destination:b,newPageBorderBoxCenter:h,viewport:m.frame,withDroppableDisplacement:!1,onlyOnMainAxis:!0}))?l:null)?(f=tc({impact:c,draggable:g,droppable:y,draggables:v,afterCritical:e}),{clientSelection:oc({pageBorderBoxCenter:f,draggable:g,viewport:w}),impact:c,scrollJumpRequest:null}):null}function hc(e){var t,r,a,n=e.state,e=e.type,o=function(e,t){e=gc(e);return e?t[e]:null}(n.impact,n.dimensions.droppables),i=Boolean(o),s=n.dimensions.droppables[n.critical.droppable.id];return(s="vertical"===(s=(o=o||s).axis.direction)&&("MOVE_UP"===e||"MOVE_DOWN"===e)||"horizontal"===s&&("MOVE_LEFT"===e||"MOVE_RIGHT"===e))&&!i?null:(i="MOVE_DOWN"===e||"MOVE_RIGHT"===e,e=n.dimensions.draggables[n.critical.draggable.id],t=n.current.page.borderBoxCenter,r=(a=n.dimensions).draggables,a=a.droppables,s?sc({isMovingForward:i,previousPageBorderBoxCenter:t,draggable:e,destination:o,draggables:r,viewport:n.viewport,previousClientSelection:n.current.client.selection,previousImpact:n.impact,afterCritical:n.afterCritical}):mc({isMovingForward:i,previousPageBorderBoxCenter:t,draggable:e,isOver:o,draggables:r,droppables:a,viewport:n.viewport,afterCritical:n.afterCritical}))}var fc=Y(function(e,t){t=t[e.line];return{value:t,point:sl(e.line,t)}}),gc=function(e){e=e.at;return e?("REORDER"===e.type?e.destination:e.combine).droppableId:null};function bc(e){return"DRAGGING"===e.phase||"COLLECTING"===e.phase}function vc(e){var t=Pl(e.top,e.bottom),r=Pl(e.left,e.right);return function(e){return t(e.y)&&r(e.x)}}function yc(e){var r,a,o=e.pageBorderBox,t=e.draggable,e=e.droppables,e=xl(e).filter(function(e){var t,r,a,n;return!!e.isEnabled&&!!(n=e.subject.active)&&(t=n,(a=o).left<t.right)&&a.right>t.left&&a.top<t.bottom&&a.bottom>t.top&&(!!vc(n)(o.center)||(a=e.axis,t=n.center[a.crossAxisLine],e=o[a.crossAxisStart],r=o[a.crossAxisEnd],a=(n=Pl(n[a.crossAxisStart],n[a.crossAxisEnd]))(e),n=n(r),!a&&!n)||(a?e<t:t<r))});return e.length?1===e.length?e[0].descriptor.id:(r=(t={pageBorderBox:o,draggable:t,candidates:e}).pageBorderBox,e=t.draggable,t=t.candidates,a=e.page.borderBox.center,(e=t.map(function(e){var t=e.axis,t=sl(e.axis.line,r.center[t.line],e.page.borderBox.center[t.crossAxisLine]);return{id:e.descriptor.id,distance:ll(a,t)}}).sort(function(e,t){return t.distance-e.distance}))[0]?e[0].id:null):null}function wc(e,t){return Ls(ul(e,t))}function Sc(e){var t=e.displaced,e=e.id;return Boolean(t.visible[e]||t.invisible[e])}function Ec(e){var a,n,o,i,s,l,c,d,u,p,t,r,m,h,f,g,b=e.pageOffset,v=e.draggable,y=e.draggables,w=e.droppables,S=e.previousImpact,E=e.viewport,e=e.afterCritical,k=yc({pageBorderBox:b=wc(v.page.borderBox,b),draggable:v,droppables:w});return k?(w=w[k],k=Nl(w.descriptor.id,y),y=function(e,t){e=e.frame;return e?wc(t,e.scroll.diff.value):t}(w,b),t=(b={pageBorderBoxWithDroppableScroll:y,draggable:v,previousImpact:S,destination:w,insideDestination:k,afterCritical:e}).draggable,r=b.pageBorderBoxWithDroppableScroll,m=b.previousImpact,h=b.destination,f=b.insideDestination,g=b.afterCritical,(h.isCombineEnabled&&(c=h.axis,b=fc(h.axis,t.displaceBy),d=b.value,u=r[c.start],p=r[c.end],r=Sl(Ml(t,f),function(e){var t=e.descriptor.id,e=e.page.borderBox,r=e[c.size]/4,a=ql(t,g),t=Sc({displaced:m.displaced,id:t});return a?t?p>e[c.start]+r&&p<e[c.end]-r:u>e[c.start]-d+r&&u<e[c.end]-d-r:t?p>e[c.start]+d+r&&p<e[c.end]+d-r:u>e[c.start]+r&&u<e[c.end]-r}))?{displacedBy:b,displaced:m.displaced,at:{type:"COMBINE",combine:{draggableId:r.descriptor.id,droppableId:h.descriptor.id}}}:null)||(t={pageBorderBoxWithDroppableScroll:y,draggable:v,destination:w,insideDestination:k,last:S.displaced,viewport:E,afterCritical:e},f=t.pageBorderBoxWithDroppableScroll,b=t.draggable,r=t.destination,h=t.insideDestination,a=t.last,y=t.viewport,n=t.afterCritical,o=r.axis,t=fc(r.axis,b.displaceBy),i=t.value,s=f[o.start],l=f[o.end],f=Sl(Ml(b,h),function(e){var t=e.descriptor.id,e=e.page.borderBox.center[o.line],r=ql(t,n),t=Sc({displaced:a,id:t});return r?t?l<=e:s<e-i:t?l<=e+i:s<e}),f={draggable:b,closest:f,inHomeList:Ol(b,r)},v=f.draggable,w=f.closest,f=f.inHomeList,f=w?f&&w.descriptor.index>v.descriptor.index?w.descriptor.index-1:w.descriptor.index:null,Hl({draggable:b,insideDestination:h,destination:r,viewport:y,last:a,displacedBy:t,index:f}))):Bl}function kc(e,t){return j({},e,((e={})[t.descriptor.id]=t,e))}function Cc(e){var t,r=e.previousImpact,a=e.impact,e=e.droppables,r=gc(r),a=gc(a);return r&&r!==a&&(a=e[r]).subject.withPlaceholder?((a=(r=a).subject.withPlaceholder)||X(!1,"Cannot remove placeholder form subject when there was none"),a=(t=r.frame)?((a=a.oldFrameMaxScroll)||X(!1,"Expected droppable with frame to have old max frame scroll when removing placeholder"),t=uc(t,a),j({},r,{subject:hl({page:r.subject.page,axis:r.axis,frame:t,withPlaceholder:null}),frame:t})):j({},r,{subject:hl({page:r.subject.page,axis:r.axis,frame:null,withPlaceholder:null})}),kc(e,a)):e}function xc(e){var t,r,a,n,o=e.state,i=e.clientSelection,s=e.dimensions,l=e.viewport,c=e.impact,e=e.scrollJumpRequest,l=l||o.viewport,s=s||o.dimensions,i=i||o.current.client.selection,i={offset:d=ol(i,o.initial.client.selection),selection:i,borderBoxCenter:m(o.initial.client.borderBoxCenter,d)},d={selection:m(i.selection,l.scroll.current),borderBoxCenter:m(i.borderBoxCenter,l.scroll.current),offset:m(i.offset,l.scroll.diff.value)},i={client:i,page:d};return"COLLECTING"===o.phase?j({phase:"COLLECTING"},o,{dimensions:s,viewport:l,current:i}):(t=s.draggables[o.critical.draggable.id],c=c||Ec({pageOffset:d.offset,draggable:t,draggables:s.draggables,droppables:s.droppables,previousImpact:o.impact,viewport:l,afterCritical:o.afterCritical}),d={draggable:t,impact:c,previousImpact:o.impact,draggables:s.draggables,droppables:s.droppables},t=d.draggable,r=d.draggables,a=d.droppables,n=d.previousImpact,d=d.impact,n=Cc({previousImpact:n,impact:d,droppables:a}),a=!(d=gc(d))||(a=a[d],Ol(t,a))||a.subject.withPlaceholder?n:(d=pc(a,t,r),kc(n,d)),j({},o,{current:i,dimensions:{draggables:s.draggables,droppables:a},impact:c,viewport:l,scrollJumpRequest:e||null,forceShouldAnimate:!e&&null}))}function Ic(e){var t,r,a=e.impact,n=e.viewport,o=e.draggables,i=e.destination,e=e.forceShouldAnimate,s=a.displaced;return t=s.all,r=o,j({},a,{displaced:Gl({afterDragging:t.map(function(e){return r[e]}),destination:i,displacedBy:a.displacedBy,viewport:n.frame,forceShouldAnimate:e,last:s})})}function Nc(e){var t=e.impact,r=e.draggable,a=e.droppable,n=e.draggables,o=e.viewport,e=e.afterCritical,t=tc({impact:t,draggable:r,draggables:n,droppable:a,afterCritical:e});return oc({pageBorderBoxCenter:t,draggable:r,viewport:o})}function Ac(e){var t=e.state,r=e.dimensions,e=e.viewport,a=("SNAP"!==t.movementMode&&X(!1),t.impact),e=e||t.viewport,n=(r=r||t.dimensions).draggables,o=r.droppables,i=n[t.critical.draggable.id],o=((s=gc(a))||X(!1,"Must be over a destination in SNAP movement mode"),o[s]),s=Ic({impact:a,viewport:e,destination:o,draggables:n}),a=Nc({impact:s,draggable:i,droppable:o,draggables:n,viewport:e,afterCritical:t.afterCritical});return xc({impact:s,clientSelection:a,state:t,dimensions:r,viewport:e})}function Tc(e){var t=e.draggable,r=e.home,a=e.draggables,e=e.viewport,n=fc(r.axis,t.displaceBy),a=Nl(r.descriptor.id,a);-1===(o=a.indexOf(t))&&X(!1,"Expected draggable to be inside home list");var o=(a=a.slice(o+1)).reduce(function(e,t){return e[t.descriptor.id]=!0,e},{}),o={inVirtualList:"virtual"===r.descriptor.mode,displacedBy:n,effected:o};return{impact:{displaced:Gl({afterDragging:a,destination:r,displacedBy:n,last:null,viewport:e.frame,forceShouldAnimate:!1}),displacedBy:n,at:{type:"REORDER",destination:{index:(a=t.descriptor).index,droppableId:a.droppableId}}},afterCritical:o}}function Oc(e){var t=e.additions,a=e.updatedDroppables,n=e.viewport,o=n.scroll.diff.value;return t.map(function(e){var t,r=e.descriptor.droppableId,r=function(e){e=e.frame;return e||X(!1,"Expected Droppable to have a frame"),e}(a[r]).scroll.diff.value,r=m(o,r);return e={draggable:e,offset:r,initialWindowScroll:n.scroll.initial},r=e.draggable,t=e.offset,e=e.initialWindowScroll,t=_s(r.client,t),e=Ts(t,e),j({},r,{placeholder:j({},r.placeholder,{client:t}),client:t,page:e})})}function Pc(e){return"SNAP"===e.movementMode}function Lc(e,t,r){var a={draggables:(a=e.dimensions).draggables,droppables:kc(a.droppables,t)};return(!Pc(e)||r?xc:Ac)({state:e,dimensions:a})}function Dc(e){return e.isDragging&&"SNAP"===e.movementMode?j({phase:"DRAGGING"},e,{scrollJumpRequest:null}):e}function Rc(e){return{type:"PUBLISH_WHILE_DRAGGING",payload:e}}function Mc(){return{type:"COLLECTION_STARTING",payload:null}}function jc(e){return{type:"UPDATE_DROPPABLE_SCROLL",payload:e}}function _c(e){return{type:"UPDATE_DROPPABLE_IS_ENABLED",payload:e}}function Bc(e){return{type:"UPDATE_DROPPABLE_IS_COMBINE_ENABLED",payload:e}}function $c(e){return{type:"MOVE",payload:e}}function zc(){return{type:"MOVE_UP",payload:null}}function Fc(){return{type:"MOVE_DOWN",payload:null}}function Uc(){return{type:"MOVE_RIGHT",payload:null}}function Gc(){return{type:"MOVE_LEFT",payload:null}}function Vc(){return{type:"FLUSH",payload:null}}function Hc(e){return{type:"DROP_COMPLETE",payload:e}}function qc(e){return{type:"DROP",payload:e}}function Wc(){return{type:"DROP_ANIMATION_FINISHED",payload:null}}var Kc={phase:"IDLE",completed:null,shouldFlush:!1},Jc=function(e,t){if(void 0===e&&(e=Kc),"FLUSH"===t.type)return j({},Kc,{shouldFlush:!0});var r,a,n,o,i,s;if("INITIAL_PUBLISH"===t.type)return"IDLE"!==e.phase&&X(!1,"INITIAL_PUBLISH must come after a IDLE phase"),c=(r=t.payload).critical,l=r.clientSelection,d=r.viewport,s=r.dimensions,r=r.movementMode,n=s.draggables[c.draggable.id],o=s.droppables[c.droppable.id],{phase:"DRAGGING",isDragging:!0,critical:c,movementMode:r,dimensions:s,initial:r={client:c={selection:l,borderBoxCenter:n.client.borderBox.center,offset:Q},page:{selection:m(c.selection,d.scroll.initial),borderBoxCenter:m(c.selection,d.scroll.initial),offset:m(c.selection,d.scroll.diff.value)}},current:r,isWindowScrollAllowed:xl(s.droppables).every(function(e){return!e.isFixedOnPage}),impact:c=(l=Tc({draggable:n,home:o,draggables:s.draggables,viewport:d})).impact,afterCritical:l.afterCritical,onLiftImpact:c,viewport:d,scrollJumpRequest:null,forceShouldAnimate:null};if("COLLECTION_STARTING"===t.type)return"COLLECTING"===e.phase||"DROP_PENDING"===e.phase?e:("DRAGGING"!==e.phase&&X(!1,"Collection cannot start from phase "+e.phase),j({phase:"COLLECTING"},e,{phase:"COLLECTING"}));if("PUBLISH_WHILE_DRAGGING"===t.type)return"COLLECTING"!==e.phase&&"DROP_PENDING"!==e.phase&&X(!1,"Unexpected "+t.type+" received in phase "+e.phase),r={state:e,published:t.payload},a=r.state,r=r.published,n=r.modified.map(function(e){var t=a.dimensions.droppables[e.droppableId];return fl(t,e.scroll)}),n=j({},a.dimensions.droppables,{},kl(n)),o=Cl(Oc({additions:r.additions,updatedDroppables:n,viewport:a.viewport})),i=j({},a.dimensions.draggables,{},o),r.removals.forEach(function(e){delete i[e]}),o={droppables:n,draggables:i},r=gc(a.impact),n=r?o.droppables[r]:null,r=o.draggables[a.critical.draggable.id],s=o.droppables[a.critical.droppable.id],r=Tc({draggable:r,home:s,draggables:i,viewport:a.viewport}),s=r.impact,r=r.afterCritical,n=n&&n.isCombineEnabled?a.impact:s,n=Ec({pageOffset:a.current.page.offset,draggable:o.draggables[a.critical.draggable.id],draggables:o.draggables,droppables:o.droppables,previousImpact:n,viewport:a.viewport,afterCritical:r}),n=j({phase:"DRAGGING"},a,{phase:"DRAGGING",impact:n,onLiftImpact:s,dimensions:o,afterCritical:r,forceShouldAnimate:!1}),"COLLECTING"===a.phase?n:j({phase:"DROP_PENDING"},n,{phase:"DROP_PENDING",reason:a.reason,isWaiting:!1});if("MOVE"===t.type){if("DROP_PENDING"===e.phase)return e;bc(e)||X(!1,t.type+" not permitted in phase "+e.phase);var l=t.payload.client;return il(l,e.current.client.selection)?e:xc({state:e,clientSelection:l,impact:Pc(e)?e.impact:null})}if("UPDATE_DROPPABLE_SCROLL"===t.type){if("DROP_PENDING"===e.phase)return Dc(e);if("COLLECTING"===e.phase)return Dc(e);bc(e)||X(!1,t.type+" not permitted in phase "+e.phase);var c=t.payload,d=c.id,l=c.newScroll,c=e.dimensions.droppables[d];return c?(d=fl(c,l),Lc(e,d,!1)):e}if("UPDATE_DROPPABLE_IS_ENABLED"===t.type){if("DROP_PENDING"===e.phase)return e;bc(e)||X(!1,"Attempting to move in an unsupported phase "+e.phase);var c=t.payload,l=c.id,d=c.isEnabled,c=e.dimensions.droppables[l],l=(c||X(!1,"Cannot find Droppable[id: "+l+"] to toggle its enabled state"),c.isEnabled===d&&X(!1,"Trying to set droppable isEnabled to "+String(d)+"\n      but it is already "+String(c.isEnabled)),j({},c,{isEnabled:d}));return Lc(e,l,!0)}if("UPDATE_DROPPABLE_IS_COMBINE_ENABLED"===t.type){if("DROP_PENDING"===e.phase)return e;bc(e)||X(!1,"Attempting to move in an unsupported phase "+e.phase);var c=t.payload,d=c.id,l=c.isCombineEnabled,c=e.dimensions.droppables[d],d=(c||X(!1,"Cannot find Droppable[id: "+d+"] to toggle its isCombineEnabled state"),c.isCombineEnabled===l&&X(!1,"Trying to set droppable isCombineEnabled to "+String(l)+"\n      but it is already "+String(c.isCombineEnabled)),j({},c,{isCombineEnabled:l}));return Lc(e,d,!0)}if("MOVE_BY_WINDOW_SCROLL"===t.type){if("DROP_PENDING"===e.phase||"DROP_ANIMATING"===e.phase)return e;bc(e)||X(!1,"Cannot move by window in phase "+e.phase),e.isWindowScrollAllowed||X(!1,"Window scrolling is currently not supported for fixed lists");c=t.payload.newScroll;return il(e.viewport.scroll.current,c)?Dc(e):(l=rc(e.viewport,c),(Pc(e)?Ac:xc)({state:e,viewport:l}))}if("UPDATE_VIEWPORT_MAX_SCROLL"===t.type)return!bc(e)||(d=t.payload.maxScroll,il(d,e.viewport.scroll.max))?e:(c=j({},e.viewport,{scroll:j({},e.viewport.scroll,{max:d})}),j({phase:"DRAGGING"},e,{viewport:c}));if("MOVE_UP"!==t.type&&"MOVE_DOWN"!==t.type&&"MOVE_LEFT"!==t.type&&"MOVE_RIGHT"!==t.type)return"DROP_PENDING"===t.type?(l=t.payload.reason,"COLLECTING"!==e.phase&&X(!1,"Can only move into the DROP_PENDING phase from the COLLECTING phase"),j({phase:"DROP_PENDING"},e,{phase:"DROP_PENDING",isWaiting:!0,reason:l})):"DROP_ANIMATE"===t.type?(c=(d=t.payload).completed,l=d.dropDuration,d=d.newHomeClientOffset,"DRAGGING"!==e.phase&&"DROP_PENDING"!==e.phase&&X(!1,"Cannot animate drop from phase "+e.phase),{phase:"DROP_ANIMATING",completed:c,dropDuration:l,newHomeClientOffset:d,dimensions:e.dimensions}):"DROP_COMPLETE"===t.type?{phase:"IDLE",completed:t.payload.completed,shouldFlush:!1}:e;if("COLLECTING"===e.phase||"DROP_PENDING"===e.phase)return e;"DRAGGING"!==e.phase&&X(!1,t.type+" received while not in DRAGGING phase");c=hc({state:e,type:t.type});return c?xc({state:e,impact:c.impact,clientSelection:c.clientSelection,scrollJumpRequest:c.scrollJumpRequest}):e};function Yc(e,t){e=Nl(e.droppable.id,t.draggables);if(!(e.length<=1)){for(var r=e.map(function(e){return e.descriptor.index}),a={},n=1;n<r.length;n++){var o=r[n];o!==r[n-1]+1&&(a[o]=!0)}Object.keys(a).length&&(e=r.map(function(e){return Boolean(a[e])?"[🔥"+e+"]":""+e}).join(", "),Z("\n    Detected non-consecutive <Draggable /> indexes.\n\n    (This can cause unexpected bugs)\n\n    "+e+"\n  "))}}function Zc(e){return il(e,Q)?null:"translate("+e.x+"px, "+e.y+"px)"}function Xc(){return{x:window.pageXOffset,y:window.pageYOffset}}var Qc=function(l){return function(e){var i=e.getState,s=e.dispatch;return function(o){return function(e){var t,r,a,n;"LIFT"!==e.type?o(e):(e=e.payload,r=e.id,t=e.clientSelection,e=e.movementMode,"DROP_ANIMATING"===(n=i()).phase&&s(Hc({completed:n.completed})),"IDLE"!==i().phase&&X(!1,"Unexpected phase to start a drag"),s(Vc()),s({type:"BEFORE_INITIAL_CAPTURE",payload:{draggableId:r,movementMode:e}}),n=l.startPublishing({draggableId:r,scrollOptions:{shouldPublishImmediately:"SNAP"===e}}),r=n.critical,a=n.dimensions,n=n.viewport,Yc(r,a),s({type:"INITIAL_PUBLISH",payload:{critical:r,dimensions:a,clientSelection:t,movementMode:e,viewport:n}}))}}}},ed=function(r){return function(){return function(t){return function(e){"INITIAL_PUBLISH"===e.type&&r.dragging(),"DROP_ANIMATE"===e.type&&r.dropping(e.payload.completed.result.reason),"FLUSH"!==e.type&&"DROP_COMPLETE"!==e.type||r.resting(),t(e)}}}},td={outOfTheWay:"cubic-bezier(0.2, 0, 0, 1)",drop:"cubic-bezier(.2,1,.1,1)"},rd={opacity:{drop:0,combining:.7},scale:{drop:.75}},Jn=.33,a=.55,Mn=.2+"s "+td.outOfTheWay,ad={fluid:"opacity "+Mn,snap:"transform "+Mn+", opacity "+Mn,drop:function(e){e=e+"s "+td.drop;return"transform "+e+", opacity "+e},outOfTheWay:"transform "+Mn,placeholder:"height "+Mn+", width "+Mn+", margin "+Mn},nd={moveTo:Zc,drop:function(e,t){e=Zc(e);return e?t?e+" scale("+rd.scale.drop+")":e:null}},od=Jn,id=a,sd=id-od,ld=function(e){var p=e.getState,m=e.dispatch;return function(u){return function(e){var t,r,a,n,o,i,s,l,c,d;"DROP"!==e.type?u(e):(t=p(),e=e.payload.reason,"COLLECTING"===t.phase?m({type:"DROP_PENDING",payload:{reason:e}}):"IDLE"!==t.phase&&("DROP_PENDING"===t.phase&&t.isWaiting&&X(!1,"A DROP action occurred while DROP_PENDING and still waiting"),"DRAGGING"!==t.phase&&"DROP_PENDING"!==t.phase&&X(!1,"Cannot drop in phase: "+t.phase),s=t.critical,o=(l=t.dimensions).draggables[t.critical.draggable.id],c={reason:e,lastImpact:t.impact,afterCritical:t.afterCritical,onLiftImpact:t.onLiftImpact,home:t.dimensions.droppables[t.critical.droppable.id],viewport:t.viewport,draggables:t.dimensions.draggables},i=c.draggables,a=c.reason,r=c.lastImpact,d=c.home,n=c.viewport,c=c.onLiftImpact,r=(a=r.at&&"DROP"===a?"REORDER"===r.at.type?{impact:r,didDropInsideDroppable:!0}:{impact:j({},r,{displaced:_l}),didDropInsideDroppable:!0}:{impact:Ic({draggables:i,impact:c,destination:d,viewport:n,forceShouldAnimate:!0}),didDropInsideDroppable:!1}).impact,c=(i=a.didDropInsideDroppable)?Al(r):null,d=i?Tl(r):null,n={index:s.draggable.index,droppableId:s.droppable.id},a={draggableId:o.descriptor.id,type:o.descriptor.type,source:n,reason:e,mode:t.movementMode,destination:c,combine:d},i={impact:r,draggable:o,dimensions:l,viewport:t.viewport,afterCritical:t.afterCritical},s=i.impact,n=i.draggable,c=i.dimensions,d=i.viewport,i=i.afterCritical,o=c.draggables,c=c.droppables,l=(l=gc(s))?c[l]:null,c=c[n.descriptor.droppableId],s=Nc({impact:s,draggable:n,draggables:o,afterCritical:i,droppable:l||c,viewport:d}),o=ol(s,n.client.borderBox.center),i={critical:t.critical,afterCritical:t.afterCritical,result:a,impact:r},!il(t.current.client.offset,o)||Boolean(a.combine)?(l={current:t.current.client.offset,destination:o,reason:e},c=l.current,d=l.destination,l=l.reason,s=(c=ll(c,d))<=0?od:1500<=c?id:(d=od+sd*(c/1500),Number(("CANCEL"===l?.6*d:d).toFixed(2))),m({type:"DROP_ANIMATE",payload:{newHomeClientOffset:o,dropDuration:s,completed:i}})):m(Hc({completed:i}))))}}};function cd(e){var t=e.onWindowScroll;var r,a=Fs(function(){t(Xc())}),n=(r=a,{eventName:"scroll",options:{passive:!0,capture:!1},fn:function(e){e.target!==window&&e.target!==window.document||r()}}),o=Ks;function i(){return o!==Ks}return{start:function(){i()&&X(!1,"Cannot start scroll listener when already active"),o=Js(window,[n])},stop:function(){i()||X(!1,"Cannot stop scroll listener when not active"),a.cancel(),o(),o=Ks},isActive:i}}function dd(t){function e(e){r?Z("Announcement already made. Not making a second announcement"):a?Z("\n        Announcements cannot be made asynchronously.\n        Default message has already been announced.\n      "):(r=!0,t(e),clearTimeout(n))}var r=!1,a=!1,n=setTimeout(function(){a=!0});return e.wasCalled=function(){return r},e}function ud(){var a=[];return{add:function(e){var r=setTimeout(function(){return t=r,-1===(e=wl(a,function(e){return e.timerId===t}))&&X(!1,"Could not find timer"),void a.splice(e,1)[0].callback();var t,e});a.push({timerId:r,callback:e})},flush:function(){var e;a.length&&(e=[].concat(a),a.length=0,e.forEach(function(e){clearTimeout(e.timerId),e.callback()}))}}}function pd(e,t){t()}function md(e,t){return{draggableId:e.draggable.id,type:e.droppable.type,source:{droppableId:e.droppable.id,index:e.draggable.index},mode:t}}function hd(e,t,r,a){e&&(e(t,{announce:e=dd(r)}),e.wasCalled())||r(a(t))}function fd(c,d){function t(e){p||X(!1,"Cannot fire onDragEnd when there is no matching onDragStart"),p=null,pd(0,function(){hd(c().onDragEnd,e,d,bl.onDragEnd)})}var u=ud(),p=null;return{beforeCapture:function(t,r){p&&X(!1,"Cannot fire onBeforeCapture as a drag start has already been published"),pd(0,function(){var e=c().onBeforeCapture;e&&e({draggableId:t,mode:r})})},beforeStart:function(t,r){p&&X(!1,"Cannot fire onBeforeDragStart as a drag start has already been published"),pd(0,function(){var e=c().onBeforeDragStart;e&&e(md(t,r))})},start:function(e,t){p&&X(!1,"Cannot fire onBeforeDragStart as a drag start has already been published");var r=md(e,t);p={mode:t,lastCritical:e,lastLocation:r.source,lastCombine:null},u.add(function(){pd(0,function(){hd(c().onDragStart,r,d,bl.onDragStart)})})},update:function(e,t){var r,a,n,o=Al(t),t=Tl(t),i=(p||X(!1,"Cannot fire onDragMove when onDragStart has not been called"),s=e,i=p.lastCritical,!(s===i||(r=s.draggable.id===i.draggable.id&&s.draggable.droppableId===i.draggable.droppableId&&s.draggable.type===i.draggable.type&&s.draggable.index===i.draggable.index,s=s.droppable.id===i.droppable.id&&s.droppable.type===i.droppable.type,r&&s))),s=(i&&(p.lastCritical=e),r=p.lastLocation,s=o,!(null==r&&null==s||null!=r&&null!=s&&r.droppableId===s.droppableId&&r.index===s.index)),l=(s&&(p.lastLocation=o),l=p.lastCombine,a=t,!(null==l&&null==a||null!=l&&null!=a&&l.draggableId===a.draggableId&&l.droppableId===a.droppableId));l&&(p.lastCombine=t),(i||s||l)&&(n=j({},md(e,p.mode),{combine:t,destination:o}),u.add(function(){pd(0,function(){hd(c().onDragUpdate,n,d,bl.onDragUpdate)})}))},flush:function(){p||X(!1,"Can only flush responders while dragging"),u.flush()},drop:t,abort:function(){var e;p&&(e=j({},md(p.lastCritical,p.mode),{combine:null,destination:null,reason:"CANCEL"}),t(e))}}}function gd(e){var t=e.dimensionMarshal,r=e.focusMarshal,a=e.styleMarshal,n=e.getResponders,o=e.announce,e=e.autoScroller;return Ti(Jc,Id(function(){for(var e=arguments.length,o=new Array(e),t=0;t<e;t++)o[t]=arguments[t];return function(n){return function(){var e=n.apply(void 0,arguments),t=function(){throw new Error("Dispatching while constructing your middleware is not allowed. Other middleware would not be applied to this dispatch.")},r={getState:e.getState,dispatch:function(){return t.apply(void 0,arguments)}},a=o.map(function(e){return e(r)}),t=Li.apply(void 0,a)(e.dispatch);return ki(ki({},e),{},{dispatch:t})}}}(ed(a),Ed(t),Qc(t),ld,wd,Sd,xd,Cd(e),vd,kd(r),yd(n,o))))}function bd(){return{additions:{},removals:{},modified:{}}}var vd=function(t){var a=cd({onWindowScroll:function(e){t.dispatch({type:"MOVE_BY_WINDOW_SCROLL",payload:{newScroll:e}})}});return function(r){return function(e){var t;a.isActive()||"INITIAL_PUBLISH"!==e.type||a.start(),!a.isActive()||"DROP_COMPLETE"!==(t=e).type&&"DROP_ANIMATE"!==t.type&&"FLUSH"!==t.type||a.stop(),r(e)}}},yd=function(e,t){var n=fd(e,t);return function(a){return function(r){return function(e){var t;"BEFORE_INITIAL_CAPTURE"===e.type?n.beforeCapture(e.payload.draggableId,e.payload.movementMode):"INITIAL_PUBLISH"===e.type?(t=e.payload.critical,n.beforeStart(t,e.payload.movementMode),r(e),n.start(t,e.payload.movementMode)):"DROP_COMPLETE"===e.type?(t=e.payload.completed.result,n.flush(),r(e),n.drop(t)):(r(e),"FLUSH"===e.type?n.abort():"DRAGGING"===(t=a.getState()).phase&&n.update(t.critical,t.impact))}}}},wd=function(r){return function(t){return function(e){"DROP_ANIMATION_FINISHED"!==e.type?t(e):("DROP_ANIMATING"!==(e=r.getState()).phase&&X(!1,"Cannot finish a drop animating when no drop is occurring"),r.dispatch(Hc({completed:e.completed})))}}},Sd=function(a){var n=null,o=null;return function(r){return function(e){var t;"FLUSH"!==e.type&&"DROP_COMPLETE"!==e.type&&"DROP_ANIMATION_FINISHED"!==e.type||(o&&(cancelAnimationFrame(o),o=null),n&&(n(),n=null)),r(e),"DROP_ANIMATE"===e.type&&(t={eventName:"scroll",options:{capture:!0,passive:!1,once:!0},fn:function(){"DROP_ANIMATING"===a.getState().phase&&a.dispatch(Wc())}},o=requestAnimationFrame(function(){o=null,n=Js(window,[t])}))}}},Ed=function(r){return function(){return function(t){return function(e){"DROP_COMPLETE"!==e.type&&"FLUSH"!==e.type&&"DROP_ANIMATE"!==e.type||r.stopPublishing(),t(e)}}}},kd=function(r){var a=!1;return function(){return function(t){return function(e){"INITIAL_PUBLISH"===e.type?(a=!0,r.tryRecordFocus(e.payload.critical.draggable.id),t(e),r.tryRestoreFocusRecorded()):(t(e),a&&("FLUSH"===e.type?(a=!1,r.tryRestoreFocusRecorded()):"DROP_COMPLETE"===e.type&&(a=!1,(e=e.payload.completed.result).combine&&r.tryShiftRecord(e.draggableId,e.combine.draggableId),r.tryRestoreFocusRecorded())))}}}},Cd=function(n){return function(a){return function(r){return function(e){var t;"DROP_COMPLETE"===(t=e).type||"DROP_ANIMATE"===t.type||"FLUSH"===t.type?(n.stop(),r(e)):"INITIAL_PUBLISH"===e.type?(r(e),"DRAGGING"!==(t=a.getState()).phase&&X(!1,"Expected phase to be DRAGGING after INITIAL_PUBLISH"),n.start(t)):(r(e),n.scroll(a.getState()))}}}},xd=function(r){return function(t){return function(e){t(e),"PUBLISH_WHILE_DRAGGING"!==e.type||"DROP_PENDING"!==(e=r.getState()).phase||e.isWaiting||r.dispatch(qc({reason:e.reason}))}}},Id="undefined"!=typeof window&&window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__?window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__({name:"react-beautiful-dnd"}):Li;function Nd(){var e=Td();return Ad({scrollHeight:e.scrollHeight,scrollWidth:e.scrollWidth,width:e.clientWidth,height:e.clientHeight})}var Ad=function(e){var t=e.scrollHeight,r=e.scrollWidth,a=e.height,e=e.width,r=ol({x:r,y:t},{x:e,y:a});return{x:Math.max(0,r.x),y:Math.max(0,r.y)}},Td=function(){var e=document.documentElement;return e||X(!1,"Cannot find document.documentElement"),e},Od=function(e){var t,r,a=e.critical,n=e.scrollOptions,e=e.registry,o=(t=Xc(),r=Nd(),l=t.y,o=t.x,c=Td(),s=c.clientWidth,c=c.clientHeight,{frame:Ls({top:l,left:o,right:o+s,bottom:l+c}),scroll:{initial:t,current:t,max:r,diff:{value:Q,displacement:Q}}}),i=o.scroll.current,s=a.droppable,l=e.droppable.getAllByType(s.type).map(function(e){return e.callbacks.getDimensionAndWatchScroll(i,n)}),c=e.draggable.getAllByType(a.draggable.type).map(function(e){return e.getDimension(i)});return{dimensions:{draggables:Cl(c),droppables:kl(l)},critical:a,viewport:o}};function Pd(e,t,r){if(r.descriptor.id!==t.id&&r.descriptor.type===t.type){if("virtual"===e.droppable.getById(r.descriptor.droppableId).descriptor.mode)return 1;Z("\n      You are attempting to add or remove a Draggable [id: "+r.descriptor.id+"]\n      while a drag is occurring. This is only supported for virtual lists.\n\n      See https://github.com/atlassian/react-beautiful-dnd/blob/master/docs/patterns/virtual-lists.md\n    ")}}function Ld(a,r){var e,n,o,i,s,l=null,c=(e={callbacks:{publish:r.publishWhileDragging,collectionStarting:r.collectionStarting},registry:a},n=e.registry,o=e.callbacks,i=bd(),s=null,{add:function(e){var t=e.descriptor.id;i.additions[t]=e,i.modified[e.descriptor.droppableId]=!0,i.removals[t]&&delete i.removals[t],d()},remove:function(e){e=e.descriptor;i.removals[e.id]=!0,i.modified[e.droppableId]=!0,i.additions[e.id]&&delete i.additions[e.id],d()},stop:function(){s&&(cancelAnimationFrame(s),s=null,i=bd())}});function d(){s||(o.collectionStarting(),s=requestAnimationFrame(function(){s=null;var e=i,t=e.additions,r=e.removals,e=e.modified,t=Object.keys(t).map(function(e){return n.draggable.getById(e).getDimension(Q)}).sort(function(e,t){return e.descriptor.index-t.descriptor.index}),e=Object.keys(e).map(function(e){return{droppableId:e,scroll:n.droppable.getById(e).callbacks.getScrollWhileDragging()}}),t={additions:t,removals:Object.keys(r),modified:e};i=bd(),o.publish(t)}))}function u(e){l||X(!1,"Should only be subscribed when a collection is occurring");var t=l.critical.draggable;"ADDITION"===e.type&&Pd(a,t,e.value)&&c.add(e.value),"REMOVAL"===e.type&&Pd(a,t,e.value)&&c.remove(e.value)}return{updateDroppableIsEnabled:function(e,t){a.droppable.exists(e)||X(!1,"Cannot update is enabled flag of Droppable "+e+" as it is not registered"),l&&r.updateDroppableIsEnabled({id:e,isEnabled:t})},updateDroppableIsCombineEnabled:function(e,t){l&&(a.droppable.exists(e)||X(!1,"Cannot update isCombineEnabled flag of Droppable "+e+" as it is not registered"),r.updateDroppableIsCombineEnabled({id:e,isCombineEnabled:t}))},scrollDroppable:function(e,t){l&&a.droppable.getById(e).callbacks.scroll(t)},updateDroppableScroll:function(e,t){l&&(a.droppable.exists(e)||X(!1,"Cannot update the scroll on Droppable "+e+" as it is not registered"),r.updateDroppableScroll({id:e,newScroll:t}))},startPublishing:function(e){l&&X(!1,"Cannot start capturing critical dimensions as there is already a collection");var t=a.draggable.getById(e.draggableId),r=a.droppable.getById(t.descriptor.droppableId),t={draggable:t.descriptor,droppable:r.descriptor},r=a.subscribe(u);return l={critical:t,unsubscribe:r},Od({critical:t,registry:a,scrollOptions:e.scrollOptions})},stopPublishing:function(){var e;l&&(c.stop(),e=l.critical.droppable,a.droppable.getAllByType(e.type).forEach(function(e){return e.callbacks.dragStopped()}),l.unsubscribe(),l=null)}}}function Dd(e,t){return"IDLE"===e.phase||"DROP_ANIMATING"===e.phase&&e.completed.result.draggableId!==t&&"DROP"===e.completed.result.reason}function Rd(e){window.scrollBy(e.x,e.y)}function Md(e){var t,r=e.center,a=e.destination,e=e.droppables;return a?(a=e[a]).frame?a:null:(t=r,Sl(zd(e),function(e){return e.frame||X(!1,"Invalid result"),vc(e.frame.pageMarginBox)(t)}))}function jd(e){var t=e.startOfRange,r=e.endOfRange,e=e.current;return 0==(r=r-t)?(Z("\n      Detected distance range of 0 in the fluid auto scroller\n      This is unexpected and would cause a divide by 0 issue.\n      Not allowing an auto scroll\n    "),0):(e-t)/r}function _d(e){var t=e.distanceToEdge,r=e.thresholds,a=e.dragStartTime,e=e.shouldUseTimeDampening;return 0===(r=(t=t)>(r=r).startScrollingFrom?0:t<=r.maxScrollValueAt?Gd:t===r.startScrollingFrom?1:(r=jd({startOfRange:r.maxScrollValueAt,endOfRange:r.startScrollingFrom,current:t}),t=Gd*Vd(1-r),Math.ceil(t)))?0:e?Math.max((t=r,e=a,a=qd,e=Date.now()-e,qd<=e?t:e<Hd?1:(a=jd({startOfRange:Hd,endOfRange:a,current:e}),e=t*Vd(a),Math.ceil(e))),1):r}function Bd(e){var t=e.container,r=e.distanceToEdges,a=e.dragStartTime,n=e.axis,e=e.shouldUseTimeDampening,t={startScrollingFrom:t[n.size]*Fd,maxScrollValueAt:t[n.size]*Ud};return r[n.end]<r[n.start]?_d({distanceToEdge:r[n.end],thresholds:t,dragStartTime:a,shouldUseTimeDampening:e}):-1*_d({distanceToEdge:r[n.start],thresholds:t,dragStartTime:a,shouldUseTimeDampening:e})}function $d(e){var t=e.dragStartTime,r=e.container,a=e.subject,n=e.center,e=e.shouldUseTimeDampening,n={top:n.y-r.top,right:r.right-n.x,bottom:r.bottom-n.y,left:n.x-r.left},o=Bd({container:r,distanceToEdges:n,dragStartTime:t,axis:zl,shouldUseTimeDampening:e}),n=Bd({container:r,distanceToEdges:n,dragStartTime:t,axis:Fl,shouldUseTimeDampening:e}),t=Wd({x:n,y:o});return il(t,Q)||(n=(e={container:r,subject:a,proposedScroll:t}).container,o=e.subject,e=e.proposedScroll,r=o.height>n.height,!(a=(o=o.width>n.width)||r?o&&r?null:{x:o?0:e.x,y:r?0:e.y}:e))||il(a,Q)?null:a}var zd=Y(function(e){return xl(e).filter(function(e){return!!e.isEnabled&&!!e.frame})}),Fd=.25,Ud=.05,Gd=28,Vd=function(e){return Math.pow(e,2)},mn={stopDampeningAt:1200,accelerateAt:360},Hd=mn.accelerateAt,qd=mn.stopDampeningAt,Wd=dl(function(e){return 0===e?0:e}),Kd=dl(function(e){return 0===e?0:0<e?1:-1}),Jd=function(e){var t=e.current,r=e.max,e=e.change,t=m(t,e),e={x:Yd(t.x,r.x),y:Yd(t.y,r.y)};return il(e,Q)?null:e};function Yd(e,t){return e<0?e:t<e?e-t:0}function Zd(e){var t=e.max,r=e.current,e=e.change,t={x:Math.max(r.x,t.x),y:Math.max(r.y,t.y)},e=Kd(e);return!(t=Jd({max:t,current:r,change:e}))||0!==e.x&&0===t.x||0!==e.y&&0===t.y}function Xd(e,t){return Zd({current:e.scroll.current,max:e.scroll.max,change:t})}function Qd(e,t){return!!(e=e.frame)&&Zd({current:e.scroll.current,max:e.scroll.max,change:t})}function eu(e){var t=e.state,r=e.dragStartTime,a=e.shouldUseTimeDampening,n=e.scrollWindow,e=e.scrollDroppable,o=t.current.page.borderBoxCenter,i=t.dimensions.draggables[t.critical.draggable.id].page.marginBox;if(t.isWindowScrollAllowed){var s=t.viewport,l=(c=(s={dragStartTime:r,viewport:s,subject:i,center:o,shouldUseTimeDampening:a}).viewport,l=s.subject,u=s.center,d=s.dragStartTime,s=s.shouldUseTimeDampening,(d=$d({dragStartTime:d,container:c.frame,subject:l,center:u,shouldUseTimeDampening:s}))&&Xd(c,d)?d:null);if(l)return n(l)}var c,d,u=Md({center:o,destination:gc(t.impact),droppables:t.dimensions.droppables});u&&(c=(s={dragStartTime:r,droppable:u,subject:i,center:o,shouldUseTimeDampening:a}).droppable,d=s.subject,n=s.center,l=s.dragStartTime,s=s.shouldUseTimeDampening,r=(t=c.frame)&&(l=$d({dragStartTime:l,container:t.pageMarginBox,subject:d,center:n,shouldUseTimeDampening:s}))&&Qd(c,l)?l:null)&&e(u.descriptor.id,r)}function tu(e){var t=e.scrollDroppable,r=e.scrollWindow,e=e.move,a=nu({scrollWindow:r,scrollDroppable:t}),n=ou({move:e,scrollWindow:r,scrollDroppable:t});return{scroll:function(e){"DRAGGING"===e.phase&&("FLUID"===e.movementMode?a.scroll(e):e.scrollJumpRequest&&n(e))},start:a.start,stop:a.stop}}function ru(){var e=document.querySelector("head");return e||X(!1,"Cannot find the head to append a style to"),e}function au(e){var t=document.createElement("style");return e&&t.setAttribute("nonce",e),t.type="text/css",t}var nu=function(e){function n(e){i||X(!1,"Cannot fluid scroll if not dragging");var t=i.shouldUseTimeDampening,r=(r=i).dragStartTime;eu({state:e,scrollWindow:a,scrollDroppable:o,dragStartTime:r,shouldUseTimeDampening:t})}var t=e.scrollWindow,e=e.scrollDroppable,a=Fs(t),o=Fs(e),i=null;return{start:function(e){i&&X(!1,"Cannot start auto scrolling when already started");function t(){a=!0}var r=Date.now(),a=!1;eu({state:e,dragStartTime:0,shouldUseTimeDampening:!1,scrollWindow:t,scrollDroppable:t}),i={dragStartTime:r,shouldUseTimeDampening:a},a&&n(e)},stop:function(){i&&(a.cancel(),o.cancel(),i=null)},scroll:n}},ou=function(e){function a(e,t){var r,a,n;return Qd(e,t)?(a=t,(r=(n=(r=e).frame)&&Qd(r,a)?Jd({current:n.scroll.current,max:n.scroll.max,change:a}):null)?(n=ol(t,r),i(e.descriptor.id,n),ol(t,n)):(i(e.descriptor.id,t),null)):t}function n(e,t,r){var a;return e&&Xd(t,r)?(e=Xd(e=t,t=r)?(a=e.scroll.max,e=e.scroll.current,Jd({current:e,max:a,change:t})):null)?(a=ol(r,e),s(a),ol(r,a)):(s(r),null):r}var o=e.move,i=e.scrollDroppable,s=e.scrollWindow;return function(e){var t,r=e.scrollJumpRequest;r&&((t=gc(e.impact))||X(!1,"Cannot perform a jump scroll when there is no destination"),t=a(e.dimensions.droppables[t],r))&&(r=e.viewport,r=n(e.isWindowScrollAllowed,r,t))&&(t=m((t=e).current.client.selection,r),o({client:t}))}},iu="data-rbd",su={base:Zn=iu+"-drag-handle",draggableId:Zn+"-draggable-id",contextId:Zn+"-context-id"},lu={base:qe=iu+"-draggable",contextId:qe+"-context-id",id:qe+"-id"},cu={base:c=iu+"-droppable",contextId:c+"-context-id",id:c+"-id"},du={contextId:iu+"-scroll-container-context-id"},uu=function(t){return function(e){return"["+e+'="'+t+'"]'}},pu=function(e,r){return e.map(function(e){var t=e.styles[r];return t?e.selector+" { "+t+" }":""}).join(" ")},mu="pointer-events: none;",hu="undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement?W.useLayoutEffect:W.useEffect;function fu(a,r){var n=K(function(){return e=uu(e=a),t="\n      cursor: -webkit-grab;\n      cursor: grab;\n    ",t={selector:e(su.contextId),styles:{always:"\n          -webkit-touch-callout: none;\n          -webkit-tap-highlight-color: rgba(0,0,0,0);\n          touch-action: manipulation;\n        ",resting:t,dragging:mu,dropAnimating:t}},r="\n      transition: "+ad.outOfTheWay+";\n    ",r=[{selector:e(lu.contextId),styles:{dragging:r,dropAnimating:r,userCancel:r}},t,{selector:e(cu.contextId),styles:{always:"overflow-anchor: none;"}},{selector:"body",styles:{dragging:"\n        cursor: grabbing;\n        cursor: -webkit-grabbing;\n        user-select: none;\n        -webkit-user-select: none;\n        -moz-user-select: none;\n        -ms-user-select: none;\n        overflow-anchor: none;\n      "}}],{always:pu(r,"always"),resting:pu(r,"resting"),dragging:pu(r,"dragging"),dropAnimating:pu(r,"dropAnimating"),userCancel:pu(r,"userCancel")};var e,t,r},[a]),o=(0,W.useRef)(null),i=(0,W.useRef)(null),s=J(Y(function(e){var t=i.current;t||X(!1,"Cannot set dynamic style element if it is not set"),t.textContent=e}),[]),l=J(function(e){var t=o.current;t||X(!1,"Cannot set dynamic style element if it is not set"),t.textContent=e},[]),e=(hu(function(){(o.current||i.current)&&X(!1,"style elements already mounted");var e=au(r),t=au(r);return o.current=e,i.current=t,e.setAttribute(iu+"-always",a),t.setAttribute(iu+"-dynamic",a),ru().appendChild(e),ru().appendChild(t),l(n.always),s(n.resting),function(){function e(e){var t=e.current;t||X(!1,"Cannot unmount ref as it is not set"),ru().removeChild(t),e.current=null}e(o),e(i)}},[r,l,s,n.always,n.resting,a]),J(function(){return s(n.dragging)},[s,n.dragging])),t=J(function(e){s("DROP"===e?n.dropAnimating:n.userCancel)},[s,n.dropAnimating,n.userCancel]),c=J(function(){i.current&&s(n.resting)},[s,n.resting]);return K(function(){return{dragging:e,dropping:t,resting:c}},[e,t,c])}var gu=function(e){return e&&e.ownerDocument?e.ownerDocument.defaultView:window};function bu(e){return e instanceof gu(e).HTMLElement}function vu(e,t){var r="["+su.contextId+'="'+e+'"]',r=El(document.querySelectorAll(r));return r.length?(r=Sl(r,function(e){return e.getAttribute(su.draggableId)===t}))?bu(r)?r:(Z("drag handle needs to be a HTMLElement"),null):(Z('Unable to find drag handle with id "'+t+'" as no handle with a matching id was found'),null):(Z('Unable to find any drag handles in the context "'+e+'"'),null)}function yu(){var a={draggables:{},droppables:{}},r=[];function n(t){r.length&&r.forEach(function(e){return e(t)})}function o(e){return a.draggables[e]||null}function i(e){return a.droppables[e]||null}return{draggable:{register:function(e){n({type:"ADDITION",value:a.draggables[e.descriptor.id]=e})},update:function(e,t){var r=a.draggables[t.descriptor.id];r&&r.uniqueId===e.uniqueId&&(delete a.draggables[t.descriptor.id],a.draggables[e.descriptor.id]=e)},unregister:function(e){var t=e.descriptor.id,r=o(t);r&&e.uniqueId===r.uniqueId&&(delete a.draggables[t],n({type:"REMOVAL",value:e}))},getById:function(e){var t=o(e);return t||X(!1,"Cannot find draggable entry with id ["+e+"]"),t},findById:o,exists:function(e){return Boolean(o(e))},getAllByType:function(t){return yl(a.draggables).filter(function(e){return e.descriptor.type===t})}},droppable:{register:function(e){a.droppables[e.descriptor.id]=e},unregister:function(e){var t=i(e.descriptor.id);t&&e.uniqueId===t.uniqueId&&delete a.droppables[e.descriptor.id]},getById:function(e){var t=i(e);return t||X(!1,"Cannot find droppable entry with id ["+e+"]"),t},findById:i,exists:function(e){return Boolean(i(e))},getAllByType:function(t){return yl(a.droppables).filter(function(e){return e.descriptor.type===t})}},subscribe:function(t){return r.push(t),function(){var e=r.indexOf(t);-1!==e&&r.splice(e,1)}},clean:function(){a.draggables={},a.droppables={},r.length=0}}}function wu(){var e=document.body;return e||X(!1,"Cannot find document.body"),e}var Su=W.default.createContext(null),Eu={position:"absolute",width:"1px",height:"1px",margin:"-1px",border:"0",padding:"0",overflow:"hidden",clip:"rect(0 0 0 0)","clip-path":"inset(100%)"};var ku=0,Cu={separator:"::"};function xu(e,t){return K(function(){return""+e+t.separator+ku++},[(t=void 0===t?Cu:t).separator,e])}function Iu(e){var t=e.contextId,r=e.text,a=xu("hidden-text",{separator:"-"}),n=K(function(){return"rbd-hidden-text-"+(e={contextId:t,uniqueId:a}).contextId+"-"+e.uniqueId;var e},[a,t]);return(0,W.useEffect)(function(){var t=document.createElement("div");return t.id=n,t.textContent=r,t.style.display="none",wu().appendChild(t),function(){var e=wu();e.contains(t)&&e.removeChild(t)}},[n,r]),n}var Nu=W.default.createContext(null),Au="^16.8.5 || ^17.0.0 || ^18.0.0",Tu=/(\d+)\.(\d+)\.(\d+)/,Ou=function(e){var t=Tu.exec(e);return null==t&&X(!1,"Unable to parse React version "+e),{major:Number(t[1]),minor:Number(t[2]),patch:Number(t[3]),raw:e}},Pu=function(e,t){return t.major>e.major||!(t.major<e.major)&&(t.minor>e.minor||!(t.minor<e.minor)&&t.patch>=e.patch)},Lu="\n  We expect a html5 doctype: <!doctype html>\n  This is to ensure consistent browser layout and measurement\n\n  More information: https://github.com/atlassian/react-beautiful-dnd/blob/master/docs/guides/doctype.md\n";function Du(e){e()}function Ru(e,t){Du(function(){(0,W.useEffect)(function(){try{e()}catch(e){Ws("\n          A setup problem was encountered.\n\n          > "+e.message+"\n        ")}},t)})}function Mu(){Ru(function(){var e=Au,t=W.default.version,t=(e=Ou(e),t=Ou(t),Pu(e,t)||Z("\n    React version: ["+t.raw+"]\n    does not satisfy expected peer dependency version: ["+e.raw+"]\n\n    This can result in run time bugs, and even fatal crashes\n  "),document);(t=t.doctype)?("html"!==t.name.toLowerCase()&&Z("\n      Unexpected <!doctype> found: ("+t.name+")\n\n      "+Lu+"\n    "),""!==t.publicId&&Z("\n      Unexpected <!doctype> publicId found: ("+t.publicId+")\n      A html5 doctype does not have a publicId\n\n      "+Lu+"\n    ")):Z("\n      No <!doctype html> found.\n\n      "+Lu+"\n    ")},[])}function ju(e){var t=(0,W.useRef)(e);return(0,W.useEffect)(function(){t.current=e}),t}function _u(){var t=null;function e(){t||X(!1,"Cannot release lock when there is no lock"),t=null}return{isClaimed:function(){return Boolean(t)},isActive:function(e){return e===t},claim:function(e){return t&&X(!1,"Cannot claim lock as it is already claimed"),t=e={abandon:e}},release:e,tryAbandon:function(){t&&(t.abandon(),e())}}}var Bu=27,$u=32,zu=37,Fu=38,Uu=39,Gu=40,Vu=((d={})[13]=!0,d[9]=!0,d),Hu=function(e){Vu[e.keyCode]&&e.preventDefault()},qu=(We="visibilitychange","undefined"!=typeof document&&Sl([We,"ms"+We,"webkit"+We,"moz"+We,"o"+We],function(e){return"on"+e in document})||We),Wu=0,Ku=5;var Ju={type:"IDLE"};function Yu(e){var r=e.cancel,a=e.completed,o=e.getPhase,i=e.setPhase;return[{eventName:"mousemove",fn:function(e){var t,r=e.button,a=e.clientX,n=e.clientY;r===Wu&&(r={x:a,y:n},"DRAGGING"===(a=o()).type?(e.preventDefault(),a.actions.move(r)):("PENDING"!==a.type&&X(!1,"Cannot be IDLE"),n=a.point,n=n,t=r,(Math.abs(t.x-n.x)>=Ku||Math.abs(t.y-n.y)>=Ku)&&(e.preventDefault(),t=a.actions.fluidLift(r),i({type:"DRAGGING",actions:t}))))}},{eventName:"mouseup",fn:function(e){var t=o();("DRAGGING"!==t.type?r:(e.preventDefault(),t.actions.drop({shouldBlockNextClick:!0}),a))()}},{eventName:"mousedown",fn:function(e){"DRAGGING"===o().type&&e.preventDefault(),r()}},{eventName:"keydown",fn:function(e){"PENDING"===o().type?r():e.keyCode===Bu?(e.preventDefault(),r()):Hu(e)}},{eventName:"resize",fn:r},{eventName:"scroll",options:{passive:!0,capture:!1},fn:function(){"PENDING"===o().type&&r()}},{eventName:"webkitmouseforcedown",fn:function(e){var t=o();"IDLE"===t.type&&X(!1,"Unexpected phase"),t.actions.shouldRespectForcePress()?r():e.preventDefault()}},{eventName:qu,fn:r}]}function Zu(){}(Je={})[34]=!0,Je[33]=!0,Je[36]=!0,Je[35]=!0;var Xu=Je;function Qu(t,r){function a(){r(),t.cancel()}return[{eventName:"keydown",fn:function(e){e.keyCode===Bu?(e.preventDefault(),a()):e.keyCode===$u?(e.preventDefault(),r(),t.drop()):e.keyCode===Gu?(e.preventDefault(),t.moveDown()):e.keyCode===Fu?(e.preventDefault(),t.moveUp()):e.keyCode===Uu?(e.preventDefault(),t.moveRight()):e.keyCode===zu?(e.preventDefault(),t.moveLeft()):Xu[e.keyCode]?e.preventDefault():Hu(e)}},{eventName:"mousedown",fn:a},{eventName:"mouseup",fn:a},{eventName:"click",fn:a},{eventName:"touchstart",fn:a},{eventName:"resize",fn:a},{eventName:"wheel",fn:a,options:{passive:!0}},{eventName:qu,fn:a}]}var ep={type:"IDLE"},tp=.15;var rp={input:!0,button:!0,textarea:!0,select:!0,option:!0,optgroup:!0,video:!0,audio:!0};function ap(e,t){t=t.target;return bu(t)&&function e(t,r){var a;return null!=r&&(!!Boolean(rp[r.tagName.toLowerCase()])||"true"===(a=r.getAttribute("contenteditable"))||""===a||r!==t&&e(t,r.parentElement))}(e,t)}Yn="matches";var np="undefined"!=typeof document&&Sl([Yn,"msMatchesSelector","webkitMatchesSelector"],function(e){return e in Element.prototype})||Yn;function op(e,t){return e.closest?e.closest(t):function e(t,r){return null==t?null:t[np](r)?t:e(t.parentElement,r)}(e,t)}function ip(e,t){var t=t.target;return t instanceof gu(t).Element?(t=op(t,"["+su.contextId+'="'+e+'"]'))?bu(t)?t:(Z("drag handle must be a HTMLElement"),null):null:(Z("event.target must be a Element"),null)}function sp(e){e.preventDefault()}function lp(e){var t=e.expected,r=e.phase,a=e.isLockActive,e=e.shouldWarn;return a()?t===r||(e&&Z("\n        Cannot perform action.\n        The actions you used belong to an outdated phase\n\n        Current phase: "+t+"\n        You called an action from outdated phase: "+r+"\n\n        Tips:\n\n        - Do not use preDragActions actions after calling preDragActions.lift()\n      "),!1):(e&&Z("\n        Cannot perform action.\n        The sensor no longer has an action lock.\n\n        Tips:\n\n        - Throw away your action handlers when forceStop() is called\n        - Check actions.isActive() if you really need to\n      "),!1)}function cp(e){var t=e.lockAPI,r=e.store,a=e.registry,e=e.draggableId;return!(t.isClaimed()||((t=a.draggable.findById(e))?!t.options.isEnabled||!Dd(r.getState(),e):(Z("Unable to find draggable with id: "+e),1)))}function dp(e){var t,r,a,n,o,i,s=e.lockAPI,l=e.contextId,c=e.store,d=e.registry,u=e.draggableId,p=e.forceSensorStop,e=e.sourceEvent;return cp({lockAPI:s,store:c,registry:d,draggableId:u})?(t=d.draggable.getById(u),d=l,a=t.descriptor.id,d="["+lu.contextId+'="'+d+'"]',(r=(d=Sl(El(document.querySelectorAll(d)),function(e){return e.getAttribute(lu.id)===a}))?bu(d)?d:(Z("Draggable element is not a HTMLElement"),null):null)?e&&!t.options.canDragInteractiveElements&&ap(r,e)?null:(n=s.claim(p||Ks),o="PRE_DRAG",i=function(e,t){lp({expected:e,phase:o,isLockActive:h,shouldWarn:!0})&&c.dispatch(t())}.bind(null,"DRAGGING"),{isActive:function(){return lp({expected:"PRE_DRAG",phase:o,isLockActive:h,shouldWarn:!1})},shouldRespectForcePress:m,fluidLift:function(e){var t=Fs(function(e){i(function(){return $c({client:e})})});return j({},f({liftActionArgs:{id:u,clientSelection:e,movementMode:"FLUID"},cleanup:function(){return t.cancel()},actions:{move:t}}),{move:t})},snapLift:function(){var e={moveUp:function(){return i(zc)},moveRight:function(){return i(Uc)},moveDown:function(){return i(Fc)},moveLeft:function(){return i(Gc)}};return f({liftActionArgs:{id:u,clientSelection:Ls(r.getBoundingClientRect()).center,movementMode:"SNAP"},cleanup:Ks,actions:e})},abort:function(){lp({expected:"PRE_DRAG",phase:o,isLockActive:h,shouldWarn:!0})&&s.release()}}):(Z("Unable to find draggable element with id: "+u),null)):null;function m(){return t.options.shouldRespectForcePress}function h(){return s.isActive(n)}function f(r){function a(){s.release(),o="COMPLETED"}function t(e,t){void 0===t&&(t={shouldBlockNextClick:!1}),r.cleanup(),t.shouldBlockNextClick&&(t=Js(window,[{eventName:"click",fn:sp,options:{once:!0,passive:!1,capture:!0}}]),setTimeout(t)),a(),c.dispatch(qc({reason:e}))}return"PRE_DRAG"!==o&&(a(),"PRE_DRAG"!==o)&&X(!1,"Cannot lift in phase "+o),c.dispatch({type:"LIFT",payload:r.liftActionArgs}),o="DRAGGING",j({isActive:function(){return lp({expected:"DRAGGING",phase:o,isLockActive:h,shouldWarn:!1})},shouldRespectForcePress:m,drop:function(e){return t("DROP",e)},cancel:function(e){return t("CANCEL",e)}},r.actions)}}var up=[function(a){var r=(0,W.useRef)(Ju),n=(0,W.useRef)(Ks),e=K(function(){return{eventName:"mousedown",fn:function(e){var t;e.defaultPrevented||e.button!==Wu||e.ctrlKey||e.metaKey||e.shiftKey||e.altKey||(t=a.findClosestDraggableId(e))&&(t=a.tryGetLock(t,i,{sourceEvent:e}))&&(e.preventDefault(),e={x:e.clientX,y:e.clientY},n.current(),c(t,e))}}},[a]),t=K(function(){return{eventName:"webkitmouseforcewillbegin",fn:function(e){var t,r;e.defaultPrevented||(t=a.findClosestDraggableId(e))&&(r=a.findOptionsForDraggable(t))&&!r.shouldRespectForcePress&&a.canGetLock(t)&&e.preventDefault()}}},[a]),o=J(function(){n.current=Js(window,[t,e],{passive:!1,capture:!0})},[t,e]),i=J(function(){"IDLE"!==r.current.type&&(r.current=Ju,n.current(),o())},[o]),s=J(function(){var e=r.current;i(),"DRAGGING"===e.type&&e.actions.cancel({shouldBlockNextClick:!0}),"PENDING"===e.type&&e.actions.abort()},[i]),l=J(function(){var e=Yu({cancel:s,completed:i,getPhase:function(){return r.current},setPhase:function(e){r.current=e}});n.current=Js(window,e,{capture:!0,passive:!1})},[s,i]),c=J(function(e,t){"IDLE"!==r.current.type&&X(!1,"Expected to move from IDLE to PENDING drag"),r.current={type:"PENDING",point:t,actions:e},l()},[l]);hu(function(){return o(),function(){n.current()}},[o])},function(n){var o=(0,W.useRef)(Zu),e=K(function(){return{eventName:"keydown",fn:function(e){var t,r;function a(){r||X(!1,"Cannot stop capturing a keyboard drag when not capturing"),r=!1,o.current(),i()}e.defaultPrevented||e.keyCode===$u&&(t=n.findClosestDraggableId(e))&&(t=n.tryGetLock(t,a,{sourceEvent:e}))&&(e.preventDefault(),r=!0,e=t.snapLift(),o.current(),o.current=Js(window,Qu(e,a),{capture:!0,passive:!1}))}}},[n]),i=J(function(){o.current=Js(window,[e],{passive:!1,capture:!0})},[e]);hu(function(){return i(),function(){o.current()}},[i])},function(r){var t=(0,W.useRef)(ep),c=(0,W.useRef)(Ks),d=J(function(){return t.current},[]),a=J(function(e){t.current=e},[]),e=K(function(){return{eventName:"touchstart",fn:function(e){var t;e.defaultPrevented||(t=r.findClosestDraggableId(e))&&(t=r.tryGetLock(t,u,{sourceEvent:e}))&&(e={x:(e=e.touches[0]).clientX,y:e.clientY},c.current(),s(t,e))}}},[r]),n=J(function(){c.current=Js(window,[e],{capture:!0,passive:!1})},[e]),u=J(function(){var e=t.current;"IDLE"!==e.type&&("PENDING"===e.type&&clearTimeout(e.longPressTimerId),a(ep),c.current(),n())},[n,a]),p=J(function(){var e=t.current;u(),"DRAGGING"===e.type&&e.actions.cancel({shouldBlockNextClick:!0}),"PENDING"===e.type&&e.actions.abort()},[u]),o=J(function(){var a,r,n,t,o,e={capture:!0,passive:!1},i={cancel:p,completed:u,getPhase:d},s=Js(window,(a=i.cancel,r=i.completed,n=i.getPhase,[{eventName:"touchmove",options:{capture:!1},fn:function(e){var t,r=n();"DRAGGING"!==r.type?a():(r.hasMoved=!0,t={x:(t=e.touches[0]).clientX,y:t.clientY},e.preventDefault(),r.actions.move(t))}},{eventName:"touchend",fn:function(e){var t=n();("DRAGGING"!==t.type?a:(e.preventDefault(),t.actions.drop({shouldBlockNextClick:!0}),r))()}},{eventName:"touchcancel",fn:function(e){"DRAGGING"!==n().type||e.preventDefault(),a()}},{eventName:"touchforcechange",fn:function(e){var t=n(),r=("IDLE"===t.type&&X(!1),e.touches[0]);if(r&&r.force>=tp){r=t.actions.shouldRespectForcePress();if("PENDING"!==t.type)return!r||t.hasMoved?void e.preventDefault():void a();r&&a()}}},{eventName:qu,fn:a}]),e),l=Js(window,(t=i.cancel,o=i.getPhase,[{eventName:"orientationchange",fn:t},{eventName:"resize",fn:t},{eventName:"contextmenu",fn:function(e){e.preventDefault()}},{eventName:"keydown",fn:function(e){"DRAGGING"!==o().type||e.keyCode===Bu&&e.preventDefault(),t()}},{eventName:qu,fn:t}]),e);c.current=function(){s(),l()}},[p,d,u]),i=J(function(){var e=d(),e=("PENDING"!==e.type&&X(!1,"Cannot start dragging from phase "+e.type),e.actions.fluidLift(e.point));a({type:"DRAGGING",actions:e,hasMoved:!1})},[d,a]),s=J(function(e,t){"IDLE"!==d().type&&X(!1,"Expected to move from IDLE to PENDING drag");var r=setTimeout(i,120);a({type:"PENDING",point:t,actions:e,longPressTimerId:r}),o()},[o,d,a,i]);hu(function(){return n(),function(){c.current();var e=d();"PENDING"===e.type&&(clearTimeout(e.longPressTimerId),a(ep))}},[d,n,a]),hu(function(){return Js(window,[{eventName:"touchmove",fn:function(){},options:{capture:!1,passive:!1}}])},[])}];function pp(e){var t,a=e.contextId,n=e.store,o=e.registry,r=e.customSensors,e=e.enableDefaultSensors,i=[].concat(e?up:[],r||[]),s=(0,W.useState)(_u)[0],l=J(function(e,t){e.isDragging&&!t.isDragging&&s.tryAbandon()},[s]),c=(hu(function(){var t=n.getState();return n.subscribe(function(){var e=n.getState();l(t,e),t=e})},[s,n,l]),hu(function(){return s.tryAbandon},[s.tryAbandon]),J(function(e){return cp({lockAPI:s,registry:o,store:n,draggableId:e})},[s,o,n])),d=J(function(e,t,r){return dp({lockAPI:s,registry:o,contextId:a,store:n,draggableId:e,forceSensorStop:t,sourceEvent:r&&r.sourceEvent?r.sourceEvent:null})},[a,s,o,n]),u=J(function(e){return(t=ip(t=a,e))?t.getAttribute(su.draggableId):null;var t},[a]),p=J(function(e){e=o.draggable.findById(e);return e?e.options:null},[o.draggable]),m=J(function(){s.isClaimed()&&(s.tryAbandon(),"IDLE"!==n.getState().phase)&&n.dispatch(Vc())},[s,n]),h=J(s.isClaimed,[s]),f=K(function(){return{canGetLock:c,tryGetLock:d,findClosestDraggableId:u,findOptionsForDraggable:p,tryReleaseLock:m,isLockClaimed:h}},[c,d,u,p,m,h]);t=i,Du(function(){var e=ju(t);Ru(function(){e.current.length!==t.length&&X(!1,"Cannot change the amount of sensor hooks after mounting")})});for(var g=0;g<i.length;g++)i[g](f)}function mp(e){return{onBeforeCapture:e.onBeforeCapture,onBeforeDragStart:e.onBeforeDragStart,onDragStart:e.onDragStart,onDragEnd:e.onDragEnd,onDragUpdate:e.onDragUpdate}}function hp(e){return e.current||X(!1,"Could not find store from lazy ref"),e.current}function fp(e){var t,r,a,n,o,i,s,l,c,d,u,p,m,h,f=e.contextId,g=e.setCallbacks,b=e.sensors,v=e.nonce,y=e.dragHandleUsageInstructions,w=(0,W.useRef)(null),S=(Mu(),ju(e)),E=J(function(){return mp(S.current)},[S]),k=(r=K(function(){return"rbd-announcement-"+t},[t=f]),a=(0,W.useRef)(null),(0,W.useEffect)(function(){var t=document.createElement("div");return(a.current=t).id=r,t.setAttribute("aria-live","assertive"),t.setAttribute("aria-atomic","true"),j(t.style,Eu),wu().appendChild(t),function(){setTimeout(function(){var e=wu();e.contains(t)&&e.removeChild(t),t===a.current&&(a.current=null)})}},[r]),J(function(e){var t=a.current;t?t.textContent=e:Z('\n      A screen reader message was trying to be announced but it was unable to do so.\n      This can occur if you unmount your <DragDropContext /> in your onDragEnd.\n      Consider calling provided.announce() before the unmount so that the instruction will\n      not be lost for users relying on a screen reader.\n\n      Message not passed to screen reader:\n\n      "'+e+'"\n    ')},[])),C=Iu({contextId:f,text:y}),x=fu(f,v),I=J(function(e){hp(w).dispatch(e)},[]),N=K(function(){return Pi({publishWhileDragging:Rc,updateDroppableScroll:jc,updateDroppableIsEnabled:_c,updateDroppableIsCombineEnabled:Bc,collectionStarting:Mc},I)},[I]),A=(n=K(yu,[]),(0,W.useEffect)(function(){return function(){requestAnimationFrame(n.clean)}},[n]),n),T=K(function(){return Ld(A,N)},[A,N]),O=K(function(){return tu(j({scrollWindow:Rd,scrollDroppable:T.scrollDroppable},Pi({move:$c},I)))},[T.scrollDroppable,I]),P=(o=f,i=(0,W.useRef)({}),s=(0,W.useRef)(null),l=(0,W.useRef)(null),c=(0,W.useRef)(!1),d=J(function(t,e){var r={id:t,focus:e};return i.current[t]=r,function(){var e=i.current;e[t]!==r&&delete e[t]}},[]),u=J(function(e){e=vu(o,e);e&&e!==document.activeElement&&e.focus()},[o]),p=J(function(e,t){s.current===e&&(s.current=t)},[]),m=J(function(){l.current||c.current&&(l.current=requestAnimationFrame(function(){l.current=null;var e=s.current;e&&u(e)}))},[u]),h=J(function(e){s.current=null;var t=document.activeElement;t&&t.getAttribute(su.draggableId)===e&&(s.current=e)},[]),hu(function(){return c.current=!0,function(){c.current=!1;var e=l.current;e&&cancelAnimationFrame(e)}},[]),K(function(){return{register:d,tryRecordFocus:h,tryRestoreFocusRecorded:m,tryShiftRecord:p}},[d,h,m,p])),y=K(function(){return gd({announce:k,autoScroller:O,dimensionMarshal:T,focusMarshal:P,getResponders:E,styleMarshal:x})},[k,O,T,P,E,x]),L=(w.current&&w.current!==y&&Z("unexpected store change"),w.current=y,J(function(){var e=hp(w);"IDLE"!==e.getState().phase&&e.dispatch(Vc())},[])),D=J(function(){var e=hp(w).getState();return e.isDragging||"DROP_ANIMATING"===e.phase},[]),R=(g(K(function(){return{isDragging:D,tryAbort:L}},[D,L])),J(function(e){return Dd(hp(w).getState(),e)},[])),M=J(function(){return bc(hp(w).getState())},[]),v=K(function(){return{marshal:T,focus:P,contextId:f,canLift:R,isMovementAllowed:M,dragHandleUsageInstructionsId:C,registry:A}},[f,T,C,P,R,M,A]);return pp({contextId:f,store:y,registry:A,customSensors:b,enableDefaultSensors:!1!==e.enableDefaultSensors}),(0,W.useEffect)(function(){return L},[L]),W.default.createElement(Nu.Provider,{value:v},W.default.createElement(Fi,{context:Su,store:y},e.children))}var gp=0;function bp(t){var r=K(function(){return""+gp++},[]),a=t.dragHandleUsageInstructions||bl.dragHandleUsageInstructions;return W.default.createElement(el,null,function(e){return W.default.createElement(fp,{nonce:t.nonce,contextId:r,setCallbacks:e,dragHandleUsageInstructions:a,enableDefaultSensors:t.enableDefaultSensors,sensors:t.sensors,onBeforeCapture:t.onBeforeCapture,onBeforeDragStart:t.onBeforeDragStart,onDragStart:t.onDragStart,onDragUpdate:t.onDragUpdate,onDragEnd:t.onDragEnd},t.children)})}function vp(t){return function(e){return t===e}}function yp(e,t){return t(e.overflowX)||t(e.overflowY)}function wp(e){return e={overflowX:(e=window.getComputedStyle(e)).overflowX,overflowY:e.overflowY},yp(e,kp)||yp(e,Cp)}function Sp(e){var t,r;return null==e?null:e===document.body?(t=wu(),(r=document.documentElement)||X(!1),wp(t)&&(t=window.getComputedStyle(r),r={overflowX:t.overflowX,overflowY:t.overflowY},(t=xp)((r=r).overflowX)&&t(r.overflowY)||Z("\n    We have detected that your <body> element might be a scroll container.\n    We have found no reliable way of detecting whether the <body> element is a scroll container.\n    Under most circumstances a <body> scroll bar will be on the <html> element (document.documentElement)\n\n    Because we cannot determine if the <body> is a scroll container, and generally it is not one,\n    we will be treating the <body> as *not* a scroll container\n\n    More information: https://github.com/atlassian/react-beautiful-dnd/blob/master/docs/guides/how-we-detect-scroll-containers.md\n  ")),null):e===document.documentElement?null:wp(e)?e:Sp(e.parentElement)}function Ep(e){return!!e&&("fixed"===window.getComputedStyle(e).position||Ep(e.parentElement))}var kp=vp("scroll"),Cp=vp("auto"),xp=vp("visible"),Ip=function(e){e&&Sp(e.parentElement)&&Z("\n    Droppable: unsupported nested scroll container detected.\n    A Droppable can only have one scroll parent (which can be itself)\n    Nested scroll containers are currently not supported.\n\n    We hope to support nested scroll containers soon: https://github.com/atlassian/react-beautiful-dnd/issues/131\n  ")},Np=function(e){return{x:e.scrollLeft,y:e.scrollTop}},Ap=function(e){return{closestScrollable:Sp(e),isFixedOnPage:Ep(e)}},Tp=function(e){var t,r,a,n=e.ref,o=e.descriptor,i=e.env,s=e.windowScroll,l=e.direction,c=e.isDropDisabled,d=e.isCombineEnabled,e=e.shouldClipSubject,u=i.closestScrollable,p=(p=u,h=Ps(n=n),!p||n!==p?h:(n=h.paddingBox.top-p.scrollTop,t=h.paddingBox.left-p.scrollLeft,m=n+p.scrollHeight,p=t+p.scrollWidth,n=Ns({top:n,right:p,bottom:m,left:t},h.border),Rs({borderBox:n,margin:h.margin,border:h.border,padding:h.padding}))),m=Ts(p,s),h=u?(t=Ps(u),n={scrollHeight:u.scrollHeight,scrollWidth:u.scrollWidth},{client:t,page:Ts(t,s),scroll:Np(u),scrollSize:n,shouldClipSubject:e}):null;return s={descriptor:o,isEnabled:!c,isCombineEnabled:d,isFixedOnPage:i.isFixedOnPage,direction:l,client:p,page:m,closest:h},u=s.descriptor,n=s.isEnabled,e=s.isCombineEnabled,o=s.isFixedOnPage,c=s.direction,d=s.client,i=s.page,s=s.closest,r=s?(a=s.scrollSize,r=s.client,l=Ad({scrollHeight:a.scrollHeight,scrollWidth:a.scrollWidth,height:r.paddingBox.height,width:r.paddingBox.width}),{pageMarginBox:s.page.marginBox,frameClient:r,scrollSize:a,shouldClipSubject:s.shouldClipSubject,scroll:{initial:s.scroll,current:s.scroll,max:l,diff:{value:Q,displacement:Q}}}):null,{descriptor:u,isCombineEnabled:e,isFixedOnPage:o,axis:a="vertical"===c?zl:Fl,isEnabled:n,client:d,page:i,frame:r,subject:hl({page:i,withPlaceholder:null,axis:a,frame:r})}},Op={passive:!1},Pp={passive:!0},Lp=function(e){return e.shouldPublishImmediately?Op:Pp};function Dp(e){e=(0,W.useContext)(e);return e||X(!1,"Could not find required context"),e}var Rp=function(e){return e&&e.env.closestScrollable||null};function Mp(){}var jp={width:0,height:0,margin:{top:0,right:0,bottom:0,left:0}},_p=function(e){var t=e.isAnimatingOpenOnMount,r=e.placeholder,e=e.animate;return t||"close"===e?jp:{height:r.client.borderBox.height,width:r.client.borderBox.width,margin:r.client.margin}};var Bp=W.default.memo(function(e){var t,r,a=(0,W.useRef)(null),n=J(function(){a.current&&(clearTimeout(a.current),a.current=null)},[]),o=e.animate,i=e.onTransitionEnd,s=e.onClose,l=e.contextId,c=(u=(0,W.useState)("open"===e.animate))[0],d=u[1],u=((0,W.useEffect)(function(){return c?"open"!==o?(n(),d(!1),Mp):a.current?Mp:(a.current=setTimeout(function(){a.current=null,d(!1)}),n):Mp},[o,c,n]),J(function(e){"height"===e.propertyName&&(i(),"close"===o)&&s()},[o,s,i])),p=(t={isAnimatingOpenOnMount:c,animate:e.animate,placeholder:e.placeholder},r=t.isAnimatingOpenOnMount,p=t.placeholder,t=t.animate,r=_p({isAnimatingOpenOnMount:r,placeholder:p,animate:t}),{display:p.display,boxSizing:"border-box",width:r.width,height:r.height,marginTop:r.margin.top,marginRight:r.margin.right,marginBottom:r.margin.bottom,marginLeft:r.margin.left,flexShrink:"0",flexGrow:"0",pointerEvents:"none",transition:"none"!==t?ad.placeholder:null});return W.default.createElement(e.placeholder.tagName,{style:p,"data-rbd-placeholder-context-id":l,onTransitionEnd:u,ref:e.innerRef})}),$p=W.default.createContext(null);function zp(e){e&&bu(e)||X(!1,"\n    provided.innerRef has not been provided with a HTMLElement.\n\n    You can find a guide on using the innerRef callback functions at:\n    https://github.com/atlassian/react-beautiful-dnd/blob/master/docs/guides/using-inner-ref.md\n  ")}function Fp(e){return"boolean"==typeof e}function Up(t,e){e.forEach(function(e){return e(t)})}var Gp=[function(e){e=e.props;e.droppableId||X(!1,"A Droppable requires a droppableId prop"),"string"!=typeof e.droppableId&&X(!1,"A Droppable requires a [string] droppableId. Provided: ["+typeof e.droppableId+"]")},function(e){e=e.props;Fp(e.isDropDisabled)||X(!1,"isDropDisabled must be a boolean"),Fp(e.isCombineEnabled)||X(!1,"isCombineEnabled must be a boolean"),Fp(e.ignoreContainerClipping)||X(!1,"ignoreContainerClipping must be a boolean")},function(e){zp((0,e.getDroppableRef)())}],Vp=[function(e){var t=e.props,e=e.getPlaceholderRef;t.placeholder&&!e()&&Z('\n      Droppable setup issue [droppableId: "'+t.droppableId+'"]:\n      DroppableProvided > placeholder could not be found.\n\n      Please be sure to add the {provided.placeholder} React Node as a child of your Droppable.\n      More information: https://github.com/atlassian/react-beautiful-dnd/blob/master/docs/api/droppable.md\n    ')}],Hp=[function(e){e.props.renderClone||X(!1,"Must provide a clone render function (renderClone) for virtual lists")},function(e){(0,e.getPlaceholderRef)()&&X(!1,"Expected virtual list to not have a placeholder")}];yi(Kp,qp=W.default.PureComponent),Kp.getDerivedStateFromProps=function(e,t){return e.shouldAnimate?e.on?{isVisible:!0,data:e.on,animate:"open"}:t.isVisible?{isVisible:!0,data:t.data,animate:"close"}:{isVisible:!1,animate:"close",data:null}:{isVisible:Boolean(e.on),data:e.on,animate:"none"}},Kp.prototype.render=function(){var e;return this.state.isVisible?(e={onClose:this.onClose,data:this.state.data,animate:this.state.animate},this.props.children(e)):null};var qp,Wp=Kp;function Kp(){for(var e,t=arguments.length,r=new Array(t),a=0;a<t;a++)r[a]=arguments[a];return(e=qp.call.apply(qp,[this].concat(r))||this).state={isVisible:Boolean(e.props.on),data:e.props.on,animate:e.props.shouldAnimate&&e.props.on?"open":"none"},e.onClose=function(){"close"===e.state.animate&&e.setState({isVisible:!1})},e}function Jp(e,t){return t?ad.drop(t.duration):e?ad.snap:ad.fluid}function Yp(e,t){return e?t?rd.opacity.drop:rd.opacity.combining:null}function Zp(e){return null!=e.forceShouldAnimate?e.forceShouldAnimate:"SNAP"===e.mode}var Xp={dragging:5e3,dropAnimating:4500};function Qp(e){return"DRAGGING"===e.type?(r=(t=e).dimension.client,a=t.offset,n=t.combineWith,o=t.dropping,n=Boolean(n),t=Zp(t),i=Boolean(o),a=i?nd.drop(a,n):nd.moveTo(a),{position:"fixed",top:r.marginBox.top,left:r.marginBox.left,boxSizing:"border-box",width:r.borderBox.width,height:r.borderBox.height,transition:Jp(t,o),transform:a,opacity:Yp(n,i),zIndex:i?Xp.dropAnimating:Xp.dragging,pointerEvents:"none"}):(r=e,{transform:nd.moveTo(r.offset),transition:r.shouldAnimateDisplacement?null:"none"});var t,r,a,n,o,i}function em(e){var t=xu("draggable"),o=e.descriptor,r=e.registry,i=e.getDraggableRef,a=e.canDragInteractiveElements,n=e.shouldRespectForcePress,s=e.isEnabled,l=K(function(){return{canDragInteractiveElements:a,shouldRespectForcePress:n,isEnabled:s}},[a,s,n]),c=J(function(e){var t,r,a,n=i();return n||X(!1,"Cannot get dimension when no ref is set"),t=o,n=n,void 0===(e=e)&&(e=Q),r=window.getComputedStyle(n),a=n.getBoundingClientRect(),a=Os(a,r),e=Ts(a,e),{descriptor:t,placeholder:{client:a,tagName:n.tagName.toLowerCase(),display:r.display},displaceBy:{x:a.marginBox.width,y:a.marginBox.height},client:a,page:e}},[o,i]),d=K(function(){return{uniqueId:t,descriptor:o,options:l,getDimension:c}},[o,c,l,t]),u=(0,W.useRef)(d),p=(0,W.useRef)(!0);hu(function(){return r.draggable.register(u.current),function(){return r.draggable.unregister(u.current)}},[r.draggable]),hu(function(){var e;p.current?p.current=!1:(e=u.current,u.current=d,r.draggable.update(d,e))},[d,r.draggable])}function tm(a,n,o){Ru(function(){function e(e){return"Draggable[id: "+e+"]: "}var t,r=a.draggableId;r||X(!1,"Draggable requires a draggableId"),"string"!=typeof r&&X(!1,"Draggable requires a [string] draggableId.\n      Provided: [type: "+typeof r+"] (value: "+r+")"),t=a.index,(Number.isInteger?Number.isInteger(t):"number"==typeof t&&isFinite(t)&&Math.floor(t)===t)||X(!1,e(r)+" requires an integer index prop"),"DRAGGING"!==a.mapped.type&&(zp(o()),a.isEnabled)&&!vu(n,r)&&X(!1,e(r)+" Unable to find drag handle")})}function rm(e){e.preventDefault()}function am(e,t){return e===t}var nm=function(e){var t=e.combine,e=e.destination;return e?e.droppableId:t?t.droppableId:null},om=function(e){return e.combine?e.combine.draggableId:null},im=function(e){return e.at&&"COMBINE"===e.at.type?e.at.combine.draggableId:null};function sm(e){return{isDragging:!1,isDropAnimating:!1,isClone:!1,dropAnimation:null,mode:null,draggingOver:null,combineTargetFor:e,combineWith:null}}var lm={mapped:{type:"SECONDARY",offset:Q,combineTargetFor:null,shouldAnimateDisplacement:!0,snapshot:sm(null)}};function cm(){function r(e,t,r,a){var n=r.displaced.visible[e],o=Boolean(a.inVirtualList&&a.effected[e]),i=(i=Tl(r))&&i.draggableId===e?t:null;return n?o?c(i):(t=r.displacedBy.point,t=s(t.x,t.y),l(t,i,n.shouldAnimate)):o?r.displaced.invisible[e]?null:(t=vl(a.displacedBy.point),n=s(t.x,t.y),l(n,i,!0)):c(i)}var s=Y(function(e,t){return{x:e,y:t}}),a=Y(sm),l=Y(function(e,t,r){return{mapped:{type:"SECONDARY",offset:e,combineTargetFor:t=void 0===t?null:t,shouldAnimateDisplacement:r,snapshot:a(t)}}}),c=function(e){return e?l(Q,e,!0):null};return function(e,t){return e.isDragging?e.critical.draggable.id===t.draggableId?null:r(t.draggableId,e.critical.draggable.id,e.impact,e.afterCritical):"DROP_ANIMATING"!==e.phase||(e=e.completed).result.draggableId===t.draggableId?null:r(t.draggableId,e.result.draggableId,e.impact,e.afterCritical)}}var dm=Xn(function(){s=Y(function(e,t){return{x:e,y:t}}),l=Y(function(e,t,r,a,n){return{isDragging:!0,isClone:t,isDropAnimating:Boolean(n),dropAnimation:n,mode:e,draggingOver:r,combineWith:a,combineTargetFor:null}}),c=Y(function(e,t,r,a,n,o,i){return{mapped:{type:"DRAGGING",dropping:null,draggingOver:n,combineWith:o,mode:t,offset:e,dimension:r,forceShouldAnimate:i,snapshot:l(t,a,n,o,null)}}});var s,l,c,r=function(e,t){var r,a,n,o,i;return e.isDragging?e.critical.draggable.id!==t.draggableId?null:(o=e.current.client.offset,r=e.dimensions.draggables[t.draggableId],a=gc(e.impact),i=im(e.impact),n=e.forceShouldAnimate,c(s(o.x,o.y),e.movementMode,r,t.isClone,a,i,n)):"DROP_ANIMATING"!==e.phase||(o=e.completed).result.draggableId!==t.draggableId?null:(r=t.isClone,a=e.dimensions.draggables[t.draggableId],n=(i=o.result).mode,t=nm(i),o=om(i),i={duration:e.dropDuration,curve:td.drop,moveTo:e.newHomeClientOffset,opacity:o?rd.opacity.drop:null,scale:o?rd.scale.drop:null},{mapped:{type:"DRAGGING",offset:e.newHomeClientOffset,dimension:a,dropping:i,draggingOver:t,combineWith:o,mode:n,forceShouldAnimate:null,snapshot:l(n,r,t,o,i)}})},a=cm();return function(e,t){return r(e,t)||a(e,t)||lm}},{dropAnimationFinished:Wc},null,{context:Su,pure:!0,areStatePropsEqual:am})(function(e){var t,r=(0,W.useRef)(null),a=J(function(e){r.current=e},[]),n=J(function(){return r.current},[]),o=(u=Dp(Nu)).contextId,i=u.dragHandleUsageInstructionsId,s=u.registry,l=(u=Dp($p)).type,c=u.droppableId,d=K(function(){return{id:e.draggableId,index:e.index,type:l,droppableId:c}},[e.draggableId,e.index,l,c]),u=e.children,p=e.draggableId,m=e.isEnabled,h=e.shouldRespectForcePress,f=e.canDragInteractiveElements,g=e.isClone,b=e.mapped,v=e.dropAnimationFinished,y=(tm(e,o,n),t=g,Du(function(){var e=(0,W.useRef)(t);Ru(function(){t!==e.current&&X(!1,"Draggable isClone prop value changed during component life")},[t])}),g||em(K(function(){return{descriptor:d,registry:s,getDraggableRef:n,canDragInteractiveElements:f,shouldRespectForcePress:h,isEnabled:m}},[d,s,n,f,h,m])),K(function(){return m?{tabIndex:0,role:"button","aria-describedby":i,"data-rbd-drag-handle-draggable-id":p,"data-rbd-drag-handle-context-id":o,draggable:!1,onDragStart:rm}:null},[o,i,p,m])),w=J(function(e){"DRAGGING"===b.type&&b.dropping&&"transform"===e.propertyName&&v()},[v,b]),g=K(function(){var e=Qp(b),t="DRAGGING"===b.type&&b.dropping?w:null;return{innerRef:a,draggableProps:{"data-rbd-draggable-context-id":o,"data-rbd-draggable-id":p,style:e,onTransitionEnd:t},dragHandleProps:y}},[o,y,p,b,w,a]),S=K(function(){return{draggableId:d.id,type:d.type,source:{index:d.index,droppableId:d.droppableId}}},[d.droppableId,d.id,d.index,d.type]);return u(g,b.snapshot,S)});function um(e){return Dp($p).isUsingCloneFor!==e.draggableId||e.isClone?W.default.createElement(dm,e):null}function pm(e){var t="boolean"!=typeof e.isDragDisabled||!e.isDragDisabled,r=Boolean(e.disableInteractiveElementBlocking),a=Boolean(e.shouldRespectForcePress);return W.default.createElement(um,j({},e,{isClone:!1,isEnabled:t,canDragInteractiveElements:r,shouldRespectForcePress:a}))}function mm(e,t){return e===t.droppable.type}function hm(e,t){return t.draggables[e.draggable.id]}var Qn={mode:"standard",type:"DEFAULT",direction:"vertical",isDropDisabled:!1,isCombineEnabled:!1,ignoreContainerClipping:!1,renderClone:null,getContainerForClone:function(){return document.body||X(!1,"document.body is not ready"),document.body}},fm=Xn(function(){var s={placeholder:null,shouldAnimatePlaceholder:!0,snapshot:{isDraggingOver:!1,draggingOverWith:null,draggingFromThisWith:null,isUsingPlaceholder:!1},useClone:null},l=j({},s,{shouldAnimatePlaceholder:!1}),c=Y(function(e){return{draggableId:e.id,type:e.type,source:{index:e.index,droppableId:e.droppableId}}}),d=Y(function(e,t,r,a,n,o){var i=n.descriptor.id;return n.descriptor.droppableId===e?(e=o?{render:o,dragging:c(n.descriptor)}:null,{placeholder:n.placeholder,shouldAnimatePlaceholder:!1,snapshot:{isDraggingOver:r,draggingOverWith:r?i:null,draggingFromThisWith:i,isUsingPlaceholder:!0},useClone:e}):t?a?{placeholder:n.placeholder,shouldAnimatePlaceholder:!0,snapshot:{isDraggingOver:r,draggingOverWith:i,draggingFromThisWith:null,isUsingPlaceholder:!0},useClone:null}:s:l});return function(e,t){var r,a,n=t.droppableId,o=t.type,i=!t.isDropDisabled,t=t.renderClone;return e.isDragging?(a=e.critical,mm(o,a)?(a=hm(a,e.dimensions),r=gc(e.impact)===n,d(n,i,r,r,a,t)):l):"DROP_ANIMATING"===e.phase?(r=e.completed,mm(o,r.critical)?(a=hm(r.critical,e.dimensions),d(n,i,nm(r.result)===n,gc(r.impact)===n,a,t)):l):"IDLE"===e.phase&&e.completed&&!e.shouldFlush&&(i=e.completed,mm(o,i.critical))?(r=gc(i.impact)===n,a=Boolean(i.impact.at&&"COMBINE"===i.impact.at.type),t=i.critical.droppable.id===n,r?a?s:l:t?s:l):l}},{updateViewportMaxScroll:function(e){return{type:"UPDATE_VIEWPORT_MAX_SCROLL",payload:e}}},null,{context:Su,pure:!0,areStatePropsEqual:am})(function(e){(A=(0,W.useContext)(Nu))||X(!1,"Could not find app context");var t,r,o,i,a,n,s,l,c,d,u,p,m,h,f,g,b,v,y,w,S,E,k,C=A.contextId,x=A.isMovementAllowed,I=(0,W.useRef)(null),N=(0,W.useRef)(null),A=e.children,T=e.droppableId,O=e.type,P=e.mode,L=e.direction,j=e.ignoreContainerClipping,_=e.isDropDisabled,B=e.isCombineEnabled,$=e.snapshot,D=e.useClone,R=e.updateViewportMaxScroll,z=e.getContainerForClone,M=J(function(){return I.current},[]),F=J(function(e){I.current=e},[]),U=J(function(){return N.current},[]),G=J(function(e){N.current=e},[]),V=(t={props:e,getDroppableRef:M,getPlaceholderRef:U},Ru(function(){Up(t,Gp),"standard"===t.props.mode&&Up(t,Vp),"virtual"===t.props.mode&&Up(t,Hp)}),J(function(){x()&&R({maxScroll:Nd()})},[x,R])),H=(r={droppableId:T,type:O,mode:P,direction:L,isDropDisabled:_,isCombineEnabled:B,ignoreContainerClipping:j,getDroppableRef:M},o=(0,W.useRef)(null),i=Dp(Nu),a=xu("droppable"),n=i.registry,s=i.marshal,l=ju(r),c=K(function(){return{id:r.droppableId,type:r.type,mode:r.mode}},[r.droppableId,r.mode,r.type]),d=(0,W.useRef)(c),u=K(function(){return Y(function(e,t){o.current||X(!1,"Can only update scroll when dragging"),s.updateDroppableScroll(c.id,{x:e,y:t})})},[c.id,s]),p=J(function(){var e=o.current;return e&&e.env.closestScrollable?Np(e.env.closestScrollable):Q},[]),m=J(function(){var e=p();u(e.x,e.y)},[p,u]),h=K(function(){return Fs(m)},[m]),f=J(function(){var e=o.current,t=Rp(e);e&&t||X(!1,"Could not find scroll options while scrolling"),(e.scrollOptions.shouldPublishImmediately?m:h)()},[h,m]),g=J(function(e,t){o.current&&X(!1,"Cannot collect a droppable while a drag is occurring");var r=l.current,a=r.getDroppableRef(),n=(a||X(!1,"Cannot collect without a droppable ref"),Ap(a)),t={ref:a,descriptor:c,env:n,scrollOptions:t},a=(o.current=t,Tp({ref:a,descriptor:c,env:n,windowScroll:e,direction:r.direction,isDropDisabled:r.isDropDisabled,isCombineEnabled:r.isCombineEnabled,shouldClipSubject:!r.ignoreContainerClipping})),e=n.closestScrollable;return e&&(e.setAttribute(du.contextId,i.contextId),e.addEventListener("scroll",f,Lp(t.scrollOptions)),Ip(e)),a},[i.contextId,c,f,l]),b=J(function(){var e=o.current,t=Rp(e);return e&&t||X(!1,"Can only recollect Droppable client for Droppables that have a scroll container"),Np(t)},[]),v=J(function(){var e=o.current,t=(e||X(!1,"Cannot stop drag when no active drag"),Rp(e));o.current=null,t&&(h.cancel(),t.removeAttribute(du.contextId),t.removeEventListener("scroll",f,Lp(e.scrollOptions)))},[f,h]),y=J(function(e){var t=o.current,t=(t||X(!1,"Cannot scroll when there is no drag"),Rp(t));t||X(!1,"Cannot scroll a droppable with no closest scrollable"),t.scrollTop+=e.y,t.scrollLeft+=e.x},[]),w=K(function(){return{getDimensionAndWatchScroll:g,getScrollWhileDragging:b,dragStopped:v,scroll:y}},[v,g,b,y]),S=K(function(){return{uniqueId:a,descriptor:c,callbacks:w}},[w,c,a]),hu(function(){return d.current=S.descriptor,n.droppable.register(S),function(){o.current&&(Z("Unsupported: changing the droppableId or type of a Droppable during a drag"),v()),n.droppable.unregister(S)}},[w,c,v,S,s,n.droppable]),hu(function(){o.current&&s.updateDroppableIsEnabled(d.current.id,!r.isDropDisabled)},[r.isDropDisabled,s]),hu(function(){o.current&&s.updateDroppableIsCombineEnabled(d.current.id,r.isCombineEnabled)},[r.isCombineEnabled,s]),W.default.createElement(Wp,{on:e.placeholder,shouldAnimate:e.shouldAnimatePlaceholder},function(e){var t=e.onClose,r=e.data,e=e.animate;return W.default.createElement(Bp,{placeholder:r,onClose:t,innerRef:G,animate:e,contextId:C,onTransitionEnd:V})})),U=K(function(){return{innerRef:F,placeholder:H,droppableProps:{"data-rbd-droppable-id":T,"data-rbd-droppable-context-id":C}}},[C,T,H,F]),q=D?D.dragging.draggableId:null,P=K(function(){return{droppableId:T,type:O,isUsingCloneFor:q}},[T,q,O]);return W.default.createElement($p.Provider,{value:P},A(U,$),D?(E=D.dragging,k=D.render,L=W.default.createElement(um,{draggableId:E.draggableId,index:E.source.index,isClone:!0,isEnabled:!0,shouldRespectForcePress:!1,canDragInteractiveElements:!0},function(e,t){return k(e,t,E)}),Us.default.createPortal(L,z())):null)}),gm=(fm.defaultProps=Qn,a=>{const i=getComputedStyle(document.body).getPropertyValue("--spice-button-disabled"),[s,t]=(0,bi.useState)({width:window.innerWidth}),n=((0,bi.useEffect)(()=>{const e=()=>t({width:window.innerWidth});return window.addEventListener("resize",e),()=>window.removeEventListener("resize",e)},[]),(e,t,r)=>{var a,n,o,t={...t};return e&&t.transform&&(t.transform=(o=(e=t.transform).match(/translate\(([-\d.]+)px,\s*([-\d.]+)px\)/))?(a=(n=s.width>=.95*window.screen.width)?600:430,n=n?120:70,`translate(${Number.parseFloat(o[1])-a}px, ${Number.parseFloat(o[2])-n}px)`):e),{borderRadius:"5px",border:r?"2px solid "+i:"2px solid red",userSelect:"none",paddingTop:12,paddingBottom:12,width:"110px",display:"flex",alignItems:"center",justifyContent:"center",textDecoration:r?"none":"line-through",cursor:"pointer",...t}});return bi.default.createElement(bp,{onDragEnd:e=>{var t,{source:e,destination:r}=e;r&&(t=a.modalConfig.tabs,e=e.index,r=r.index,[e]=(t=Array.from(t)).splice(e,1),t.splice(r,0,e),a.modalConfig.tabs=t,localStorage.setItem(x.tabs,JSON.stringify(a.modalConfig.tabs)),a.updateConfig(a.modalConfig))}},bi.default.createElement(fm,{droppableId:"droppable",direction:"horizontal"},(e,t)=>bi.default.createElement("div",{ref:e.innerRef,style:{display:"flex",paddingTop:8,paddingBottom:8,gap:8},...e.droppableProps},a.modalConfig.tabs.map((r,e)=>bi.default.createElement(pm,{key:r.name,draggableId:r.name,index:e},(e,t)=>bi.default.createElement("div",{ref:e.innerRef,...e.draggableProps,style:n(t.isDragging,e.draggableProps.style,r.enabled)},bi.default.createElement("div",{className:"dnd-box",...e.dragHandleProps,onClick:()=>{return t=r.name,e=a.modalConfig.tabs.map(e=>e.name===t?{...e,enabled:!e.enabled}:e),a.modalConfig.tabs=e,localStorage.setItem(x.tabs,JSON.stringify(a.modalConfig.tabs)),void a.updateConfig(a.modalConfig);var t,e}},bi.default.createElement("svg",{className:"dnd-icon",fill:"currentColor",width:"16",height:"16",viewBox:"0 0 16 16",xmlns:"http://www.w3.org/2000/svg","aria-label":"Drag icon",role:"img"},bi.default.createElement("path",{"fill-rule":"evenodd",d:"M7.375 3.67c0-.645-.56-1.17-1.25-1.17s-1.25.525-1.25 1.17c0 .646.56 1.17 1.25 1.17s1.25-.524 1.25-1.17zm0 8.66c0-.646-.56-1.17-1.25-1.17s-1.25.524-1.25 1.17c0 .645.56 1.17 1.25 1.17s1.25-.525 1.25-1.17zm-1.25-5.5c.69 0 1.25.525 1.25 1.17 0 .645-.56 1.17-1.25 1.17S4.875 8.645 4.875 8c0-.645.56-1.17 1.25-1.17zm5-3.16c0-.645-.56-1.17-1.25-1.17s-1.25.525-1.25 1.17c0 .646.56 1.17 1.25 1.17s1.25-.524 1.25-1.17zm-1.25 7.49c.69 0 1.25.524 1.25 1.17 0 .645-.56 1.17-1.25 1.17s-1.25-.525-1.25-1.17c0-.646.56-1.17 1.25-1.17zM11.125 8c0-.645-.56-1.17-1.25-1.17s-1.25.525-1.25 1.17c0 .645.56 1.17 1.25 1.17s1.25-.525 1.25-1.17z"})),"Extensions"===r.name?"Extens.":r.name)))),e.placeholder)))}),bm=async()=>{const e=new MutationObserver(async()=>{var t;document.querySelector(".GenericModal[aria-label='Settings']")||(t=100,await new Promise(e=>setTimeout(e,t)),Tm("BACKUP"),e.disconnect())});e.observe(document.body,{childList:!0,subtree:!0}),Spicetify.PopupModal.hide()},vm=({CONFIG:e,updateAppConfig:t})=>{const[r,a]=p.default.useState({...e}),[n,o]=p.default.useState(y("settings.versionBtn"));var e=e=>{t({...e}),a({...e})},i=document.querySelector("body > generic-modal button.main-trackCreditsModal-closeBtn");const s=document.querySelector("body > generic-modal > div");i&&s&&(i.onclick=()=>location.reload(),i.setAttribute("style","cursor: pointer;"),s.onclick=e=>{e.target===s&&location.reload()});i=N(x.albumArtBasedColor)?p.default.createElement(p.default.Fragment,null,p.default.createElement(gi,{name:y("settings.albumArtBasedColorsMode"),storageKey:"albumArtBasedColorsMode",modalConfig:r,updateConfig:e,type:"dropdown",options:["monochromeDark","monochromeLight","analogicComplement","analogic","triad","quad"],description:y("settings.albumArtColorsModeToolTip")}),p.default.createElement(gi,{name:y("settings.albumArtBasedColorsVibrancy"),storageKey:"albumArtBasedColorsVibrancy",modalConfig:r,updateConfig:e,type:"dropdown",options:["desaturated","lightVibrant","prominent","vibrant"],description:y("settings.albumArtBasedColorsVibrancyToolTip")})):null;return p.default.createElement("div",{id:"marketplace-config-container"},p.default.createElement("div",{className:"settings-block-top"},p.default.createElement("h2",{className:"settings-heading"},y("settings.optionsHeading")),p.default.createElement(gi,{name:y("settings.starCountLabel"),storageKey:"stars",modalConfig:r,updateConfig:e}),p.default.createElement(gi,{name:y("settings.tagsLabel"),storageKey:"tags",modalConfig:r,updateConfig:e}),p.default.createElement(gi,{name:y("settings.showArchived"),storageKey:"showArchived",modalConfig:r,updateConfig:e}),p.default.createElement(gi,{name:y("settings.devToolsLabel"),storageKey:"themeDevTools",modalConfig:r,updateConfig:e}),p.default.createElement(gi,{name:y("settings.hideInstalledLabel"),storageKey:"hideInstalled",modalConfig:r,updateConfig:e}),p.default.createElement(gi,{name:y("settings.colourShiftLabel"),storageKey:"colorShift",modalConfig:r,updateConfig:e}),p.default.createElement(gi,{name:y("settings.albumArtBasedColors"),storageKey:"albumArtBasedColors",modalConfig:r,updateConfig:e}),i),p.default.createElement("div",{className:"settings-block"},p.default.createElement("h2",{className:"settings-heading"},y("settings.tabsHeading")),p.default.createElement(gm,{modalConfig:r,updateConfig:e}),p.default.createElement("p",{className:"settings-tabs-description"},"(",y("settings.tabsDescription"),")")),p.default.createElement("div",{className:"settings-block"},p.default.createElement("h2",{className:"settings-heading"},y("settings.resetHeading")),p.default.createElement("div",{className:"settings-row"},p.default.createElement("span",{className:"col description"},y("settings.resetDescription")),p.default.createElement("div",{className:"col action"},p.default.createElement(Zo,{onClick:()=>Co()},y("settings.resetBtn"))))),p.default.createElement("div",{className:"settings-block"},p.default.createElement("h2",{className:"settings-heading"},y("settings.backupHeading")),p.default.createElement("div",{className:"settings-row"},p.default.createElement("span",{className:"col description"},y("settings.backupLabel")),p.default.createElement("div",{className:"col action"},p.default.createElement(Zo,{onClick:bm},y("settings.backupBtn"))))),p.default.createElement("div",{className:"settings-block-bottom"},p.default.createElement("div",{className:"settings-row"},p.default.createElement("span",{className:"col description"},y("grid.spicetifyMarketplace")," ",y("settings.versionHeading")," ",$r),p.default.createElement("div",{className:"col action"},p.default.createElement(Zo,{onClick:()=>{Spicetify.Platform.ClipboardAPI.copy($r),o(y("settings.versionCopied")),setTimeout(()=>o(y("settings.versionBtn")),3e3)},classes:["small"]},n)))))},ym=t(F()),f=t(r()),wm=t(U()),Sm=(e=Prism,l=/(?:"(?:\\(?:\r\n|[\s\S])|[^"\\\r\n])*"|'(?:\\(?:\r\n|[\s\S])|[^'\\\r\n])*')/,e.languages.css={comment:/\/\*[\s\S]*?\*\//,atrule:{pattern:RegExp("@[\\w-](?:"+/[^;{\s"']|\s+(?!\s)/.source+"|"+l.source+")*?"+/(?:;|(?=\s*\{))/.source),inside:{rule:/^@[\w-]+/,"selector-function-argument":{pattern:/(\bselector\s*\(\s*(?![\s)]))(?:[^()\s]|\s+(?![\s)])|\((?:[^()]|\([^()]*\))*\))+(?=\s*\))/,lookbehind:!0,alias:"selector"},keyword:{pattern:/(^|[^\w-])(?:and|not|only|or)(?![\w-])/,lookbehind:!0}}},url:{pattern:RegExp("\\burl\\((?:"+l.source+"|"+/(?:[^\\\r\n()"']|\\[\s\S])*/.source+")\\)","i"),greedy:!0,inside:{function:/^url/i,punctuation:/^\(|\)$/,string:{pattern:RegExp("^"+l.source+"$"),alias:"url"}}},selector:{pattern:RegExp(`(^|[{}\\s])[^{}\\s](?:[^{};"'\\s]|\\s+(?![\\s{])|`+l.source+")*(?=\\s*\\{)"),lookbehind:!0},string:{pattern:l,greedy:!0},property:{pattern:/(^|[^-\w\xA0-\uFFFF])(?!\s)[-_a-z\xA0-\uFFFF](?:(?!\s)[-\w\xA0-\uFFFF])*(?=\s*:)/i,lookbehind:!0},important:/!important\b/i,function:{pattern:/(^|[^-a-z0-9])[-a-z0-9]+(?=\()/i,lookbehind:!0},punctuation:/[(){};:,]/},e.languages.css.atrule.inside.rest=e.languages.css,(l=e.languages.markup)&&(l.tag.addInlined("style","css"),l.tag.addAttribute("style","css")),a=>{var e="marketplace-customCSS-preview";const[n,t]=f.default.useState("ADD_SNIPPET"!==a.type&&a.content?.item.code||""),[r,o]=f.default.useState("ADD_SNIPPET"!==a.type&&a.content?.item.title||""),[i,s]=f.default.useState("ADD_SNIPPET"!==a.type&&a.content?.item.description||""),[l,c]=f.default.useState("ADD_SNIPPET"!==a.type&&a.content?.item.imageURL||""),d=()=>r.replace(/\n/g,"").replaceAll(" ","-");const u="marketplace:installed:snippet:"+d(),[p,m]=f.default.useState(!!N(u));let h;return f.default.createElement("div",{id:"marketplace-add-snippet-container"},f.default.createElement("div",{className:"marketplace-customCSS-input-container"},f.default.createElement("label",{htmlFor:"marketplace-custom-css"},y("snippets.customCSS")),f.default.createElement("div",{className:"marketplace-code-editor-wrapper marketplace-code-editor"},f.default.createElement(wm.default,{value:n,onValueChange:e=>t(e),highlight:e=>(0,ym.highlight)(e,ym.languages.css),textareaId:"marketplace-custom-css",textareaClassName:"snippet-code-editor",readOnly:"VIEW_SNIPPET"===a.type,placeholder:y("snippets.customCSSPlaceholder"),style:{}}))),f.default.createElement("div",{className:"marketplace-customCSS-input-container"},f.default.createElement("label",{htmlFor:"marketplace-customCSS-name-submit"},y("snippets.snippetName")),f.default.createElement("input",{id:"marketplace-customCSS-name-submit",className:"marketplace-code-editor",value:r,onChange:e=>{"VIEW_SNIPPET"!==a.type&&o(e.target.value)},placeholder:y("snippets.snippetNamePlaceholder")})),f.default.createElement("div",{className:"marketplace-customCSS-input-container"},f.default.createElement("label",{htmlFor:"marketplace-customCSS-description-submit"},y("snippets.snippetDesc")),f.default.createElement("input",{id:"marketplace-customCSS-description-submit",className:"marketplace-code-editor",value:i,onChange:e=>{"VIEW_SNIPPET"!==a.type&&s(e.target.value)},placeholder:y("snippets.snippetDescPlaceholder")})),f.default.createElement("div",{className:"marketplace-customCSS-input-container"},f.default.createElement("label",{htmlFor:e},y("snippets.snippetPreview")," ","VIEW_SNIPPET"!==a.type&&`(${y("snippets.optional")})`),l&&f.default.createElement("label",{htmlFor:e,style:{textAlign:"center"}},f.default.createElement("img",{className:"marketplace-customCSS-image-preview",src:l,alt:"Preview"}))),"VIEW_SNIPPET"!==a.type&&f.default.createElement(f.default.Fragment,null,f.default.createElement(Zo,{onClick:()=>{h.click()}},l.length?y("snippets.changeImage"):y("snippets.addImage"),f.default.createElement("input",{id:e,type:"file",style:{display:"none"},ref:e=>h=e,onChange:async e=>{if(e.target.files?.[0])try{a=e.target.files?.[0];var t=await new Promise((e,t)=>{const r=new FileReader;r.readAsDataURL(a),r.onload=()=>{e(r.result)},r.onerror=e=>{t(e)}});t&&c(t)}catch(e){console.error(e)}var a}})),f.default.createElement(Zo,{onClick:()=>{var e,t=d(),r=i.trim();p&&"EDIT_SNIPPET"!==a.type?Spicetify.showNotification(y("snippets.duplicateName"),!0):(console.debug("Installing snippet: "+t),a.content&&a.content.item.title!==t&&(console.debug("Deleting outdated snippet: "+a.content.item.title),localStorage.removeItem("marketplace:installed:snippet:"+a.content.item.title),e=N(x.installedSnippets,[]).filter(e=>e!=="marketplace:installed:snippet:"+a.content?.item.title),localStorage.setItem(x.installedSnippets,JSON.stringify(e))),localStorage.setItem(u,JSON.stringify({title:t,code:n,description:r,imageURL:l,custom:!0})),-1===(e=N(x.installedSnippets,[])).indexOf(u)&&(e.push(u),localStorage.setItem(x.installedSnippets,JSON.stringify(e))),t=e.map(e=>N(e)),wo(t),Spicetify.PopupModal.hide(),"EDIT_SNIPPET"===a.type&&location.reload())},disabled:!d()||!n.replace(/\n/g,"\\n")},y("snippets.saveCSS"))),"VIEW_SNIPPET"===a.type&&f.default.createElement(Zo,{onClick:()=>{a.callback?.(),m(!p)}},p?y("remove"):y("install")))}),Em=t(F()),km=t(r()),Cm=t(U()),xm=(Prism.languages.ini={comment:{pattern:/(^[ \f\t\v]*)[#;][^\n\r]*/m,lookbehind:!0},section:{pattern:/(^[ \f\t\v]*)\[[^\n\r\]]*\]?/m,lookbehind:!0,inside:{"section-name":{pattern:/(^\[[ \f\t\v]*)[^ \f\t\v\]]+(?:[ \f\t\v]+[^ \f\t\v\]]+)*/,lookbehind:!0,alias:"selector"},punctuation:/\[|\]/}},key:{pattern:/(^[ \f\t\v]*)[^ \f\n\r\t\v=]+(?:[ \f\t\v]+[^ \f\n\r\t\v=]+)*(?=[ \f\t\v]*=)/m,lookbehind:!0,alias:"attr-name"},value:{pattern:/(=[ \f\t\v]*)[^ \f\n\r\t\v]+(?:[ \f\t\v]+[^ \f\n\r\t\v]+)*/,lookbehind:!0,alias:"attr-value",inside:{"inner-value":{pattern:/^("|').+(?=\1$)/,lookbehind:!0}}},punctuation:/=/},localStorage.getItem(x.themeInstalled)),Im=xm?N(xm):null,Nm=()=>{const[r,t]=km.default.useState(Im?(e=>{let t="";for(const r in e)if(Object.prototype.hasOwnProperty.call(e,r))if("object"==typeof e[r]){t+=`[${r}]
`;for(const a in e[r])Object.prototype.hasOwnProperty.call(e[r],a)&&(t+=`${a}=${e[r][a]}
`)}else t+=`${r}=${e[r]}
`;return t})(Im.schemes):y("devTools.noThemeInstalled"));return km.default.createElement("div",{id:"marketplace-theme-dev-tools-container",className:"marketplace-theme-dev-tools-container"},km.default.createElement("div",{className:"devtools-column"},km.default.createElement("label",{htmlFor:"color-ini-editor"},km.default.createElement("h2",{className:"devtools-heading"},y("devTools.colorIniEditor"))),km.default.createElement("div",{className:"marketplace-code-editor-wrapper marketplace-code-editor"},km.default.createElement(Cm.default,{value:r,onValueChange:e=>t(e),highlight:e=>(0,Em.highlight)(e,Em.languages.ini),textareaId:"color-ini-editor",textareaClassName:"color-ini-editor",readOnly:!Im,placeholder:y("devTools.colorIniEditorPlaceholder"),style:{fontFamily:"monospace",resize:"none"}})),km.default.createElement(Zo,{onClick:()=>{var e=r;{var t;xm?(t=yo(e),Im.schemes=t,localStorage.setItem(xm,JSON.stringify(Im))):Spicetify.showNotification(y("devTools.noThemeManifest"),!0)}}},y("save"))),km.default.createElement("div",{className:"devtools-column"},km.default.createElement("h2",{className:"devtools-heading"},y("devTools.invalidCSS")),km.default.createElement("div",{className:"marketplace-code-editor-wrapper marketplace-code-editor"},function(){var e=document.querySelector("body > style.marketplaceCSS.marketplaceUserCSS")?.innerHTML;if(!e)return["Error: Class name list not found; please create an issue"];var r=[];for(const o of e.matchAll(/.-?[_a-zA-Z]+[_a-zA-Z0-9-]*\s*{/g)){var a=o[0].replace(/{/g,"").trim(),n=a.split(" ");let t;for(let e=0;e<n.length;e++){try{t=document.querySelector(""+n[e])}catch(e){t=document.getElementsByClassName(""+a)}t||r.push(a)}}return r}().map((e,t)=>km.default.createElement("div",{key:t,className:"invalid-css-text"},e)))))},v=t(r());var Am=function(){const[e,t]=v.default.useState(null);return v.default.useEffect(()=>{!async function(){try{var{body:e,tag_name:t,message:r}=await(await fetch(Vr)).json();return e&&t&&!r?{version:t.replace("v",""),changelog:await To(e.match(/## What's Changed([\s\S]*?)(\r\n\r|\n\n##)/)[1],"spicetify","marketplace")}:null}catch(e){return console.error(e),null}}().then(e=>t(e))},[]),v.default.createElement("div",{id:"marketplace-update-container"},v.default.createElement("div",{id:"marketplace-update-description"},v.default.createElement("h4",null,y("updateModal.description")),v.default.createElement("a",{href:Gr+"/tag/v1.0.6"},y("updateModal.currentVersion",{version:$r})),v.default.createElement("a",{href:Gr+"/tag/v"+e?.version},y("updateModal.latestVersion",{version:e?.version}))),v.default.createElement("hr",null),v.default.createElement("div",{id:"marketplace-update-whats-changed"},v.default.createElement("h3",{className:"marketplace-update-header"},y("updateModal.whatsChanged")),v.default.createElement("details",null,v.default.createElement("summary",null,y("updateModal.seeChangelog")),v.default.createElement("ul",{dangerouslySetInnerHTML:{__html:e?.changelog??""}}))),v.default.createElement("hr",null),v.default.createElement("div",{id:"marketplace-update-guide"},v.default.createElement("h3",{className:"marketplace-update-header"},y("updateModal.howToUpgrade")),v.default.createElement("a",{href:"https://github.com/spicetify/marketplace/wiki/Installation"},y("updateModal.viewGuide"))))},Tm=(e,t,r,a,n)=>{e=((e,t,r,a,n)=>{switch(e){case"ADD_SNIPPET":return{title:y("snippets.addTitle"),content:Vo.default.createElement(Sm,{type:e}),isLarge:!0};case"EDIT_SNIPPET":return{title:y("snippets.editTitle"),content:Vo.default.createElement(Sm,{type:e,content:a}),isLarge:!0};case"VIEW_SNIPPET":return{title:y("snippets.viewTitle"),content:Vo.default.createElement(Sm,{type:e,content:a,callback:n}),isLarge:!0};case"RELOAD":return{title:y("reloadModal.title"),content:Vo.default.createElement(ei,null),isLarge:!1};case"SETTINGS":return{title:y("settings.title"),content:Vo.default.createElement(vm,{CONFIG:t,updateAppConfig:r}),isLarge:!0};case"THEME_DEV_TOOLS":return{title:y("devTools.title"),content:Vo.default.createElement(Nm,null),isLarge:!0};case"BACKUP":return{title:y("backupModal.title"),content:Vo.default.createElement(Xo,null),isLarge:!0};case"UPDATE":return{title:y("updateModal.title"),content:Vo.default.createElement(Am,null),isLarge:!0};default:return{title:"",content:Vo.default.createElement("div",null),isLarge:!1}}})(e,t,r,a,n),Spicetify.PopupModal.display(e)},w=t(r()),Om=t(r()),Pm=()=>Om.default.createElement("svg",{role:"img",width:"16",height:"16",viewBox:"0 0 512 512","aria-hidden":"true",xmlns:"http://www.w3.org/2000/svg"},Om.default.createElement("path",{d:"M480 352h-133.5l-45.25 45.25C289.2 409.3 273.1 416 256 416s-33.16-6.656-45.25-18.75L165.5 352H32c-17.67 0-32 14.33-32 32v96c0 17.67 14.33 32 32 32h448c17.67 0 32-14.33 32-32v-96C512 366.3 497.7 352 480 352zM432 456c-13.2 0-24-10.8-24-24c0-13.2 10.8-24 24-24s24 10.8 24 24C456 445.2 445.2 456 432 456zM233.4 374.6C239.6 380.9 247.8 384 256 384s16.38-3.125 22.62-9.375l128-128c12.49-12.5 12.49-32.75 0-45.25c-12.5-12.5-32.76-12.5-45.25 0L288 274.8V32c0-17.67-14.33-32-32-32C238.3 0 224 14.33 224 32v242.8L150.6 201.4c-12.49-12.5-32.75-12.5-45.25 0c-12.49 12.5-12.49 32.75 0 45.25L233.4 374.6z",fill:"currentColor"})),Lm=t(r()),Dm=()=>Lm.default.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",role:"img","aria-label":"GitHub Icon"},Lm.default.createElement("path",{d:"M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z",fill:"currentColor"})),Rm=t(r()),Mm=()=>Rm.default.createElement("svg",{role:"img",width:"16",height:"16",viewBox:"0 0 448 512","aria-hidden":"true",xmlns:"http://www.w3.org/2000/svg"},Rm.default.createElement("path",{d:"M53.21 467c1.562 24.84 23.02 45 47.9 45h245.8c24.88 0 46.33-20.16 47.9-45L416 128H32L53.21 467zM432 32H320l-11.58-23.16c-2.709-5.42-8.25-8.844-14.31-8.844H153.9c-6.061 0-11.6 3.424-14.31 8.844L128 32H16c-8.836 0-16 7.162-16 16V80c0 8.836 7.164 16 16 16h416c8.838 0 16-7.164 16-16V48C448 39.16 440.8 32 432 32z",fill:"currentColor"})),jm=t(r()),_m=e=>{return jm.default.createElement("div",{className:"marketplace-card__authors"},e.authors.map(e=>jm.default.createElement("a",{title:e.name,className:"marketplace-card__author",href:e.url,draggable:"false",dir:"auto",target:"_blank",rel:"noopener noreferrer",onClick:e=>e.stopPropagation(),key:e.name+e.url},e.name)))},Bm=t(r()),$m=a=>{const[e,t]=Bm.default.useState(!1),n={[y("grid.externalJS")]:"external JS",[y("grid.archived")]:"archived",[y("grid.dark")]:"dark",[y("grid.light")]:"light"};var r=e=>{return e.filter((e,t,r)=>r.indexOf(e)===t).reduce((e,t)=>{var r=n[t]||t;return!a.showTags&&t!==y("grid.externalJS")&&t!==y("grid.archived")||e.push(Bm.default.createElement("li",{className:"marketplace-card__tag",draggable:!1,"data-tag":r},t)),e},[])};let o=[...a.tags??[]].sort(e=>e===y("grid.externalJS")||e===y("grid.archived")?-1:1),i=[];return 1<o.length-4&&(i=a.tags.slice(4),o=o.slice(0,4)),Bm.default.createElement("div",{className:"marketplace-card__tags-container"},Bm.default.createElement("ul",{className:"marketplace-card__tags"},r(o),i.length&&e?r(i):null),i.length&&!e?Bm.default.createElement("button",{className:"marketplace-card__tags-more-btn",onClick:e=>{e.stopPropagation(),t(!0)}},"..."):null)},zm=window.Spicetify,Fm=class extends w.default.Component{tags;menuType;localStorageKey;key=null;type=Fm;constructor(e){super(e),this.menuType=zm.ReactComponent.Menu,this.localStorageKey=Oo(e),Object.assign(this,e),this.tags=e.item.tags||[],e.item.include&&this.tags.push(y("grid.externalJS")),e.item.archived&&this.tags.push(y("grid.archived")),this.state={installed:null!==localStorage.getItem(this.localStorageKey),stars:this.props.item.stars||0,tagsExpanded:!1,externalUrl:this.props.item.user&&this.props.item.repo?`https://github.com/${this.props.item.user}/`+this.props.item.repo:"",lastUpdated:this.props.item.user&&this.props.item.repo?this.props.item.lastUpdated:void 0,created:this.props.item.user&&this.props.item.repo?this.props.item.created:void 0}}isInstalled(){return null!==localStorage.getItem(this.localStorageKey)}async componentDidMount(){if("Installed"===this.props.CONFIG.activeTab&&"snippet"!==this.props.type){var e=`https://api.github.com/repos/${this.props.item.user}/`+this.props.item.repo,{stargazers_count:e,pushed_at:t}=await fetch(e).then(e=>e.json());if(this.state.stars!==e&&this.props.CONFIG.visual.stars&&console.debug("Stars updated to: "+e),this.state.lastUpdated!==t)switch(console.debug("New update pushed at: "+t),this.props.type){case"extension":this.installExtension();break;case"theme":this.installTheme(!0)}}}buttonClicked(){if("extension"===this.props.type)this.isInstalled()?(console.debug("Extension already installed, removing"),this.removeExtension()):this.installExtension(),Tm("RELOAD");else if("theme"===this.props.type){var e=localStorage.getItem(x.themeInstalled),e=e?N(e,{}):{};if(this.isInstalled())console.debug("Theme already installed, removing"),this.removeTheme(this.localStorageKey);else{var t=localStorage.getItem(x.localTheme);if(null!=t&&"marketplace"!==t.toLowerCase())return void zm.showNotification(y("notifications.wrongLocalTheme"),!0,5e3);this.removeTheme(),this.installTheme()}(this.props.item.manifest?.include||e.include)&&Tm("RELOAD")}else"app"===this.props.type?window.open(this.state.externalUrl,"_blank"):"snippet"===this.props.type?this.isInstalled()?(console.debug("Snippet already installed, removing"),this.removeSnippet()):this.installSnippet():console.error("Unknown card type")}installExtension(){var e,t,r,a,n,o,i,s,l,c,d,u;console.debug("Installing extension "+this.localStorageKey),this.props.item?({manifest:u,title:e,subtitle:t,authors:r,user:a,repo:n,branch:o,imageURL:i,extensionURL:s,readmeURL:l,lastUpdated:c,created:d}=this.props.item,localStorage.setItem(this.localStorageKey,JSON.stringify({manifest:u,type:this.props.type,title:e,subtitle:t,authors:r,user:a,repo:n,branch:o,imageURL:i,extensionURL:s,readmeURL:l,stars:this.state.stars,lastUpdated:c,created:d})),-1===(u=N(x.installedExtensions,[])).indexOf(this.localStorageKey)&&(u.push(this.localStorageKey),localStorage.setItem(x.installedExtensions,JSON.stringify(u))),console.debug("Installed"),this.setState({installed:!0})):zm.showNotification(y("notifications.extensionInstallationError"),!0)}removeExtension(){var e;localStorage.getItem(this.localStorageKey)&&(console.debug("Removing extension "+this.localStorageKey),localStorage.removeItem(this.localStorageKey),e=N(x.installedExtensions,[]).filter(e=>e!==this.localStorageKey),localStorage.setItem(x.installedExtensions,JSON.stringify(e)),console.debug("Removed"),this.setState({installed:!1}))}async installTheme(r=!1){var a=this.props["item"];if(a){console.debug("Installing theme "+this.localStorageKey);let e={},t=null;r?({schemes:r,activeScheme:n}=N(this.localStorageKey,{}),e=r,t=n):a.schemesURL&&(r=await(await fetch(a.schemesURL)).text(),e=yo(r));var n=t||Object.keys(e)[0]||null,{manifest:r,title:o,subtitle:i,authors:s,user:l,repo:c,branch:d,imageURL:u,extensionURL:p,readmeURL:m,cssURL:h,schemesURL:f,include:g,lastUpdated:b,created:v}=(console.debug(e,n),a),r=(localStorage.setItem(this.localStorageKey,JSON.stringify({manifest:r,type:this.props.type,title:o,subtitle:i,authors:s,user:l,repo:c,branch:d,imageURL:u,extensionURL:p,readmeURL:m,stars:this.state.stars,tags:this.tags,cssURL:h,schemesURL:f,include:g,schemes:e,activeScheme:n,lastUpdated:b,created:v})),N(x.installedThemes,[]));-1===r.indexOf(this.localStorageKey)&&(r.push(this.localStorageKey),localStorage.setItem(x.installedThemes,JSON.stringify(r)),localStorage.setItem(x.themeInstalled,this.localStorageKey)),console.debug("Installed"),a.include||(this.fetchAndInjectUserCSS(this.localStorageKey),this.props.updateActiveTheme(this.localStorageKey),this.props.updateColourSchemes(e,n),(o=this.props.item.manifest?.name)&&(zm.Config.current_theme=o),n&&(zm.Config.color_scheme=n)),this.setState({installed:!0})}else zm.showNotification(y("notifications.themeInstallationError"),!0)}removeTheme(e){const t=e||localStorage.getItem(x.themeInstalled);var e=t&&localStorage.getItem(t);t&&e&&(console.debug("Removing theme "+t),localStorage.removeItem(t),localStorage.removeItem(x.themeInstalled),e=N(x.installedThemes,[]).filter(e=>e!==t),localStorage.setItem(x.installedThemes,JSON.stringify(e)),console.debug("Removed"),this.fetchAndInjectUserCSS(null),this.props.updateActiveTheme(null),this.props.updateColourSchemes(null,null),zm.Config.current_theme="marketplace",zm.Config.color_scheme="marketplace",this.setState({installed:!1}))}installSnippet(){console.debug("Installing snippet "+this.localStorageKey),localStorage.setItem(this.localStorageKey,JSON.stringify({code:this.props.item.code,title:this.props.item.title,description:this.props.item.description,imageURL:this.props.item.imageURL}));var e=N(x.installedSnippets,[]),e=(-1===e.indexOf(this.localStorageKey)&&(e.push(this.localStorageKey),localStorage.setItem(x.installedSnippets,JSON.stringify(e))),e.map(e=>N(e)));wo(e),this.setState({installed:!0})}removeSnippet(){localStorage.removeItem(this.localStorageKey);var e=N(x.installedSnippets,[]).filter(e=>e!==this.localStorageKey),e=(localStorage.setItem(x.installedSnippets,JSON.stringify(e)),e.map(e=>N(e)));wo(e),this.setState({installed:!1})}async fetchAndInjectUserCSS(e){try{var t=window.sessionStorage.getItem("marketplace-request-tld")||void 0,r=e?await No(this.props.item,t):void 0;Io(r)}catch(e){console.warn(e)}}openReadme(){this.props.item?.manifest?.readme?zm.Platform.History.push({pathname:Ur+"/readme",state:{data:{title:this.props.item.title,user:this.props.item.user,repo:this.props.item.repo,branch:this.props.item.branch,readmeURL:this.props.item.readmeURL,type:this.props.type,install:this.buttonClicked.bind(this),isInstalled:this.isInstalled.bind(this)}}}):zm.showNotification(y("notifications.noReadmeFile"),!0)}render(){var e,t,r=this.isInstalled();return"Installed"!==this.props.CONFIG.activeTab||r?(e=["main-card-card","marketplace-card--"+this.props.type],r&&e.push("marketplace-card--installed"),t=[],"snippet"!==this.props.type&&this.props.visual.stars&&t.push("★ "+this.state.stars),w.default.createElement("div",{className:e.join(" "),onClick:()=>{if("snippet"===this.props.type){var e=this.props.item.title.replace(/\n/g,"");if(N("marketplace:installed:snippet:"+e)?.custom)return Tm("EDIT_SNIPPET",void 0,void 0,this.props);Tm("VIEW_SNIPPET",void 0,void 0,this.props,this.buttonClicked.bind(this))}else this.openReadme()}},w.default.createElement("div",{className:"main-card-draggable",draggable:"true"},w.default.createElement("div",{className:"main-card-imageContainer"},w.default.createElement("div",{className:"main-cardImage-imageWrapper"},w.default.createElement("div",null,w.default.createElement("img",{alt:"","aria-hidden":"false",draggable:"false",loading:"lazy",src:this.props.item.imageURL,className:"main-image-image main-cardImage-image",onError:e=>{e.currentTarget.setAttribute("src","data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNkYAAAAAYAAjCB0C8AAAAASUVORK5CYII"),e.currentTarget.closest(".main-cardImage-imageWrapper")?.classList.add("main-cardImage-imageWrapper--error")}})))),w.default.createElement("div",{className:"main-card-cardMetadata"},w.default.createElement("a",{draggable:"false",title:"snippet"===this.props.type?this.props.item.title:this.props.item.manifest?.name,className:"main-cardHeader-link",dir:"auto",href:"snippet"!==this.props.type?this.state.externalUrl:"https://github.com/spicetify/marketplace/blob/main/resources/snippets.json",target:"_blank",rel:"noopener noreferrer",onClick:e=>e.stopPropagation()},w.default.createElement("div",{className:"main-cardHeader-text main-type-balladBold"},this.props.item.title)),w.default.createElement("div",{className:"main-cardSubHeader-root main-type-mestoBold marketplace-cardSubHeader"},this.props.item.authors&&w.default.createElement(_m,{authors:this.props.item.authors}),w.default.createElement("span",null,t.join(" ‒ "))),w.default.createElement("p",{className:"marketplace-card-desc"},"snippet"===this.props.type?this.props.item.description:this.props.item.manifest?.description),this.props.item.lastUpdated&&w.default.createElement("p",{className:"marketplace-card-desc"},y("grid.lastUpdated",{val:new Date(this.props.item.lastUpdated),formatParams:{val:{year:"numeric",month:"long",day:"numeric"}}})),this.tags.length?w.default.createElement("div",{className:"marketplace-card__bottom-meta main-type-mestoBold"},w.default.createElement($m,{tags:this.tags,showTags:this.props.CONFIG.visual.tags})):null,r&&w.default.createElement("div",{className:"marketplace-card__bottom-meta main-type-mestoBold"},"✓ ",y("grid.installed")),w.default.createElement(fi,{label:"app"===this.props.type?y("github"):y(r?"remove":"install"),renderInline:!0},w.default.createElement("div",{className:"main-card-PlayButtonContainer"},w.default.createElement(Zo,{classes:["marketplace-installButton"],type:"circle",label:"app"===this.props.type?y("github"):y(r?"remove":"install"),onClick:e=>{e.stopPropagation(),this.buttonClicked()}},"app"===this.props.type?w.default.createElement(Dm,null):r?w.default.createElement(Mm,null):w.default.createElement(Pm,null)))))))):(console.debug("Card item not installed"),null)}},Um=_r()(Fm),Gm=t(r()),Vm=class extends Gm.default.Component{render(){return Gm.default.createElement("div",{style:{marginTop:"60px"},onClick:this.props.onClick},Gm.default.createElement("p",{style:{fontSize:100,lineHeight:"65px"}},"»"),Gm.default.createElement("span",{style:{fontSize:20}},"Load more"))}},Hm=t(r()),qm=()=>Hm.default.createElement("svg",{style:{marginTop:"60px"},width:"100px",height:"100px",viewBox:"0 0 100 100",preserveAspectRatio:"xMidYMid",role:"img","aria-label":"Loading Icon"},Hm.default.createElement("circle",{cx:"50",cy:"50",r:"0",fill:"none",stroke:"currentColor",strokeWidth:"2"},Hm.default.createElement("animate",{attributeName:"r",repeatCount:"indefinite",dur:"1s",values:"0;40",keyTimes:"0;1",keySplines:"0 0.2 0.8 1",calcMode:"spline",begin:"0s"}),Hm.default.createElement("animate",{attributeName:"opacity",repeatCount:"indefinite",dur:"1s",values:"1;0",keyTimes:"0;1",keySplines:"0.2 0 0.8 1",calcMode:"spline",begin:"0s"})),Hm.default.createElement("circle",{cx:"50",cy:"50",r:"0",fill:"none",stroke:"currentColor",strokeWidth:"2"},Hm.default.createElement("animate",{attributeName:"r",repeatCount:"indefinite",dur:"1s",values:"0;40",keyTimes:"0;1",keySplines:"0 0.2 0.8 1",calcMode:"spline",begin:"-0.5s"}),Hm.default.createElement("animate",{attributeName:"opacity",repeatCount:"indefinite",dur:"1s",values:"1;0",keyTimes:"0;1",keySplines:"0.2 0 0.8 1",calcMode:"spline",begin:"-0.5s"}))),Wm=t(r()),Km=()=>Wm.default.createElement("svg",{role:"img",width:"16",height:"16",viewBox:"0 0 24 24","aria-hidden":"true",xmlns:"http://www.w3.org/2000/svg"},Wm.default.createElement("path",{d:"M24 13.616v-3.232c-1.651-.587-2.694-.752-3.219-2.019v-.001c-.527-1.271.1-2.134.847-3.707l-2.285-2.285c-1.561.742-2.433 1.375-3.707.847h-.001c-1.269-.526-1.435-1.576-2.019-3.219h-3.232c-.582 1.635-.749 2.692-2.019 3.219h-.001c-1.271.528-2.132-.098-3.707-.847l-2.285 2.285c.745 1.568 1.375 2.434.847 3.707-.527 1.271-1.584 1.438-3.219 2.02v3.232c1.632.58 2.692.749 3.219 2.019.53 1.282-.114 2.166-.847 3.707l2.285 2.286c1.562-.743 2.434-1.375 3.707-.847h.001c1.27.526 1.436 1.579 2.019 3.219h3.232c.582-1.636.75-2.69 2.027-3.222h.001c1.262-.524 2.12.101 3.698.851l2.285-2.286c-.744-1.563-1.375-2.433-.848-3.706.527-1.271 1.588-1.44 3.221-2.021zm-12 2.384c-2.209 0-4-1.791-4-4s1.791-4 4-4 4 1.791 4 4-1.791 4-4 4z",fill:"currentColor"})),Jm=t(r()),Ym=()=>Jm.default.createElement("svg",{className:"devtools-icon",version:"1.1",viewBox:"1 1 22 22",xmlSpace:"preserve",xmlns:"http://www.w3.org/2000/svg",role:"img","aria-label":"Developer Tools Icon"},Jm.default.createElement("g",{className:"devtools-icon-internal",id:"grid_system"}),Jm.default.createElement("g",{id:"_icons"},Jm.default.createElement("path",{d:"M18,12v-0.9l0.7-5.7C18.8,4.5,18.6,3.7,18,3c-0.6-0.6-1.4-1-2.2-1H8.3C7.4,2,6.6,2.4,6,3C5.4,3.7,5.2,4.5,5.3,5.4L6,11.1   V12c0,1.6,1.3,2.9,2.8,3l-0.4,2.9c-0.1,1,0.2,2.1,0.8,2.9S11,22,12,22s2-0.5,2.7-1.2s1-1.8,0.8-2.9L15.2,15   C16.7,14.9,18,13.6,18,12z M7.5,4.3C7.7,4.1,8,4,8.3,4H13v2c0,0.6,0.4,1,1,1s1-0.4,1-1V4h0.7c0.3,0,0.6,0.1,0.8,0.3   c0.2,0.2,0.3,0.5,0.2,0.8L16.1,10H7.9L7.3,5.1C7.2,4.8,7.3,4.6,7.5,4.3z M13.2,19.4c-0.6,0.7-1.8,0.7-2.4,0   c-0.3-0.4-0.4-0.8-0.4-1.3l0.5-3.2h2.3l0.5,3.2C13.7,18.6,13.5,19.1,13.2,19.4z M15,13h-1h-4H9c-0.6,0-1-0.4-1-1h8   C16,12.6,15.6,13,15,13z"}))),S=t(r()),Zm=t(Be()),Xm=_r()(class extends S.default.Component{render(){var e=this.props["t"];return this.props.item.enabled?S.default.createElement("li",{className:"marketplace-tabBar-headerItem","data-tab":this.props.item.value,onClick:e=>{e.preventDefault(),this.props.switchTo(this.props.item)}},S.default.createElement("a",{"aria-current":"page",className:"marketplace-tabBar-headerItemLink "+(this.props.item.active?"marketplace-tabBar-active":""),draggable:"false",href:"##"},S.default.createElement("span",{className:"main-type-mestoBold"},e("tabs."+this.props.item.value)))):null}}),Qm=S.default.memo(function({items:e,switchTo:t}){return S.default.createElement("li",{className:"marketplace-tabBar-headerItem"},S.default.createElement(Zm.default,{className:"main-type-mestoBold",options:e,value:"More",placeholder:"More",onChange:t}))}),eh=e=>{const t=(0,S.useRef)(null),r=(0,S.useCallback)(()=>{var e=document.querySelector(".main-topBar-topbarContentWrapper");t?.current&&e?e.appendChild(t.current):setTimeout(r,100)},[t.current]);return(0,S.useEffect)(()=>(r(),()=>(t.current||document.querySelector(".marketplace-tabBar"))?.remove())),S.default.createElement(th,{ref:t,links:e.links,activeLink:e.activeLink,switchCallback:e.switchCallback})},th=S.default.forwardRef(({links:e,activeLink:r,switchCallback:t},a)=>{const n=(0,S.useRef)(null),[o,i]=(0,S.useState)([0]),[s,l]=(0,S.useState)(0),[c,d]=(0,S.useState)([0]),u=e.map(({name:e,enabled:t})=>{return{label:e,value:e,active:e===r,enabled:t}});return(0,S.useEffect)(()=>{if(n.current){const e=new ResizeObserver(e=>l(e[0].contentRect.width));return e.observe(n.current),()=>{e.disconnect()}}},[n.current]),(0,S.useEffect)(()=>{var e;n.current&&(e=Array.from(n.current.children).map(e=>e.clientWidth),i(e))},[e]),(0,S.useEffect)(()=>{if(n.current)if(o.reduce((e,t)=>e+t,0)<=s)d([]);else{var e=Math.max(...o);const a=[];let r=e;o.forEach((e,t)=>{s>=r+e?r+=e:a.push(t)}),d(a)}},[s,o]),S.default.createElement("nav",{className:"marketplace-tabBar marketplace-tabBar-nav",ref:a},S.default.createElement("ul",{className:"marketplace-tabBar-header",ref:n},u.filter((e,t)=>!c.includes(t)).map(e=>S.default.createElement(Xm,{key:e.value,item:e,switchTo:t})),c.length||0===o.length?S.default.createElement(Qm,{items:c.map(e=>u[e]).filter(e=>e),switchTo:t}):null))}),rh=window.Spicetify,ah=_r()(class extends o.default.Component{constructor(e){super(e),Object.assign(this,e),this.updateAppConfig=e.updateAppConfig.bind(this),this.sortConfig={by:N(x.sort,"top")},this.state={version:$r,newUpdate:!1,searchValue:"",cards:[],tabs:e.CONFIG.tabs,rest:!0,endOfList:!1,schemes:e.CONFIG.theme.schemes,activeScheme:e.CONFIG.theme.activeScheme,activeThemeKey:e.CONFIG.theme.activeThemeKey}}searchRequested;endOfList=!1;lastScroll=0;requestQueue=[];requestPage=0;cardList=[];sortConfig;gridUpdateTabs;gridUpdatePostsVisual;checkScroll;CONFIG;updateAppConfig;BLACKLIST;SNIPPETS;getInstalledTheme(){var e=localStorage.getItem(x.themeInstalled);return(e=e&&localStorage.getItem(e))?JSON.parse(e):null}newRequest(e){this.cardList=[];var t=[];this.requestQueue.unshift(t),this.loadAmount(t,e)}appendCard(e,t,r){r===this.props.CONFIG.activeTab&&(r=o.default.createElement(Um,{item:e,key:`${this.props.CONFIG.activeTab}:${e.user}:`+e.title,CONFIG:this.CONFIG,visual:this.props.CONFIG.visual,type:t,activeThemeKey:this.state.activeThemeKey,updateColourSchemes:this.updateColourSchemes.bind(this),updateActiveTheme:this.setActiveTheme.bind(this)}),this.cardList.push(r))}updateSort(e){e&&(this.sortConfig.by=e,localStorage.setItem(x.sort,e)),this.requestPage=0,this.cardList=[],this.setState({cards:[],rest:!1,endOfList:!1}),this.endOfList=!1,this.newRequest(Fr)}updateTabs(){this.setState({tabs:[...this.props.CONFIG.tabs]})}updatePostsVisual(){this.cardList=this.cardList.map((e,t)=>o.default.createElement(Um,{...e.props,key:t.toString(),CONFIG:this.CONFIG})),this.setState({cards:[...this.cardList]})}switchTo(e){this.CONFIG.activeTab=e.value,localStorage.setItem(x.activeTab,e.value),this.cardList=[],this.requestPage=0,this.setState({cards:[],rest:!1,endOfList:!1}),this.endOfList=!1,this.newRequest(Fr)}async loadPage(e){var t=this.CONFIG.activeTab;switch(t){case"Extensions":var r=await jo("spicetify-extensions",this.requestPage,this.BLACKLIST,this.CONFIG.visual.showArchived),a=[];for(const f of r.items){var n=await $o(f.contents_url,f.default_branch,f.stargazers_count,this.CONFIG.visual.hideInstalled);if(1<this.requestQueue.length&&e!==this.requestQueue[0])return-1;n?.length&&a.push(...n.map(e=>({...e,archived:f.archived,lastUpdated:f.pushed_at,created:f.created_at})))}Mo(a,localStorage.getItem("marketplace:sort")||"stars");for(const g of a)this.appendCard(g,"extension",t);this.setState({cards:this.cardList});var o=-1<this.requestPage&&this.requestPage?this.requestPage:1,i=Fr*(o-1)+r.page_count,s=r.total_count-i;if(console.debug(`Parsed ${i}/${r.total_count} extensions`),0<s)return o+1;console.debug("No more extension results");break;case"Installed":var l={theme:N(x.installedThemes,[]),extension:N(x.installedExtensions,[]),snippet:N(x.installedSnippets,[])};for(const b in l)if(l[b].length){var c=[];for(const v of l[b]){var d=N(v);if(1<this.requestQueue.length&&e!==this.requestQueue[0])return-1;c.push(d)}Mo(c,localStorage.getItem("marketplace:sort")||"stars");for(const y of c)this.appendCard(y,b,t)}this.setState({cards:this.cardList});break;case"Themes":var i=await jo("spicetify-themes",this.requestPage,this.BLACKLIST,this.CONFIG.visual.showArchived),u=[];for(const w of i.items){var p=await zo(w.contents_url,w.default_branch,w.stargazers_count);if(1<this.requestQueue.length&&e!==this.requestQueue[0])return-1;p?.length&&u.push(...p.map(e=>({...e,archived:w.archived,lastUpdated:w.pushed_at,created:w.created_at})))}this.setState({cards:this.cardList}),Mo(u,localStorage.getItem("marketplace:sort")||"stars");for(const S of u)this.appendCard(S,"theme",t);r=-1<this.requestPage&&this.requestPage?this.requestPage:1,s=Fr*(r-1)+i.page_count,o=i.total_count-s;if(console.debug(`Parsed ${s}/${i.total_count} themes`),0<o)return r+1;console.debug("No more theme results");break;case"Apps":var s=await jo("spicetify-apps",this.requestPage,this.BLACKLIST,this.CONFIG.visual.showArchived),m=[];for(const E of s.items){var h=await Fo(E.contents_url,E.default_branch,E.stargazers_count);if(1<this.requestQueue.length&&e!==this.requestQueue[0])return-1;h?.length&&m.push(...h.map(e=>({...e,archived:E.archived,lastUpdated:E.pushed_at,created:E.created_at})))}this.setState({cards:this.cardList}),Mo(m,localStorage.getItem("marketplace:sort")||"stars");for(const k of m)this.appendCard(k,"app",t);i=-1<this.requestPage&&this.requestPage?this.requestPage:1,o=Fr*(i-1)+s.page_count,r=s.total_count-o;if(console.debug(`Parsed ${o}/${s.total_count} apps`),0<r)return i+1;console.debug("No more app results");break;case"Snippets":o=this.SNIPPETS;if(1<this.requestQueue.length&&e!==this.requestQueue[0])return-1;if(o?.length){Mo(o,localStorage.getItem("marketplace:sort")||"stars");for(const C of o)this.appendCard(C,"snippet",t);this.setState({cards:this.cardList})}}return this.setState({rest:!0,endOfList:!0}),this.endOfList=!0,0}async loadAmount(t,e=Fr){this.setState({rest:!1});var r=this.cardList.length+e;for(this.requestPage=await this.loadPage(t);this.requestPage&&-1!==this.requestPage&&this.cardList.length<r&&!this.state.endOfList;)this.requestPage=await this.loadPage(t);-1===this.requestPage?this.requestQueue=this.requestQueue.filter(e=>e!==t):(this.requestQueue.shift(),this.setState({rest:!0}))}loadMore(){this.state.rest&&!this.endOfList&&this.loadAmount(this.requestQueue[0],Fr)}updateColourSchemes(e,t){console.debug("updateColourSchemes",e,t),this.CONFIG.theme.schemes=e,(this.CONFIG.theme.activeScheme=t)&&(rh.Config.color_scheme=t),e&&t&&e[t]?xo(this.CONFIG.theme.schemes[t]):xo(null);var r=N(x.themeInstalled),a=N(r);a?(a.activeScheme=t,console.debug(a),localStorage.setItem(r,JSON.stringify(a))):console.debug("No installed theme data"),this.setState({schemes:e,activeScheme:t})}async componentDidMount(){fetch(Vr).then(e=>e.json()).then(e=>{if(e.message)throw e;this.setState({version:e.name});try{this.setState({newUpdate:Br.default.gt(e.name,$r)})}catch(e){console.error(e)}},e=>{console.error("Failed to check for updates",e)}),this.gridUpdateTabs=this.updateTabs.bind(this),this.gridUpdatePostsVisual=this.updatePostsVisual.bind(this);var e=document.querySelector(".os-viewport")??document.querySelector("#main .main-view-container__scroll-node");this.checkScroll=this.isScrolledBottom.bind(this),e&&(e.addEventListener("scroll",this.checkScroll),this.cardList.length)?0<this.lastScroll&&e.scrollTo(0,this.lastScroll):(this.BLACKLIST=await Uo(),this.SNIPPETS=await Go(this.CONFIG.visual.hideInstalled),this.newRequest(Fr))}componentWillUnmount(){this.gridUpdateTabs=this.gridUpdatePostsVisual=null;var e=document.querySelector(".os-viewport")??document.querySelector("#main .main-view-container__scroll-node");e&&(this.lastScroll=e.scrollTop,e.removeEventListener("scroll",this.checkScroll))}isScrolledBottom(e){e=e.target;e.scrollTop+e.clientHeight>=e.scrollHeight&&this.loadMore()}setActiveTheme(e){this.CONFIG.theme.activeThemeKey=e,this.setState({activeThemeKey:e})}getActiveScheme(){return this.state.activeScheme}render(){const r=this.props["t"];return o.default.createElement("section",{className:"contentSpacing"},o.default.createElement("div",{className:"marketplace-header"},o.default.createElement("div",{className:"marketplace-header__left"},this.state.newUpdate?o.default.createElement("button",{type:"button",title:r("grid.newUpdate"),className:"marketplace-header-icon-button",id:"marketplace-update",onClick:()=>Tm("UPDATE")},o.default.createElement(Pm,null)," ",this.state.version):null,o.default.createElement("h2",{className:"marketplace-header__label"},r("grid.sort.label")),o.default.createElement(ii,{onChange:e=>this.updateSort(e),sortBoxOptions:ko(r),sortBySelectedFn:e=>e.key===this.CONFIG.sort})),o.default.createElement("div",{className:"marketplace-header__right"},this.CONFIG.visual.themeDevTools?o.default.createElement(fi,{label:r("devTools.title"),renderInline:!0,placement:"bottom"},o.default.createElement("button",{type:"button","aria-label":r("devTools.title"),className:"marketplace-header-icon-button",onClick:()=>Tm("THEME_DEV_TOOLS")},o.default.createElement(Ym,null))):null,this.state.activeScheme?o.default.createElement(ii,{onChange:e=>this.updateColourSchemes(this.state.schemes,e),sortBoxOptions:Eo(this.state.schemes),sortBySelectedFn:e=>e.key===this.getActiveScheme()}):null,o.default.createElement("div",{className:"searchbar--bar__wrapper"},o.default.createElement("input",{className:"searchbar-bar",type:"text",placeholder:`${r("grid.search")} ${r("tabs."+this.CONFIG.activeTab)}...`,value:this.state.searchValue,onChange:e=>{this.setState({searchValue:e.target.value})}})),o.default.createElement(fi,{label:r("settings.title"),renderInline:!0,placement:"bottom"},o.default.createElement("button",{type:"button","aria-label":r("settings.title"),className:"marketplace-header-icon-button",id:"marketplace-settings-button",onClick:()=>Tm("SETTINGS",this.CONFIG,this.updateAppConfig)},o.default.createElement(Km,null))))),[{handle:"extension",name:"Extensions"},{handle:"theme",name:"Themes"},{handle:"snippet",name:"Snippets"},{handle:"app",name:"Apps"}].map(t=>{var e=this.cardList.filter(e=>e.props.type===t.handle).filter(e=>{const t=this.state.searchValue.trim().toLowerCase();var{title:e,user:r,authors:a,tags:n}=e.props.item;return!t||e.toLowerCase().includes(t)||r?.toLowerCase().includes(t)||a?.some(e=>e.name.toLowerCase().includes(t))||[...n??[]].some(e=>e.toLowerCase().includes(t))}).map(e=>o.default.cloneElement(e,{activeThemeKey:this.state.activeThemeKey,key:e.key})).filter((t,e,r)=>r.findIndex(e=>e.key===t.key)===e);return e.length?o.default.createElement("div",{className:"marketplace-content"},o.default.createElement("h2",{className:"marketplace-card-type-heading"},r("tabs."+t.name)),o.default.createElement("div",{className:"marketplace-grid main-gridContainer-gridContainer main-gridContainer-fixedWidth","data-tab":this.CONFIG.activeTab,"data-card-type":r("tabs."+t.name)},e)):null}),"Snippets"===this.CONFIG.activeTab?o.default.createElement(Zo,{classes:["marketplace-add-snippet-btn"],onClick:()=>Tm("ADD_SNIPPET")},"+ ",r("grid.addCSS")):null,o.default.createElement("footer",{className:"marketplace-footer"},this.state.endOfList?o.default.createElement("div",{style:{height:"64px"}}):this.state.rest&&0<this.state.cards.length?o.default.createElement(Vm,{onClick:this.loadMore.bind(this)}):o.default.createElement(qm,null)),o.default.createElement(eh,{switchCallback:this.switchTo.bind(this),links:this.CONFIG.tabs,activeLink:this.CONFIG.activeTab}))}}),nh=t(r()),oh=_r()(class extends nh.default.Component{state={isInstalled:this.props.data.isInstalled(),html:`<p>${this.props.t("readmePage.loading")}</p>`};getReadmeHTML=async()=>fetch(this.props.data.readmeURL).then(e=>{if(e.ok)return e.text();throw Spicetify.showNotification(`${this.props.t("readmePage.errorLoading")} (HTTP ${e.status})`,!0)}).then(e=>To(e,this.props.data.user,this.props.data.repo)).then(e=>(e||Spicetify.Platform.History.goBack(),e)).catch(e=>(console.error(e),Spicetify.Platform.History.goBack(),null));componentDidMount(){this.getReadmeHTML().then(e=>{null!=e&&this.setState({html:e})})}componentDidUpdate(){const e=document.querySelector("#marketplace-readme")?.closest("main");if(e){const t=setInterval(()=>{document.querySelector("#marketplace-readme")?(e.style.overflowY="visible",e.style.overflowY="auto"):(clearInterval(t),e.style.removeProperty("overflow-y"))},1e3)}document.querySelectorAll("#marketplace-readme img").forEach(e=>{e.addEventListener("error",e=>{var e=e.target,t=e.getAttribute("src"),t="/"===t?.charAt(0)?`https://raw.githubusercontent.com/${this.props.data.user}/${this.props.data.repo}/${this.props.data.branch}/`+t?.slice(1):this.props.data.readmeURL.substring(0,this.props.data.readmeURL.lastIndexOf("/"))+"/"+t;e.setAttribute("src",t)},{once:!0})})}buttonContent(){return"app"===this.props.data.type?{icon:nh.default.createElement(Dm,null),text:this.props.t("github")}:this.state.isInstalled?{icon:nh.default.createElement(Mm,null),text:this.props.t("remove")}:{icon:nh.default.createElement(Pm,null),text:this.props.t("install")}}render(){var e="control"!==JSON.parse(localStorage.getItem("spicetify-exp-features")||"{}").enableGlobalNavBar?.value&&!0;return nh.default.createElement("section",{className:"contentSpacing"},nh.default.createElement("div",{className:"marketplace-header",style:{marginTop:e?"60px":"0px"}},nh.default.createElement("div",{className:"marketplace-header__left"},nh.default.createElement("h1",null,this.props.title)),nh.default.createElement("div",{className:"marketplace-header__right"},nh.default.createElement(Zo,{classes:["marketplace-header__button"],onClick:e=>{e.preventDefault(),this.props.data.install(),this.setState({isInstalled:!this.state.isInstalled})},label:this.buttonContent().text},this.buttonContent().icon," ",this.buttonContent().text))),"<p>Loading...</p>"===this.state.html?nh.default.createElement("footer",{className:"marketplace-footer"},nh.default.createElement(qm,null)):nh.default.createElement("div",{id:"marketplace-readme",className:"marketplace-readme__container",dangerouslySetInnerHTML:{__html:this.state.html}}))}}),Mn={ar:{translation:{settings:{title:"إعدادات المتجر",optionsHeading:"خيارات",starCountLabel:"عدد النجوم",tagsLabel:"العلامات",showArchived:"إظهار المستودعات المؤرشفة",devToolsLabel:"أدوات مطوري السمات",hideInstalledLabel:"إخفاء المثبتة عند التصفح",colourShiftLabel:"تغيير الألوان كل دقيقة",albumArtBasedColors:"تغيير الألوان بناءً على غلاف الألبوم",albumArtBasedColorsMode:"وضع نظام الألوان (واجهة برمجة التطبيقات الملونة (API))",albumArtBasedColorsVibrancy:"اللون المأخوذ من غلاف الألبوم",albumArtBasedColorsVibrancyToolTip:"مشبع قليلًا: اللون الأكثر بروزًا ولكن مع سطوع أقل بكثير \n ساطع خفيف: اللون الأكثر حيوية ولكن مع زيادة السطوع قليلًا \n بارز: اللون الأكثر بروزًا في غلاف الألبوم \n حيوي: اللون الأكثر حيوية في غلاف الألبوم",albumArtColorsModeToolTip:"أحادي اللون الداكن: نظام ألوان يعتمد بشكل مباشر على اللون الرئيسي المحدد، باستخدام ظلال مختلفة من اللون الرئيسي ودمج درجات الرمادي لإنشاء نظام ألوان، وهذا هو عكس نظام أحادي اللون الفاتح. \n أحادي اللون الفاتح: نظام ألوان يعتمد بشكل مباشر على اللون الرئيسي المحدد، باستخدام ظلال مختلفة من اللون الرئيسي ودمج درجات الرمادي لإنشاء نظام ألوان. ستكون خلفية أحادي اللون الفاتح هي لون المقدمة أو النص في نظام أحادي اللون الداكن والعكس صحيح. \n تناظري: نظام ألوان يعتمد على اللون الرئيسي المحدد، باستخدام الألوان المجاورة للون الرئيسي على عجلة الألوان. \n تناظري تكميلي: نظام ألوان يعتمد على اللون الرئيسي المحدد، باستخدام الألوان المجاورة للون الرئيسي على عجلة الألوان واللون التكميلي. \n ثلاثي: نظام ألوان يعتمد على اللون الرئيسي المحدد، باستخدام الألوان الموجودة على عجلة الألوان والتي تكون متساوية البعد عن اللون الرئيسي. \n رباعي: نظام ألوان يعتمد على اللون الرئيسي المحدد، باستخدام الألوان الموجودة على عجلة الألوان والتي تبعد 90 درجة عن اللون الرئيسي.",tabsHeading:"علامات التبويب",tabsDescription:"اسحب وأفلت لإعادة الترتيب، انقر للتمكين/التعطيل",resetHeading:"إعادة الضبط",resetBtn:"$t(settings.resetHeading)",resetDescription:"إلغاء تثبيت جميع الإضافات والسمات، وإعادة ضبط التفضيلات",backupHeading:"نسخ احتياطي/استعادة",backupLabel:"نسخ احتياطي أو استعادة جميع بيانات المتجر. لا يتضمن ذلك إعدادات أي شيء تم تثبيته عبر المتجر.",backupBtn:"فتح",versionHeading:"الإصدار",versionBtn:"نسخ",versionCopied:"تم النسخ"},tabs:{Extensions:"الإضافات",Themes:"السمات",Snippets:"المقتطفات",Apps:"التطبيقات",Installed:"المثبتة"},snippets:{addTitle:"إضافة مقتطف",duplicateName:"هذا الاسم مستخدم بالفعل!",editTitle:"تعديل مقتطف",viewTitle:"عرض مقتطف",customCSS:"CSS مخصص",customCSSPlaceholder:"أدخل CSS المخصص الخاص بك هنا! يمكنك العثور عليها في علامة التبويب المثبتة للإدارة.",snippetName:"اسم المقتطف",snippetNamePlaceholder:"أدخل اسمًا للمقتطف المخصص الخاص بك",snippetDesc:"وصف المقتطف",snippetDescPlaceholder:"أدخل وصفًا للمقتطف المخصص الخاص بك",snippetPreview:"معاينة المقتطف",optional:"اختياري",addImage:"إضافة صورة",changeImage:"تغيير الصورة",saveCSS:"حفظ CSS"},reloadModal:{title:"إعادة التحميل",description:"يجب إعادة تحميل الصفحة لإكمال هذه العملية.",reloadNow:"إعادة التحميل الآن",reloadLater:"إعادة التحميل لاحقًا"},backupModal:{title:"$t(settings.backupHeading)",settingsCopied:"تم نسخ الإعدادات إلى الحافظة",noDataPasted:"لم يتم لصق أي بيانات",invalidJSON:"JSON غير صالح",inputLabel:"إعدادات المتجر",inputPlaceholder:"انسخ/ألصق إعداداتك هنا",exportBtn:"تصدير",importBtn:"استيراد",fileImportBtn:"استيراد من ملف"},devTools:{title:"أدوات تطوير السمات",noThemeInstalled:"خطأ: لم يتم تثبيت أي سمة من المتجر",noThemeManifest:"خطأ: لم يتم العثور على بيان السمة",colorIniEditor:"محرر Color.ini",colorIniEditorPlaceholder:"[اسم-نظام-الألوان-الخاص-بك]",invalidCSS:"CSS غير صالح"},updateModal:{title:"تحديث المتجر",description:"قم بتحديث متجر سبياسيتيفي لتلقي ميزات جديدة وإصلاح الأخطاء.",currentVersion:"الإصدار الحالي: {{إصدار}}",latestVersion:"أحدث إصدار: {{إصدار}}",whatsChanged:"ما الذي تغير",seeChangelog:"عرض سجل التغييرات",howToUpgrade:"كيفية الترقية",viewGuide:"عرض الدليل"},grid:{spicetifyMarketplace:"متجر سبياسيتيفي",newUpdate:"تحديث جديد",addCSS:"إضافة CSS",search:"بحث",installed:"مثبتة",lastUpdated:"آخر تحديث {{val, datetime}}",externalJS:"JS خارجي",archived:"مؤرشفة",dark:"داكن",light:"فاتح",sort:{label:"فرز حسب:",stars:"النجوم",newest:"الأحدث",oldest:"الأقدم",lastUpdated:"آخر تحديث",mostStale:"الأكثر تقادمًا",aToZ:"أ-ي",zToA:"ي-أ"}},readmePage:{title:"$t(grid.spicetifyMarketplace) - اقرأني",loading:"جارٍ التحميل...",errorLoading:"README خطأ في تحميل ملف اقرأني"},github:"غيثب",install:"تثبيت",remove:"إزالة",save:"حفظ",colour_one:"لون",colour_other:"ألوان",favourite:"مفضل",notifications:{wrongLocalTheme:"يُرجى ضبط السمة الحالية في config-xpui.ini على ” المتجر“ لتثبيت السمات باستخدام المتجر",tooManyRequests:"عدد كبير جدًا من الطلبات، يرجى الانتظار",noCdnConnection:"المتجر غير قادر على الاتصال بشبكة CDN. يرجى التحقق من إعدادات الإنترنت الخاصة بك",markdownParsingError:"خطأ في تحليل Markdown (HTTP {{status}})",noReadmeFile:"لم يتم العثور على ملف اقرأني README",themeInstallationError:"حدث خطأ أثناء تثبيت السمة",extensionInstallationError:"حدث خطأ أثناء تثبيت الإضافة"}}},ca:{translation:{settings:{title:"Configuració",optionsHeading:"Opcions",starCountLabel:"Número d'estrelles",tagsLabel:"Etiquetes",devToolsLabel:"Eines per a desenvolupadors de temes",hideInstalledLabel:"Amagar instal·lats",colourShiftLabel:"Canviar colors cada minut",albumArtBasedColors:"Canviar colors a partir de la portada de l'àlbum",albumArtBasedColorsMode:"Mode esquema de colors (ColorApi)",albumArtBasedColorsVibrancy:"Colors agafats de la portada de l'àlbum",albumArtBasedColorsVibrancyToolTip:"Desaturat:El color més destacat però amb molta menys bror \n Vibrant Clar: El color més villantibrant amb la brillantor augmentada una mica \n Prominent: El color més destacat a la portada de l'Àlbum \n Vibrant: El color més vibrant a la portada de l'Àlbum",albumArtColorsModeToolTip:"Monochrome Dark: Un esquema de colors basat en el color principal seleccionat, emprant diferentes tonalitats i barrejant tons grisos per crear l'esquema de colors, aquest és l'invers de Monochrome Light. \n Monochrome Light: Un esquema de colors basat en el color principal seleccionat, emprant diferentes tonalitats i barrejant tons grisos per crear l'esquema de colors. El colors del fins de Monochrome light seria el color de primer pla en Monochrome Dark i viceversa. \n Analògic: Un esquema de colors basat en el color principal seleccionat, emprant els colors adjacents en la roda de colors. \n Analògic Complementari: Un esquema de colors basat en el color principal seleccionat, emprant els colors adjacents en la roda de colors i el color complementari. \n Tríada: Un esquema de colors basat en el color principal seleccionat, emprant els colors de la roda de colors que estan separats de manera equidistant del color principal. \n Quad: Un esquema de colors basat en el color principal seleccionat, emprant els colors que es troben separats 90 graus entre si en la roda de colors.",tabsHeading:"Pestanyes",tabsDescription:"Arrossegueu i deixeu anar per canviar l'ordre, feu clic per activar/desactivar",resetHeading:"Restablir",resetBtn:"$t(settings.resetHeading)",resetDescription:"Borrar totes les extensions, temes i preferències",backupHeading:"Fer una còpia/Reestablir des d'una còpia",backupLabel:"Fer una còpia o restablir totes les dades de Marketplace des d'una còpia. Això no inclou la configuració per els elements instal·lats amb Marketplace.",backupBtn:"Obrir",versionHeading:"Versió",versionBtn:"Copiar",versionCopied:"Copiat"},tabs:{Extensions:"Extensions",Themes:"Temes",Snippets:"Fragments",Apps:"Aplicacions",Installed:"Instal·lats"},snippets:{addTitle:"Afegir fragment",editTitle:"Editar fragment",viewTitle:"Veure fragment",customCSS:"CSS personalitzat",customCSSPlaceholder:"Crea el teu propi CSS aqui! Pots trobar-los a la pestanya d'instal·lats per administrar-los.",snippetName:"Nom del fragment de codi",snippetNamePlaceholder:"Afegeix un nom al teu codi personalitzat",snippetDesc:"Descripció del codi",snippetDescPlaceholder:"Crea una descripció per al teu codi personalitzat",snippetPreview:"Vista prèvia del fragment",optional:"Opcional",addImage:"Afegir imatge",changeImage:"Canviar imatge",saveCSS:"Guardar CSS"},reloadModal:{title:"Recarregar",description:"És necessari recarregar la finestra per completar aquesta operació.",reloadNow:"Fes-ho ara",reloadLater:"Després"},backupModal:{title:"$t(settings.backupHeading)",settingsCopied:"Configuració copiada al portapapers",noDataPasted:"No s'han enganxat dades",invalidJSON:"JSON invàlid",inputLabel:"Configuració de Marketplace",inputPlaceholder:"Còpia/enganxa la teva configuració aquí",exportBtn:"Exportar",importBtn:"Importar",fileImportBtn:"Importar des d'un arxiu"},devTools:{title:"Eines de desenvolupador de temes",noThemeInstalled:"Error: No hi ha cap tema de Marketplace instal·lat",noThemeManifest:"Error: No s'ha trobat el manifest",colorIniEditor:"Editor de Color.ini",colorIniEditorPlaceholder:"[nom-de-esquema-de-color]",invalidCSS:"CSS invàlid"},grid:{spicetifyMarketplace:"Marketplace de Spicetify",newUpdate:"Nova Actualització",addCSS:"Afegir CSS",search:"Buscar",installed:"Instal·lat",lastUpdated:"Última actualizació {{val, datetime}}",externalJS:"JS extern",dark:"fosc",light:"clar"},readmePage:{title:"$t(grid.spicetifyMarketplace) - Readme",loading:"Carregant...",errorLoading:"Error carregant el README"},github:"GitHub",install:"Instal·lar",remove:"Borrar",save:"Guardar",colour_one:"color",colour_other:"colors",favourite:"preferit"}},"de-DE":{translation:{settings:{title:"Marketplace Einstellungen",optionsHeading:"Optionen",starCountLabel:"Sterne Anzahl",tagsLabel:"Schlagwörter",showArchived:"Archivierte repos anzeigen",devToolsLabel:"Design Entwicklerwerkzeuge",hideInstalledLabel:"Versteckt installierte beim durchsuchen",colourShiftLabel:"Jede Minute Farbe wechseln",albumArtBasedColors:"Farbe basierend auf Album Cover wechseln",albumArtBasedColorsMode:"Farbschema (ColorApi) Modus",albumArtBasedColorsVibrancy:"Farbe vom Album Cover übernommen",albumArtBasedColorsVibrancyToolTip:"Entsättigt: Die am stärksten ausgeprägte Farbe, aber mit viel weniger Helligkeit \n Leicht lebhaft: Die am stärksten leuchtende Farbe, aber mit etwas mehr Helligkeit \n Prominent: Die Farbe, die im Albumcover am stärksten hervortritt \n Lebendig: Die lebendigste Farbe im Albumcover",albumArtColorsModeToolTip:"Monochrom Dunkel: Ein Farbschema, das direkt auf der ausgewählten Hauptfarbe basiert, wobei verschiedene Schattierungen der Hauptfarbe verwendet und Grautöne beigemischt werden, um ein Farbschema zu erstellen; dies ist das Gegenteil von Monochrom Hell. \n Monochrom Hell: Ein Farbschema, das direkt auf der ausgewählten Hauptfarbe basiert, wobei verschiedene Schattierungen der Hauptfarbe verwendet und Grautöne beigemischt werden, um ein Farbschema zu erstellen. Der Hintergrund von Monochrom hell wäre die Vordergrund- oder Textfarbe bei Monochrom dunkel und umgekehrt. \n Analogisch: Ein Farbschema, das auf der ausgewählten Hauptfarbe basiert und die Farben verwendet, die auf dem Farbkreis neben der Hauptfarbe liegen. \n Analogisch Komplementär: Ein Farbschema, das auf der ausgewählten Hauptfarbe basiert, wobei die Farben neben der Hauptfarbe auf dem Farbkreis und die Komplementärfarbe verwendet werden. \n Dreiklang: Ein Farbschema auf der Grundlage der ausgewählten Hauptfarbe, bei dem die Farben auf dem Farbkreis verwendet werden, die gleich weit von der Hauptfarbe entfernt sind. \n Vierer: Ein Farbschema auf der Grundlage der ausgewählten Hauptfarbe, bei dem die Farben auf dem Farbkreis verwendet werden, die 90 Grad von der Hauptfarbe entfernt sind.",tabsHeading:"Register",tabsDescription:"Ziehen und Ablegen zum Ändern der Reihenfolge, Klicken zum Aktivieren/Deaktivieren",resetHeading:"Zurücksetzen",resetBtn:"$t(settings.resetHeading)",resetDescription:"Deinstalliere alle Erweiterungen und Themes und setze Präferenzen zurück",backupHeading:"Sicherung erstellen / Wiederherstellen",backupLabel:"Sichern Sie alle Marketplace-Daten oder stellen Sie sie wieder her. Dies umfasst nicht die Einstellungen für alles, was über den Marketplace installiert wurde.",backupBtn:"Öffnen",versionHeading:"Version",versionBtn:"Kopieren",versionCopied:"Kopiert"},tabs:{Extensions:"Erweiterungen",Themes:"Designs",Snippets:"Snippets",Apps:"Apps",Installed:"Installiert"},snippets:{addTitle:"Snippet hinzufügen",duplicateName:"Dieser Name ist bereits vergeben!",editTitle:"Snippet bearbeiten",viewTitle:"Snippet ansehen",customCSS:"Benutzerdefiniertes CSS",customCSSPlaceholder:"Geben Sie hier Ihre eigenen benutzerdefinierten CSS ein! Sie finden sie auf der Registerkarte „Installiert“ für die Verwaltung.",snippetName:"Snippet Name",snippetNamePlaceholder:"Geben Sie einen Namen für ihr benutzerdefiniertes Snippet ein",snippetDesc:"Snippet Beschreibung",snippetDescPlaceholder:"Geben sie eine Beschreibung für ihr benutzerdefiniertes Snippet ein",snippetPreview:"Snippet Vorschau",optional:"Optional",addImage:"Foto hinzufügen",changeImage:"Foto ändern",saveCSS:"CSS speichern"},reloadModal:{title:"Neu laden",description:"Um diesen Vorgang abzuschließen, muss die Seite neu geladen werden.",reloadNow:"Jetzt neu laden",reloadLater:"Später neu laden"},backupModal:{title:"$t(settings.backupHeading)",settingsCopied:"Einstellungen in die Zwischenablage kopiert",noDataPasted:"Keine Daten eingefügt",invalidJSON:"Ungültiges JSON",inputLabel:"Marketplace Einstellungen",inputPlaceholder:"Kopieren Sie Ihre Einstellungen und fügen Sie sie hier ein",exportBtn:"Export",importBtn:"Import",fileImportBtn:"Von Datei importieren"},devTools:{title:"Design Entwicklerwerkzeug",noThemeInstalled:"Fehler: Kein Marketplace Design installed",noThemeManifest:"Fehler: Kein Design-Manifest gefunden",colorIniEditor:"Color.ini Bearbeitung",colorIniEditorPlaceholder:"[your-colour-scheme-name]",invalidCSS:"Ungültiges CSS"},updateModal:{title:"Aktualisieren Sie den Marketplace",description:"Aktualisieren Sie den Spicetify Marketplace um neue Funktionen und Fehlerbehebungen zu erhalten.",currentVersion:"Derzeitige Version: {{version}}",latestVersion:"Neueste Version: {{version}}",whatsChanged:"Was hat sich geändert?",seeChangelog:"Änderungsverlauf ansehen",howToUpgrade:"Wie man aktualisiert",viewGuide:"Anleitung ansehen"},grid:{spicetifyMarketplace:"Spicetify Marketplace",newUpdate:"Neues Update",addCSS:"CSS hinzufügen",search:"Suchen",installed:"Installiert",lastUpdated:"Zuletzt aktualisiert {{val, datetime}}",externalJS:"Externes JS",archived:"archiviert",dark:"dunkel",light:"hell",sort:{label:"Sortieren nach:",stars:"Sterne",newest:"Neueste",oldest:"Älteste",lastUpdated:"Zuletzt aktualisiert",mostStale:"Am abgestandensten",aToZ:"A-Z",zToA:"Z-A"}},readmePage:{title:"$t(grid.spicetifyMarketplace) - Les mich",loading:"Laden...",errorLoading:"Fehler beim laden von README"},github:"GitHub",install:"Installieren",remove:"Entfernen",save:"Speichern",colour_one:"Farbe",colour_other:"Farben",favourite:"Favorit",notifications:{wrongLocalTheme:"Bitte setzen Sie current_theme in config-xpui.ini auf 'marketplace', um Themen über den Marketplace zu installieren",tooManyRequests:"Zu viele Anfragen, beruhigen Sie sich",noCdnConnection:"Marketplace kann sich nicht mit dem CDN verbinden. Bitte überprüfen Sie Ihre Internetkonfiguration",markdownParsingError:"Fehler beim Parsen von Markdown (HTTP {{status}})",noReadmeFile:"README wurde nicht gefunden",themeInstallationError:"Beim Installieren von einem Design ist ein Fehler aufgetreten",extensionInstallationError:"Beim Installieren einer Erweiterung ist ein Fehler aufgetreten"}}},"en-US":{translation:{settings:{colourShiftLabel:"Shift colors every minute",albumArtBasedColors:"Change colors based on album art",albumArtBasedColorsMode:"Color scheme (ColorApi) mode",albumArtBasedColorsVibrancy:"Color grabbed from album art",albumArtBasedColorsVibrancyToolTip:"Desaturated: The color that is the most prominent but with much less brightness \n Light Vibrant: The most Vibrant color but with the brightness amped up a tad \n Prominent: The color that pops the most in the album art \n Vibrant: The most vibrant color in the album art",albumArtColorsModeToolTip:"Monochrome Dark: A color scheme based directly on the main color selected, using different shades of the main color and mixing in greys to create a color scheme, this is the inverse of Monochrome Light. \n Monochrome Light: A color scheme based directly on the main color selected, using different shades of the main color and mixing in greys to create a color scheme. The background of monochrome light would be the foreground or text color on Monochrome Dark and vice versa. \n Analogic: A color scheme based on the main color selected, using the colors adjacent to the main color on the color wheel. \n Analogic Complementary: A color scheme based on the main color selected, using the colors adjacent to the main color on the color wheel and the complementary color. \n Triad: A color scheme based on the main color selected, using the colors on the color wheel that are equidistant from the main color. \n Quad: A color scheme based on the main color selected, using the colors on the color wheel that are 90 degrees from the main color."},devTools:{colorIniEditorPlaceholder:"[your-color-scheme-name]"},colour_one:"color",colour_other:"colors",favourite:"favorite"}},en:{translation:{settings:{title:"Marketplace Settings",optionsHeading:"Options",starCountLabel:"Stars count",tagsLabel:"Tags",showArchived:"Show archived repos",devToolsLabel:"Theme developer tools",hideInstalledLabel:"Hide installed when browsing",colourShiftLabel:"Shift colours every minute",albumArtBasedColors:"Change colours based on album art",albumArtBasedColorsMode:"Colour scheme (ColorApi) mode",albumArtBasedColorsVibrancy:"Colour grabbed from album art",albumArtBasedColorsVibrancyToolTip:"Desaturated: The colour that is the most prominent but with much less brightness \n Light Vibrant: The most Vibrant colour but with the brightness amped up a tad \n Prominent: The colour that pops the most in the album art \n Vibrant: The most vibrant colour in the album art",albumArtColorsModeToolTip:"Monochrome Dark: A colour scheme based directly on the main colour selected, using different shades of the main colour and mixing in greys to create a colour scheme, this is the inverse of Monochrome Light. \n Monochrome Light: A colour scheme based directly on the main colour selected, using different shades of the main colour and mixing in greys to create a colour scheme. The background of monochrome light would be the foreground or text colour on Monochrome Dark and vice versa. \n Analogic: A colour scheme based on the main colour selected, using the colours adjacent to the main colour on the colour wheel. \n Analogic Complementary: A colour scheme based on the main colour selected, using the colours adjacent to the main colour on the colour wheel and the complementary colour. \n Triad: A colour scheme based on the main colour selected, using the colours on the colour wheel that are equidistant from the main colour. \n Quad: A colour scheme based on the main colour selected, using the colours on the colour wheel that are 90 degrees from the main colour.",tabsHeading:"Tabs",tabsDescription:"Drag and drop to reorder, click to enable/disable",resetHeading:"Reset",resetBtn:"$t(settings.resetHeading)",resetDescription:"Uninstall all extensions and themes, and reset preferences",backupHeading:"Back up/Restore",backupLabel:"Back up or restore all Marketplace data. This does not include settings for anything installed via Marketplace.",backupBtn:"Open",versionHeading:"Version",versionBtn:"Copy",versionCopied:"Copied"},tabs:{Extensions:"Extensions",Themes:"Themes",Snippets:"Snippets",Apps:"Apps",Installed:"Installed"},snippets:{addTitle:"Add Snippet",duplicateName:"That name is already taken!",editTitle:"Edit Snippet",viewTitle:"View Snippet",customCSS:"Custom CSS",customCSSPlaceholder:"Input your own custom CSS here! You can find them in the installed tab for management.",snippetName:"Snippet Name",snippetNamePlaceholder:"Enter a name for your custom snippet",snippetDesc:"Snippet Description",snippetDescPlaceholder:"Enter a description for your custom snippet",snippetPreview:"Snippet Preview",optional:"Optional",addImage:"Add image",changeImage:"Change image",saveCSS:"Save CSS"},reloadModal:{title:"Reload",description:"A page reload is required to complete this operation.",reloadNow:"Reload now",reloadLater:"Reload later"},backupModal:{title:"$t(settings.backupHeading)",settingsCopied:"Settings copied to clipboard",noDataPasted:"No data pasted",invalidJSON:"Invalid JSON",inputLabel:"Marketplace Settings",inputPlaceholder:"Copy/paste your settings here",exportBtn:"Export",importBtn:"Import",fileImportBtn:"Import from file"},devTools:{title:"Theme Dev Tools",noThemeInstalled:"Error: No marketplace theme installed",noThemeManifest:"Error: No theme manifest found",colorIniEditor:"Color.ini Editor",colorIniEditorPlaceholder:"[your-colour-scheme-name]",invalidCSS:"Invalid CSS"},updateModal:{title:"Update the Marketplace",description:"Update Spicetify Marketplace to receive new features and bug fixes.",currentVersion:"Current version: {{version}}",latestVersion:"Latest version: {{version}}",whatsChanged:"What's Changed",seeChangelog:"See changelog",howToUpgrade:"How to upgrade",viewGuide:"View guide"},grid:{spicetifyMarketplace:"Spicetify Marketplace",newUpdate:"New update",addCSS:"Add CSS",search:"Search",installed:"Installed",lastUpdated:"Last updated {{val, datetime}}",externalJS:"external JS",archived:"archived",dark:"dark",light:"light",sort:{label:"Sort by:",stars:"Stars",newest:"Newest",oldest:"Oldest",lastUpdated:"Last Updated",mostStale:"Most Stale",aToZ:"A-Z",zToA:"Z-A"}},readmePage:{title:"$t(grid.spicetifyMarketplace) - Readme",loading:"Loading...",errorLoading:"Error loading README"},github:"GitHub",install:"Install",remove:"Remove",save:"Save",colour_one:"colour",colour_other:"colours",favourite:"favourite",notifications:{wrongLocalTheme:"Please set current_theme in config-xpui.ini to 'marketplace' to install themes using the Marketplace",tooManyRequests:"Too many requests, cool down",noCdnConnection:"Marketplace is unable to connect to the CDN. Please check your Internet configuration",markdownParsingError:"Error parsing markdown (HTTP {{status}})",noReadmeFile:"No README was found",themeInstallationError:"There was an error installing theme",extensionInstallationError:"There was an error installing extension"}}},es:{translation:{settings:{title:"Ajustes de Marketplace",optionsHeading:"Opciones",starCountLabel:"Número de estrellas",tagsLabel:"Etiquetas",showArchived:"Mostrar repositorios archivados",devToolsLabel:"Herramientas para desarrolladores de temas",hideInstalledLabel:"Ocultar lo instalado al navegar",colourShiftLabel:"Cambiar colores cada minuto",albumArtBasedColors:"Cambiar los colores según la portada del álbum",albumArtBasedColorsMode:"Modo de esquema de colores (ColorApi)",albumArtBasedColorsVibrancy:"Color obtenido de la portada del álbum",albumArtBasedColorsVibrancyToolTip:"Desaturado: El color más prominente pero con mucha menos brillo \n Vibrante claro: El color más vibrante pero con el brillo aumentado un poco \n Prominente: El color que más destaca en la portada del álbum \n Vibrante: El color más vibrante en la portada del álbum",albumArtColorsModeToolTip:"Monocromo oscuro: Un esquema de color basado directamente en el color principal seleccionado, usando diferentes tonos del color principal y mezclando con grises para crear un esquema de color, esto es lo inverso de Monocromo claro. \n Monocromo claro: Un esquema de color basado directamente en el color principal seleccionado, usando diferentes tonos del color principal y mezclando con grises para crear un esquema de color. El fondo del monocromo claro sería el primer plano o color de texto en Monocromo oscuro y viceversa. \n Analógico: Un esquema de color basado en el color principal seleccionado, usando los colores adyacentes al color principal en la rueda de colores. \n Complementario analógico: Un esquema de color basado en el color principal seleccionado, usando los colores adyacentes al color principal en la rueda de colores y el color complementario. \n Tríada: Un esquema de color basado en el color principal seleccionado, usando los colores en la rueda de colores que están equidistantes del color principal. \n Cuádruple: Un esquema de color basado en el color principal seleccionado, usando los colores en la rueda de colores que están a 90 grados del color principal.",tabsHeading:"Pestañas",tabsDescription:"Arrastre y suelte para cambiar el orden, haga clic para activar/desactivar",resetHeading:"Restablecer",resetBtn:"$t(settings.resetHeading)",resetDescription:"Borrar todas extensiones y temas, y borrar preferencias",backupHeading:"Copia de seguridad/Restauración",backupLabel:"Haz una copia de seguridad o restaura todos los datos de Marketplace. Esto no incluye la configuración de nada instalado a través de Marketplace.",backupBtn:"Abrir",versionHeading:"Versión",versionBtn:"Copiar",versionCopied:"Copiado"},tabs:{Extensions:"Extensiones",Themes:"Temas",Snippets:"Códigos",Apps:"Aplicaciones",Installed:"Instalados"},snippets:{addTitle:"Añadir Código",duplicateName:"¡Ese nombre ya está en uso!",editTitle:"Editar Código",viewTitle:"Ver Código",customCSS:"Custom CSS",customCSSPlaceholder:"¡Crea tu propio CSS aquí! Puedes encontrarlos en la pestaña de instalados para administrarlos.",snippetName:"Nombre del código",snippetNamePlaceholder:"Asígnale un nombre para tu código personalizado",snippetDesc:"Descripción del código",snippetDescPlaceholder:"Crea una descripción para tu código personalizado",snippetPreview:"Código",optional:"Opcional",addImage:"Añadir imagen",changeImage:"Cambiar imagen",saveCSS:"Guardar CSS"},reloadModal:{title:"Recargar",description:"Es necesario recargar la página para completar esta operación.",reloadNow:"Recargar ahora",reloadLater:"Recargar después"},backupModal:{title:"$t(settings.backupHeading)",settingsCopied:"Ajustes copiados al portapapeles",noDataPasted:"No hay datos pegados",invalidJSON:"JSON inválido",inputLabel:"Ajustes de Marketplace",inputPlaceholder:"Copia/pega tus ajustes aquí",exportBtn:"Exportar",importBtn:"Importar",fileImportBtn:"Importar desde un archivo"},devTools:{title:"Herramientas de desarrollador de temas",noThemeInstalled:"Error: No se ha instalado el tema del marketplace",noThemeManifest:"Error: No se ha encontrado el manifiesto del tema",colorIniEditor:"Editor de Color.ini",colorIniEditorPlaceholder:"[nombre-de-esquema-de-color]",invalidCSS:"CSS inválido"},updateModal:{title:"Actualizar el Marketplace",description:"Actualiza Spicetify Marketplace para recibir nuevas funciones y correcciones de errores.",currentVersion:"Versión actual: {{version}}",latestVersion:"Última versión: {{version}}",whatsChanged:"Qué ha cambiado",seeChangelog:"Ver registro de cambios",howToUpgrade:"Cómo actualizar",viewGuide:"Ver guía"},grid:{spicetifyMarketplace:"Marketplace de Spicetify",newUpdate:"Nueva actualización",addCSS:"Añadir CSS",search:"Buscar",installed:"Instalado",lastUpdated:"Última actualización {{val, datetime}}",externalJS:"JS externo",archived:"archivado",dark:"oscuro",light:"claro",sort:{label:"Ordenar por:",stars:"Estrellas",newest:"Más nuevo",oldest:"Más antiguo",lastUpdated:"Última actualización",mostStale:"Más desactualizado",aToZ:"A-Z",zToA:"Z-A"}},readmePage:{title:"$t(grid.spicetifyMarketplace) - Readme",loading:"Cargando...",errorLoading:"Error al cargar el README"},github:"GitHub",install:"Instalar",remove:"Borrar",save:"Guardar",colour_one:"color",colour_other:"colores",favourite:"favorito",notifications:{wrongLocalTheme:"Por favor, establece current_theme en config-xpui.ini como 'marketplace' para instalar temas usando el Marketplace",tooManyRequests:"Demasiadas solicitudes, espera un momento",noCdnConnection:"Marketplace no puede conectarse al CDN. Por favor, revisa tu configuración de Internet",markdownParsingError:"Error al analizar markdown (HTTP {{status}})",noReadmeFile:"No se encontró el archivo README",themeInstallationError:"Hubo un error al instalar el tema",extensionInstallationError:"Hubo un error al instalar la extensión"}}},et:{translation:{settings:{title:"Turu seaded",optionsHeading:"Seaded",starCountLabel:"Tähtede arv",tagsLabel:"Sildid",devToolsLabel:"Teema arendaja tööriistad",hideInstalledLabel:"Peida sirvimisel paigaldatud",colourShiftLabel:"Muutke värve iga minut",albumArtBasedColors:"Muutke värve albumipildi põhjal",albumArtBasedColorsMode:"Värviskeemi (ColorApi) režiim",albumArtBasedColorsVibrancy:"Albumipildilt haaratud värv",albumArtBasedColorsVibrancyToolTip:"Desaturated: Värv, mis on kõige silmatorkavam, kuid palju väiksema heledusega \n Light vibrant: Kõige erksam värv, kuid veidi suurendatud heledusega \n Prominent: Värv, mis ilmub albumi kujunduses kõige rohkem \n Vibrant: Albumipildi kõige elavam värv",albumArtColorsModeToolTip:"Monochrome dark: Värvilahendus, mis põhineb otse valitud põhivärvil, kasutades põhivärvi erinevaid toone ja segades värviskeemi loomiseks halle, see on ühevärvlise heleda pöördväärtus. \n Monochrome light: Värvilahendus, mis põhineb otse valitud põhivärvil, kasutades põhivärvi erinevaid toone ja segades värviskeemi loomiseks halle. Ühevärvilise valguse taust oleks ühevärvilise tumeda esiplaani või teksti värv ja vastupidi. \n Analogic: Valitud põhivärvil põhinev värviskeem, kasutades värviratta põhivärviga külgnevaid värve. \n Analogic complement: Valitud põhivärvil põhinev värviskeem, kasutades värviratta põhivärviga külgnevaid värve ja lisavärvi. \n Triad: Valitud põhivärvil põhinev värviskeem, kasutades põhivärvist võrdsel kaugusel asuvaid värviratta värve. \n Quad: Valitud põhivärvil põhinev värviskeem, kasutades värvirattal olevaid värve, mis on põhivärvist 90 kraadi.",tabsHeading:"Vahekaardid",tabsDescription:"Järjekorra muutmiseks lohista ja eemalda, lubamiseks/välja lülitamiseks klõpsa",resetHeading:"Reset",resetBtn:"$t(settings.resetHeading)",resetDescription:"Uninstall all extensions and themes, and reset preferences",backupHeading:"Varunda/Taasta",backupLabel:"Varunda või taasta kõik turu andmed. See ei hõlma turu kaudu paigaldatud elementide seadeid.",backupBtn:"Ava",versionHeading:"Versioon",versionBtn:"Kopeeri",versionCopied:"Kopeeritud"},tabs:{Extensions:"Lisad",Themes:"Teemad",Snippets:"Katked",Apps:"Rakendused",Installed:"Paigaldatud"},snippets:{addTitle:"Lisa katkend",editTitle:"Muuda katkendit",viewTitle:"Vaata katkendit",customCSS:"Kohandatud CSS",customCSSPlaceholder:"Paigalda Kohandatud CSS siia! Haldamiseks leiate need paigaldatud vahekaardilt.",snippetName:"Katkendi nimi",snippetNamePlaceholder:"Lisa kohandatud katkendi nimi",snippetDesc:"Katkendi kirjeldus",snippetDescPlaceholder:"Lisa kohandatud katkendi kirjeldus",snippetPreview:"Katkendi eelvaade",optional:"valikuline",addImage:"Lisa pilt",changeImage:"Muuda pilti",saveCSS:"Salvesta CSS"},reloadModal:{title:"Laadi uuesti",description:"Selle toimingu lõpuleviimiseks on vaja leht uuesti laadida.",reloadNow:"Laadige kohe uuesti",reloadLater:"Laadige hiljem uuesti"},backupModal:{title:"$t(settings.backupHeading)",settingsCopied:"Seaded kopeeriti lõikelauale",noDataPasted:"Andmeid pole kleebitud",invalidJSON:"Vale JSON",inputLabel:"Turu Seaded",inputPlaceholder:"Kopeeri/kleebi enda seaded siia",exportBtn:"Ekspordi",importBtn:"Impordi",fileImportBtn:"Impordi failist"},devTools:{title:"Teema arendustööriistad",noThemeInstalled:"Viga: Turu teemat pole installitud",noThemeManifest:"Viga: Teema manifesti ei leitud",colorIniEditor:"Color.ini redaktor",colorIniEditorPlaceholder:"[teie-värviskeemi-nimi]",invalidCSS:"Vigane CSS"},grid:{spicetifyMarketplace:"Spicetify Turg",newUpdate:"Uus värskendus",addCSS:"Lisa CSS",search:"Otsi",installed:"Paigaldatud",lastUpdated:"Viimati uuendatud {{val, datetime}}",externalJS:"väline JS",dark:"tume",light:"hele"},readmePage:{title:"$t(grid.spicetifyMarketplace) - Readme",loading:"Laadimine...",errorLoading:"Viga README laadimisel"},github:"GitHub",install:"Paigalda",remove:"Eemalda",save:"Salvesta",colour_one:"värv",colour_other:"värvid",favourite:"lemmik"}},fi:{translation:{settings:{title:"Marketplacen asetukset",optionsHeading:"Valinnat",starCountLabel:"Tähdet",tagsLabel:"Tagit",showArchived:"Näytä arkistoituja lähdekoodivarastoja",devToolsLabel:"Teeman kehittäjätyökalut",hideInstalledLabel:"Piilota asennetut",colourShiftLabel:"Väriä muutetaan joka minuutti",albumArtBasedColors:"Muuta väriä albumin perusteella",albumArtBasedColorsMode:"Värityyli (ColorApi) tila",albumArtBasedColorsVibrancy:"Väri otettu albumista",albumArtBasedColorsVibrancyToolTip:"Kyllästelemätön: Väri, joka on näkyvin, mutta jonka kirkkaus on paljon pienempi \n Valoisa eloisa: Eloisin väri, jonka kirkkautta on hieman lisätty \n Merkittävä: Väri, joka erottuu eniten albumin kuvituksessa \n Eloisa: Albumin kuvituksen eloisin väri",albumArtColorsModeToolTip:"Tumma yksiväri: Suoraan valittuun pääväriin perustuva värimaailma, jossa käytetään päävärin eri sävyjä ja harmaita sekoittamalla värimaailman luomiseksi. Tämä on vaalean yksivärisen käänteinen värimaailma. \n Vaalea yksiväri: Suoraan valittuun pääväriin perustuva värimaailma, jossa käytetään päävärin eri sävyjä ja harmaita sekoittamalla värimaailman luomiseksi. Vaalean yksivärisen värimaailman tausta olisi tumman yksivärisen värimaailman etualan tai tekstin väri ja päinvastoin. \n Analoginen: Valittuun pääväriin perustuva värimaailma, jossa käytetään väriympyrässä päävärin viereisiä värejä. \n Analoginen komplementtiväri: Valittuun pääväriin perustuva värimaailma, jossa käytetään väriympyrässä päävärin viereisiä värejä ja komplementtiväriä. \n Kolmio: Valittuun pääväriin perustuva värimaailma, jossa käytetään väriympyrän värejä, jotka ovat yhtä kaukana pääväristä. \n Neliväri: Valittuun pääväriin perustuva värimaailma, jossa käytetään väriympyrän värejä, jotka ovat 90 asteen etäisyydellä pääväristä.",tabsHeading:"Sivut",tabsDescription:"Vedä ja pudota uudelleen järjestykseen, napsauta käyttöön/pois käytöstä",resetHeading:"Palauta",resetBtn:"$t(settings.resetHeading)",resetDescription:"Poista kaikki laajennukset ja teemat ja palauta asetukset",backupHeading:"Varmuuskopioi/Palauta",backupLabel:"Varmuuskopioi tai palauta kaikki Marketplacen data. Tämä ei sisällä asetuksia mitään, joka on asennettu Marketplacein kautta.",backupBtn:"Avaa",versionHeading:"Versio",versionBtn:"Kopioi",versionCopied:"Kopioitu"},tabs:{Extensions:"Laajennukset",Themes:"Teemat",Snippets:"Koodin pätkät",Apps:"Sovellukset",Installed:"Asennetut"},snippets:{addTitle:"Lisää koodin pätkä",duplicateName:"Nimi on jo käytössä!",editTitle:"Muokkaa koodin pätkää",viewTitle:"Näytä koodin pätkä",customCSS:"Oma CSS",customCSSPlaceholder:"Syötä oma mukautettu CSS-koodisi tähän! Löydät ne hallintaa varten asennetut-välilehdeltä.",snippetName:"Koodin pätkän nimi",snippetNamePlaceholder:"Anna nimi koodin pätkälle",snippetDesc:"Koodin pätkän kuvaus",snippetDescPlaceholder:"Anna kuvaus koodin pätkälle",snippetPreview:"Koodin pätkän esikatselu",optional:"Valinnainen",addImage:"Lisää kuva",changeImage:"Vaihda kuva",saveCSS:"Tallenna CSS"},reloadModal:{title:"Lataa uudelleen",description:"Sivun lataaminen uudelleen on vaadittava tämän toiminnon suorittamiseksi.",reloadNow:"Lataa uudelleen nyt",reloadLater:"Lataa uudelleen myöhemmin"},backupModal:{title:"$t(settings.backupHeading)",settingsCopied:"Asetukset kopioitu leikepöydälle",noDataPasted:"Ei dataa liitetty",invalidJSON:"Virheellinen JSON",inputLabel:"Marketplacen asetukset",inputPlaceholder:"Kopioi/liitä asetuksesi tähän",exportBtn:"Vie",importBtn:"Tuo",fileImportBtn:"Tuo tiedostosta"},devTools:{title:"Teeman kehittäjätyökalut",noThemeInstalled:"Virhe: Ei Marketplace-teemaa asennettu",noThemeManifest:"Virhe: Ei teeman manifestia",colorIniEditor:"Color.ini-editori",colorIniEditorPlaceholder:"[sinun-värityylisi-nimi]",invalidCSS:"Virheellinen CSS"},updateModal:{title:"Päivitä Marketplace",description:"Päivitä Spicetify Marketplace saadaksesi uusia ominaisuuksia ja bugikorjauksia.",currentVersion:"Nykyinen versio: {{version}}",latestVersion:"Uusin versio: {{version}}",whatsChanged:"Mitä on muuttunut",seeChangelog:"Katso muutosloki",howToUpgrade:"Kuinka päivittää",viewGuide:"Näytä opas"},grid:{spicetifyMarketplace:"Spicetify Marketplace",newUpdate:"Uusi päivitys",addCSS:"Lisää CSS",search:"Haku",installed:"Asennettu",lastUpdated:"Viimeisin päivitys {{val, datetime}}",externalJS:"ulkoiset JS-skriptit",archived:"arkistoitu",dark:"tumma",light:"vaalea",sort:{label:"Järjestä:",stars:"Tähdet",newest:"Uusin",oldest:"Vanhin",lastUpdated:"Viimeisin päivitys",mostStale:"Eniten vanhentunut",aToZ:"A-Z",zToA:"Z-A"}},readmePage:{title:"$t(grid.spicetifyMarketplace) - Readme",loading:"Ladataan...",errorLoading:"Virhe ladattaessa README"},github:"GitHub",install:"Asenna",remove:"Poista",save:"Tallenna",colour_one:"väri",colour_other:"värit",favourite:"suosikki",notifications:{wrongLocalTheme:"Aseta current_theme config-xpui.ini-tiedostossa 'marketplace'-arvoon käyttääksesi Marketplace-teemoja",tooManyRequests:"Liian monta pyyntöä, odota hetki",noCdnConnection:"Marketplace ei pysty yhdistämään CDN:ään. Tarkista internetyhteys",markdownParsingError:"Virhe jäsentämässä markdownia (HTTP {{status}})",noReadmeFile:"Ei README-tiedostoa löytynyt",themeInstallationError:"Teeman asentamisessa tapahtui virhe",extensionInstallationError:"Laajennuksen asentamisessa tapahtui virhe"}}},fr:{translation:{settings:{title:"Réglages Marché Spicetify",optionsHeading:"Options",starCountLabel:"Nombres d’étoiles",tagsLabel:"Tags",devToolsLabel:"Outils pour les développeurs de thèmes",hideInstalledLabel:"Masquer ceux étant installés lors de la navigation",colourShiftLabel:"Changer de couleur chaque minutes",albumArtBasedColors:"Changement des couleurs basé sur les pochettes d'albums",albumArtBasedColorsMode:"Mode de schéma de couleur (ColorApi)",albumArtBasedColorsVibrancy:"Couleur saisie depuis les pochettes d'albums",albumArtBasedColorsVibrancyToolTip:"Désaturé: La couleur qui est la plus proéminente mais avec beaucoup moins de luminosité\nVibrations Claires: La couleur la plus vibrante, mais avec une luminosité un peu plus forte\nPrometteur: La couleur qui ressort le plus dans la pochette de l'album\nVibrations: La couleur la plus vibrante dans la pochette de l'album",albumArtColorsModeToolTip:"Monochrome foncé: une palette de couleurs basée directement sur la couleur principale sélectionnée, en utilisant différentes nuances de la couleur principale et en mélangeant des gris pour créer une palette de couleurs, c'est l'inverse du monochrome clair.\nMonochrome clair: Une palette de couleurs basée directement sur la couleur principale sélectionnée, en utilisant différentes nuances de la couleur principale et en mélangeant les gris pour créer une palette de couleurs. L'arrière-plan d'un monochrome clair sera le premier plan ou la couleur du texte d'un monochrome foncé et vice versa.\nAnalogique: Schéma de couleurs basé sur la couleur principale sélectionnée, utilisant les couleurs adjacentes à la couleur principale sur le cercle chromatique.\nAnalogique complémentaire: Un schéma de couleurs basé sur la couleur principale sélectionnée, utilisant les couleurs adjacentes à la couleur principale sur le cercle chromatique et la couleur complémentaire.\nTriade: Un schéma de couleurs basé sur la couleur principale sélectionnée, utilisant les couleurs équidistantes de la couleur principale sur le cercle chromatique.\nQuad: Un schéma de couleurs basé sur la couleur principale sélectionnée, utilisant les couleurs du cercle chromatique qui sont à 90 degrés de la couleur principale.",tabsHeading:"Onglets",tabsDescription:"Glisser-déposer pour modifier l'ordre, cliquer pour activer/désactiver",resetHeading:"Réinitialiser",resetBtn:"$t(settings.resetHeading)",resetDescription:"Désinstaller toutes les extensions et tous les thèmes, ainsi que l’ensemble des réglages",backupHeading:"Sauvegarde/Restauration",backupLabel:"Sauvegarder ou restaurer toutes les données du Marché. Celà n'inclue pas les réglages pour quoi que ce soit installé depuis le Marché.",backupBtn:"Ouvrir",versionHeading:"Version",versionBtn:"Copier",versionCopied:"Copié"},tabs:{Extensions:"Extensions",Themes:"Thèmes",Snippets:"Bribes",Apps:"Applications",Installed:"Installé(s)"},snippets:{addTitle:"Ajouter Bribe",editTitle:"Éditer Bribe",viewTitle:"Voir Bribe",customCSS:"CSS personnalisé",customCSSPlaceholder:"Insérez votre propre CSS personnalisé ici! Vous pouvez les retrouver dans l’onglet Installé pour les gérer.",snippetName:"Nom de la bribe",snippetNamePlaceholder:"Entrer un nom pour votre bribe personnalisée",snippetDesc:"Description de la bribe",snippetDescPlaceholder:"Entrez une description pour votre bribe personnalisée",snippetPreview:"Prévisualiser la bribe",optional:"Optionnel",addImage:"Ajouter une image",changeImage:"Changer l’image",saveCSS:"Enregistrer le CSS"},reloadModal:{title:"Recharger",description:"Un rechargement de la page est requis pour finaliser cette opération.",reloadNow:"Recharger maintenant",reloadLater:"Recharger plus tard"},backupModal:{title:"Sauvegarder/Restaurer",settingsCopied:"Réglages copiés dans le presse-papier",noDataPasted:"Aucune donnée collée",invalidJSON:"JSON invalide",inputLabel:"Réglages du Marché",inputPlaceholder:"Copier/coller vos réglages ici",exportBtn:"Exporter",importBtn:"Importer",fileImportBtn:"Importer depuis un fichier"},devTools:{title:"Outils de développeurs de thèmes",noThemeInstalled:"Erreur: Aucun thème du marché n’est installé",noThemeManifest:"Erreur: Aucun manifeste de thème trouvé",colorIniEditor:"Éditeur Color.ini",colorIniEditorPlaceholder:"[nom-de-votre-schéma-de-couleur]",invalidCSS:"CSS invalide"},grid:{spicetifyMarketplace:"Marché Spicetify",newUpdate:"Nouvelle mise à jour",addCSS:"Ajouter CSS",search:"Rechercher",installed:"Installé",lastUpdated:"Dernière mise à jour {{val, datetime}}",externalJS:"JS externe",dark:"sombre",light:"clair",sort:{label:"Trier par:",stars:"Étoiles",newest:"Nouveauté",oldest:"Ancienneté",lastUpdated:"Dernière mise à jour",mostStale:"Le plus périmé",aToZ:"A-Z",zToA:"Z-A"}},readmePage:{title:"$t(grid.spicetifyMarketplace) - Readme",loading:"Chargement…",errorLoading:"Erreur lors du chargement du README"},github:"GitHub",install:"Installer",remove:"Supprimer",save:"Enregistrer",colour_one:"couleur",colour_other:"couleurs",favourite:"favoris"}},it:{translation:{settings:{title:"Impostazioni Marketplace",optionsHeading:"Opzioni",starCountLabel:"Contatore stelle",tagsLabel:"Tag",showArchived:"Mostra repository archiviati",devToolsLabel:"Strumenti di sviluppo del tema",hideInstalledLabel:"Nascondi i pacchetti già installati durante la navigazione",colourShiftLabel:"Cambia colori ogni minuto",albumArtBasedColors:"Cambia i colori in base alla copertina dell'album",albumArtBasedColorsMode:"Schema colori modalità (ColorApi)",albumArtBasedColorsVibrancy:"Colore preso dalla copertina dell'album",albumArtBasedColorsVibrancyToolTip:"Desaturato: Il colore predominante ma con molta meno luminosità \n Vibrante Chiaro: Il colore più intenso ma con la luminosità aumentata leggermente \n Predominante: Il colore che spicca di più nella copertina dell'album \n Vibrante: Il colore più intenso nella copertina dell'album",albumArtColorsModeToolTip:"Monocromo Scuro: Uno schema di colori basato direttamente sul colore principale selezionato, utilizzando diverse sfumature del colore principale e mescolando i grigi per creare uno schema di colori; questo è l'inverso di Monocromo Chiaro. \n Monocromo Chiaro: Uno schema di colori basato direttamente sul colore principale selezionato, utilizzando diverse sfumature del colore principale e mescolando i grigi per creare uno schema di colori. Lo sfondo di Monocromo Chiaro sarebbe il colore del testo o di quello in primo piano su Monocromo Scuro e viceversa. \n Armonico: Uno schema di colori basato sul colore principale selezionato, utilizzando i colori adiacenti al colore principale sulla ruota dei colori.\n Armonico Complementare: Uno schema di colori basato sul colore principale selezionato, utilizzando i colori adiacenti al colore principale sulla ruota dei colori e il colore complementare. \n Ternario: Uno schema di colori basato sul colore principale selezionato, utilizzando i colori sulla ruota dei colori che sono equidistanti dal colore principale. \n Quaternario: Uno schema di colori basato sul colore principale selezionato, utilizzando i colori sulla ruota dei colori che sono a 90 gradi dal colore principale.",tabsHeading:"Schede",tabsDescription:"Trascinare e rilasciare per cambiare l'ordine, cliccare per attivare/disattivare",resetHeading:"Reimposta",resetBtn:"$t(settings.resetHeading)",resetDescription:"Disinstalla tutte le estensioni e i temi, e ripristina le preferenze",backupHeading:"Backup/Ripristino",backupLabel:"Effettua il backup o ripristina tutti i dati del Marketplace. Questo non include le impostazioni per qualsiasi elemento installato tramite Marketplace.",backupBtn:"Apri",versionHeading:"Versione",versionBtn:"Copia",versionCopied:"Copiato"},tabs:{Extensions:"Estensioni",Themes:"Temi",Snippets:"Moduli",Apps:"Applicazioni",Installed:"Installato"},snippets:{addTitle:"Aggiungi Modulo",duplicateName:"Questo nome è già stato utilizzato!",editTitle:"Modifica Modulo",viewTitle:"Visualizza Modulo",customCSS:"CSS personalizzato",customCSSPlaceholder:"Scrivi qui il tuo CSS personalizzato! Puoi trovarli nella scheda degli installati per la gestione.",snippetName:"Nome Modulo",snippetNamePlaceholder:"Inserisci un nome per il tuo modulo personalizzato",snippetDesc:"Descrizione Modulo",snippetDescPlaceholder:"Inserisci una descrizione per il tuo modulo personalizzato",snippetPreview:"Anteprima Modulo",optional:"Opzionale",addImage:"Aggiungi immagine",changeImage:"Cambia immagine",saveCSS:"Salva CSS"},reloadModal:{title:"Ricarica",description:"È necessario ricaricare la pagina per completare questa operazione.",reloadNow:"Ricarica",reloadLater:"Più tardi"},backupModal:{title:"$t(settings.backupHeading)",settingsCopied:"Impostazioni copiate negli appunti",noDataPasted:"Nessun dato incollato",invalidJSON:"JSON non valido",inputLabel:"Impostazioni Marketplace",inputPlaceholder:"Copia/incolla qui le tue impostazioni",exportBtn:"Esporta",importBtn:"Importa",fileImportBtn:"Importa da file"},devTools:{title:"Strumenti di sviluppo del tema",noThemeInstalled:"Errore: Nessun tema del Marketplace installato",noThemeManifest:"Errore: Nessun manifest del tema trovato",colorIniEditor:"Editor Color.ini",colorIniEditorPlaceholder:"[nome-del-tuo-schema-colori]",invalidCSS:"Classi CSS non valide"},updateModal:{title:"Aggiorna il Marketplace",description:"Aggiorna Spicetify Marketplace per ricevere nuove funzionalità e correzioni dei bug.",currentVersion:"Versione attuale: {{version}}",latestVersion:"Ultima versione: {{version}}",whatsChanged:"Cos'è Cambiato",seeChangelog:"Guarda il changelog",howToUpgrade:"Come effettuare l'aggiornamento",viewGuide:"Visualizza guida"},grid:{spicetifyMarketplace:"Spicetify Marketplace",newUpdate:"Nuovo aggiornamento",addCSS:"Aggiungi CSS",search:"Cerca",installed:"Installato",lastUpdated:"Ultimo aggiornamento {{val, datetime}}",externalJS:"jS esterno",archived:"archiviato",dark:"scuro",light:"chiaro",sort:{label:"Ordina per:",stars:"Valutazione",newest:"Più recente",oldest:"Meno recenti",lastUpdated:"Ultimo aggiornamento",mostStale:"Meno Aggiornato",aToZ:"A-Z",zToA:"Z-A"}},readmePage:{title:"$t(grid.spicetifyMarketplace) - Readme",loading:"Caricamento...",errorLoading:"Errore nel caricamento del README"},github:"GitHub",install:"Installa",remove:"Rimuovi",save:"Salva",colour_one:"colore",colour_other:"colori",favourite:"preferito",notifications:{wrongLocalTheme:"Si prega d'impostare current_theme in config-xpui.ini su 'marketplace' per installare temi utilizzando il Marketplace",tooManyRequests:"Troppe richieste, attendi un momento",noCdnConnection:"Il Marketplace non riesce a connettersi al CDN. Si prega di controllare la configurazione Internet",markdownParsingError:"Errore durante l'analisi del Markdown (HTTP {{status}})",noReadmeFile:"Nessun README trovato",themeInstallationError:"Si è verificato un errore durante l'installazione del tema",extensionInstallationError:"Si è verificato un errore durante l'installazione dell'estensione"}}},ja:{translation:{settings:{title:"マーケットプレイスの設定",optionsHeading:"オプション",starCountLabel:"スターの数",tagsLabel:"タグ",showArchived:"アーカイブされたリポジトリを表示",devToolsLabel:"テーマ開発者ツール",hideInstalledLabel:"ブラウジング時にインストール済みを非表示にする",colourShiftLabel:"1分ごとに色を変更",albumArtBasedColors:"アルバムアートに基づいて色を変更",albumArtBasedColorsMode:"カラースキーム（ColorApi）モード",albumArtBasedColorsVibrancy:"アルバムアートから取得した色",albumArtBasedColorsVibrancyToolTip:"Desaturated: 最も目立つ色だが、明るさがはるかに抑えられています \n Light Vibrant: 最も活気ある色ですが、明るさが少し増しています \n Prominent: アルバムアートで最も目立つ色です \n Vibrant: アルバムアートで最も鮮やかな色です",albumArtColorsModeToolTip:"Monochrome Dark: 選択したメインカラーを基にした色の配色スキームで、メインカラーの異なる濃淡やグレーを混ぜて配色することで、これはMonochrome Lightの反対です。 \n Monochrome Light: 選択したメインカラーを直接基にした色の配色スキームで、メインカラーの異なる濃淡やグレーを混ぜて配色します。Monochrome Lightの背景色は、Monochrome Darkの前景色やテキスト色となり、その逆も同様です。 \n Analogic: 選択されたメインカラーを基に、カラーホイール上でメインカラーに隣接する色を使用した配色スキームです。 \n Analogic Complementary: 選択したメインカラーを基に、カラーホイール上でメインカラーに隣接する色と補色を使用した配色スキームです。 \n Triad: 選択したメインカラーを基に、カラーホイール上でメインカラーから等距離にある色を使用した配色スキームです。 \n Quad: 選択されたメインカラーを基に、カラーホイール上でメインカラーから90度離れた色を使用した配色スキームです。",tabsHeading:"タブ",tabsDescription:"ドラッグ＆ドロップで順序を変更し、クリックで有効／無効を切り替える",resetHeading:"リセット",resetBtn:"$t(settings.resetHeading)",resetDescription:"すべての拡張機能とテーマをアンインストールし、設定をリセットします",backupHeading:"バックアップ/リストア",backupLabel:"すべてのマーケットプレイスデータのバックアップまたはリストアを行います。これには、マーケットプレイスを介してインストールされた設定は含まれません。",backupBtn:"開く",versionHeading:"バージョン",versionBtn:"コピー",versionCopied:"コピーされました"},tabs:{Extensions:"拡張機能",Themes:"テーマ",Snippets:"スニペット",Apps:"アプリ",Installed:"インストール済み"},snippets:{addTitle:"スニペットを追加",duplicateName:"その名前は既に使われています!",editTitle:"スニペットを編集",viewTitle:"スニペットを表示",customCSS:"カスタムCSS",customCSSPlaceholder:"ここにカスタムCSSを入力してください! 管理用のインストール済みタブで見つけることができます。",snippetName:"スニペット名",snippetNamePlaceholder:"カスタムスニペットの名前を入力してください",snippetDesc:"スニペットの説明",snippetDescPlaceholder:"カスタムスニペットの説明を入力してください",snippetPreview:"スニペットプレビュー",optional:"オプション",addImage:"画像を追加",changeImage:"画像を変更",saveCSS:"CSSを保存"},reloadModal:{title:"リロード",description:"この操作を完了するにはページのリロードが必要です。",reloadNow:"今すぐ読み込む",reloadLater:"後で読み込む"},backupModal:{title:"$t(settings.backupHeading)",settingsCopied:"設定がクリップボードにコピーされました",noDataPasted:"データが貼り付けられていません",invalidJSON:"無効なJSON",inputLabel:"マーケットプレイスの設定",inputPlaceholder:"ここに設定をコピー＆ペーストしてください",exportBtn:"エクスポート",importBtn:"インポート",fileImportBtn:"ファイルからインポート"},devTools:{title:"テーマ開発ツール",noThemeInstalled:"エラー：マーケットプレイスのテーマがインストールされていません",noThemeManifest:"エラー：テーママニフェストが見つかりませんでした",colorIniEditor:"Color.ini Editor",colorIniEditorPlaceholder:"[your-colour-scheme-name]",invalidCSS:"無効なCSS"},updateModal:{title:"マーケットプレイスの更新",description:"新機能やバグ修正を受け取るために、Spicetify Marketplaceを更新してください。",currentVersion:"現在のバージョン: {{version}}",latestVersion:"最新のバージョン: {{version}}",whatsChanged:"変更内容",seeChangelog:"変更履歴を見る",howToUpgrade:"アップグレード方法",viewGuide:"ガイドを見る"},grid:{spicetifyMarketplace:"Spicetify Marketplace",newUpdate:"新しいアップデート",addCSS:"CSSを追加",search:"検索",installed:"インストール済み",lastUpdated:"{{val, datetime}}に最終更新",externalJS:"外部JS",archived:"アーカイブ済み",dark:"ダーク",light:"ライト",sort:{label:"並べ替え:",stars:"スター",newest:"最新",oldest:"最古",lastUpdated:"最終更新",mostStale:"最も古い",aToZ:"A-Z",zToA:"Z-A"}},readmePage:{title:"$t(grid.spicetifyMarketplace) - README",loading:"読み込み中...",errorLoading:"READMEの読み込み中にエラーが発生しました"},github:"GitHub",install:"インストール",remove:"削除",save:"保存",colour_one:"色",colour_other:"色",favourite:"お気に入り",notifications:{wrongLocalTheme:"config-xpui.iniのcurrent_themeを 'marketplace' に設定して、マーケットプレイスを使用してテーマをインストールしてください",tooManyRequests:"リクエストが多すぎます。時間をおいて再試行してください",noCdnConnection:"マーケットプレイスがCDNに接続できません。インターネットの設定を確認してください",markdownParsingError:"Markdownの解析エラー（HTTP {{status}}）",noReadmeFile:"READMEが見つかりませんでした",themeInstallationError:"テーマのインストール中にエラーが発生しました",extensionInstallationError:"拡張機能のインストール中にエラーが発生しました"}}},ko:{translation:{settings:{title:"마켓플레이스 설정",optionsHeading:"설정",starCountLabel:"별점 개수",tagsLabel:"태그",showArchived:"보관된 리포지스토리 보기",devToolsLabel:"테마 개발자 도구",hideInstalledLabel:"설치된 기능은 탐색 결과에서 숨기기",colourShiftLabel:"매 분마다 색상 변경",albumArtBasedColors:"앨범 표지에 따라 색상 변경",albumArtBasedColorsMode:"색상 구성표 (색상Api) 모드",albumArtBasedColorsVibrancy:"앨범 표지에서 색상 추출",albumArtBasedColorsVibrancyToolTip:"탁색 (Desaturated): 가장 두드러지지만 밝기가 낮은 색상입니다.\n쨍한 백색 (Light Vibrant): 가장 생생하게 느껴지면서도 밝기가 조금 더 높아진 색상입니다.\n강조색 (Prominent): 앨범 아트에서 가장 눈에 띄는 색상입니다, 앨범 아트에서 가장 돋보이는 색이라고 할 수 있죠.\n높은 채도색 (Vibrant): 앨범 아트에서 가장 생생한 색상입니다.",albumArtColorsModeToolTip:"모노크롬 다크 (Monochrome Dark): 선택한 메인 색상을 기준으로 다양한 명암들과 회색을 섞어 만든 색상입니다. 이는 Monochrome Light의 반대 개념이죠.\n모노크롬 라이트 (Monochrome Light): 선택한 메인 색상을 기준으로 다양한 명암과 회색을 섞어 만든 색상입니다. Monochrome Light의 배경색은 Monochrome Dark의 전경색(배경색) 또는 텍스트의 색상이 되며, 그 반대도 마찬가지입니다.\n유사색 (Analogic): 선택한 메인 색상을 기준으로 색 팔레트에서 메인 색상에 인접한 색상들을 이용하여 만든 색상입니다.\n유사 보색 (Analogic Complementary): 선택한 메인 색상을 기준으로 색 팔레트에서 메인 색상에 인접한 색상들과 보색들을 함께 사용하여 만든 색입니다.\n트라이드 (Triad): 선택한 메인 색상을 기준으로 색팔레트에서 메인 색상으로부터 동일한 간격으로 떨어져 있는 세 가지 색상을 사용하여 만든 색상 구성표입니다.\n쿼드 (Quad): 선택한 메인 색상을 기준으로 색팔레트에서 메인 색상으로부터 90도 간격으로 떨어져 있는 네 가지 색상을 사용하여 만든 색상 구성표입니다.",tabsHeading:"탭",tabsDescription:"끌어 놓아 순서를 변경하고, 클릭하여 활성화 및 비활성화 합니다.",resetHeading:"\n초기화",resetBtn:"$t(settings.resetHeading)",resetDescription:"모든 확장 프로그램들과 테마들을 제거하고, 기본 설정으로 되돌립니다.",backupHeading:"백업/복구",backupLabel:"모든 마켓플레이스 데이터를 백업하거나 복원합니다.여기에는 마켓플레이스를 통해 설치된 항목들의 설정은 포함되지 않습니다.",backupBtn:"열기",versionHeading:"버전",versionBtn:"복사",versionCopied:"복사됨"},tabs:{Extensions:"확장 프로그램",Themes:"테마 목록",Snippets:"추가 기능",Apps:"앱 목록",Installed:"설치 목록"},snippets:{addTitle:"추가 기능 등록",duplicateName:"이 이름은 이미 사용되고 있습니다!",editTitle:"추가 기능 수정",viewTitle:"추가 기능",customCSS:"Custom CSS",customCSSPlaceholder:"자신의 CSS 파일을 이곳에 첨부하세요! 설치 목록 탭에서 찾을 수 있습니다.",snippetName:"추가 기능 이름",snippetNamePlaceholder:"추가 기능 이름을 이곳에 입력하세요!",snippetDesc:"추가 기능 설명",snippetDescPlaceholder:"추가 기능 설명을 이곳에 입력하세요!",snippetPreview:"추가 기능 미리보기 이미지",optional:"선택사항",addImage:"이미지 추가",changeImage:"이미지 바꾸기",saveCSS:"CSS 저장"},reloadModal:{title:"번경 사항 적용",description:"이 작업을 완료하려면 페이지를 재시작해야 합니다.",reloadNow:"지금 재시작",reloadLater:"나중에 재시작"},backupModal:{title:"$t(settings.backupHeading)",settingsCopied:"설정이 클립보드에 저장되었습니다.",noDataPasted:"붙여넣어진 데이터가 없습니다",invalidJSON:"유효하지 않은 JSON",inputLabel:"마켓 플레이스 설정",inputPlaceholder:"이곳에 설정을 복사/붙여넣기 하세요",exportBtn:"내보내기",importBtn:"가져오기",fileImportBtn:"파일에서 가져오기"},devTools:{title:"테마 개발자 도구",noThemeInstalled:"오류: 어떤 마켓플레이스 테마도 설치되지 않았습니다",noThemeManifest:"오류: 테마 매니페스트 찾을 수 없음",colorIniEditor:"Colour.ini 편집기",colorIniEditorPlaceholder:"[당신의-색상-구성표-이름]",invalidCSS:"유효치 않은 CSS"},updateModal:{title:"마켓플레이스 업데이트",description:"spicetify 마켓 플레이스를 업데이트 하여 새로운 기능들과 버그 픽스들을 제공받습니다.",currentVersion:"현재 버전: {{version}}",latestVersion:"지난 버전: {{version}}",whatsChanged:"새로운 기능",seeChangelog:"패치노트 보기",howToUpgrade:"업데이트 방법",viewGuide:"가이드 보기"},grid:{spicetifyMarketplace:"Spicetify 마켓플레이스",newUpdate:"새로운 업데이트",addCSS:"CSS 등록",search:"검색",installed:"설치됨",lastUpdated:"마지막 업데이트일 {{val, datetime}}",externalJS:"external JS",archived:"만료됨",dark:"다크 모드",light:"라이트 모드",sort:{label:"나열 기준:",stars:"별점",newest:"신규",oldest:"오래된 순",lastUpdated:"가장 최신 순",mostStale:"가장 오래된 순",aToZ:"A-Z 까지",zToA:"Z-A 까지"}},readmePage:{title:"$t(grid.spicetifyMarketplace) - Readme",loading:"로딩중...",errorLoading:"README를 불러오는 중 오류 발생"},github:"GitHub",install:"설치",remove:"제거",save:"저장",colour_one:"색상",colour_other:"색상들",favourite:"즐겨찾기",notifications:{wrongLocalTheme:"마켓플레이스를 사용하여 테마를 설치하려면 config-xpui.ini에서 current_theme를 'marketplace'로 설정하세요.",tooManyRequests:"요청이 너무 빈번합니다, 잠시 뒤 시도하세요",noCdnConnection:"마켓플레이스가 CDN에 접속할 수 없습니다. 현재 인터넷 구성을 확인해주세요.",markdownParsingError:"Markdown 구문 분석 오류 (HTTP {{status}})",noReadmeFile:"README 파일이 존재하지 않습니다",themeInstallationError:"테마를 설치하는데 오류가 있었습니다",extensionInstallationError:"확장 프로그램을 설치하는 데 오류가 발생했습니다"}}},pl:{translation:{settings:{title:"Ustawienia Marketplace",optionsHeading:"Opcje",starCountLabel:"Ilość gwiazdek",tagsLabel:"Tagi",showArchived:"Pokaż archiwalne repozytoria",devToolsLabel:"Narzędzia do tworzenia motywów",hideInstalledLabel:"Ukryj zainstalowane podczas przeglądania",colourShiftLabel:"Zmieniaj kolory co minutę",albumArtBasedColors:"Zmień kolory bazując na okładce albumu",albumArtBasedColorsMode:"Tryb schematu kolorów (ColorApi)",albumArtBasedColorsVibrancy:"Kolor pobrany z okładki albumu",albumArtBasedColorsVibrancyToolTip:"Nasycony: Kolor, który jest najbardziej widoczny, ale o znacznie mniejszej jasności. \nJasny wibrujący: Najbardziej żywy kolor, ale z nieco zwiększoną jasnością. \nWyraźny: Kolor, który najbardziej rzuca się w oczy na okładce albumu. \nWibrujący: Najbardziej żywy kolor na okładce albumu",albumArtColorsModeToolTip:"Monochromatyczny ciemny: Schemat kolorów oparty bezpośrednio na wybranym głównym kolorze, wykorzystujący różne odcienie głównego koloru i mieszający szarości w celu stworzenia schematu kolorów, jest to odwrotność Monochromatycznego jasnego. \nMonochromatyczny jasny: Schemat kolorów oparty bezpośrednio na wybranym głównym kolorze, wykorzystujący różne odcienie głównego koloru i mieszanie szarości w celu utworzenia schematu kolorów. Tło monochromatycznego światła będzie na pierwszym planie lub kolorem tekstu w monochromatycznym ciemnym i odwrotnie. \nAnalogowy: Schemat kolorów oparty na wybranym głównym kolorze, wykorzystujący kolory sąsiadujące z głównym kolorem na kole kolorów. \nUzupełnienie analogowe: Schemat kolorów oparty na wybranym głównym kolorze, wykorzystujący kolory sąsiadujące z głównym kolorem na kole kolorów i kolorem uzupełniającym. \nTriada: Schemat kolorów oparty na wybranym głównym kolorze, wykorzystujący kolory na kole kolorów, które są w równej odległości od głównego koloru. \nQuad: Schemat kolorów oparty na wybranym głównym kolorze, wykorzystujący kolory na kole kolorów, które są oddalone o 90 stopni od głównego koloru.",tabsHeading:"Karty",tabsDescription:"Przeciągnij i upuść, aby zmienić kolejność, kliknij, aby włączyć/wyłączyć",resetHeading:"Reset",resetBtn:"Zresetuj",resetDescription:"Odinstaluj wszystkie rozszerzenia, motywy i zresetuj preferencje",backupHeading:"Kopia zapasowa/Przywracanie kopii",backupLabel:"Utwórz kopię zapasową lub przywróć wszystkie dane Marketplace. Kopia nie zawiera ustawień dla rzeczy zainstalowanych poprzez Marketplace.",backupBtn:"Otwórz",versionHeading:"Wersja",versionBtn:"Skopiuj",versionCopied:"Skopiowano"},tabs:{Extensions:"Rozszerzenia",Themes:"Motywy",Snippets:"Snippety",Apps:"Aplikacje",Installed:"Zainstalowane"},snippets:{addTitle:"Dodaj Snippet",duplicateName:"Ta nazwa jest już zajęta!",editTitle:"Edytuj Snippet",viewTitle:"Pokaż Snippet",customCSS:"Niestandardowy CSS",customCSSPlaceholder:"Wprowadź tutaj swój własny CSS! Możesz go znaleźć w zakładce 'Zainstalowane' aby nim zarządzać.",snippetName:"Nazwa snippetu",snippetNamePlaceholder:"Wprowadź nazwę swojego niestandardowego snippetu",snippetDesc:"Opis snippetu",snippetDescPlaceholder:"Wpisz opis swojego snippetu",snippetPreview:"Podgląd snippetu",optional:"Opcjonalne",addImage:"Dodaj obraz",changeImage:"Zmień obraz",saveCSS:"Zapisz CSS"},reloadModal:{title:"Przeładuj",description:"Do ukończenia tej operacji wymagane jest przeładowanie strony.",reloadNow:"Przeładuj teraz",reloadLater:"Przeładuj póżniej"},backupModal:{title:"$t(settings.backupHeading)",settingsCopied:"Skopiowano do schowka",noDataPasted:"Dane nie zostały wklejone",invalidJSON:"Nieprawidłowy JSON",inputLabel:"Ustawienia Marketplace",inputPlaceholder:"Skopiuj/wklej tu swoje ustawienia",exportBtn:"Eksportuj",importBtn:"Importuj",fileImportBtn:"Importuj z pliku"},devTools:{title:"Narzędzia developerskie do motywów",noThemeInstalled:"Błąd: Nie zainstalowano motywu",noThemeManifest:"Błąd: Nie znaleziono pliku manifestu motywu",colorIniEditor:"Edytor color.ini",colorIniEditorPlaceholder:"[nazwa-twojego-koloru]",invalidCSS:"Nieprawidłowy CSS"},updateModal:{title:"Zaktualizuj Marketplace",description:"Zaktualizuj Spicetify Marketplace, aby otrzymywać nowe funkcje i poprawki błędów.",currentVersion:"Obecna wersja: {{version}}",latestVersion:"Najnowsza wersja: {{version}}",whatsChanged:"Co się zmieniło",seeChangelog:"Zobacz zmiany",howToUpgrade:"Jak zaktualizować",viewGuide:"Zobacz przewodnik"},grid:{spicetifyMarketplace:"Spicetify Marketplace",newUpdate:"Nowa aktualizacja",addCSS:"Dodaj CSS",search:"Wyszukaj",installed:"Zainstalowane",lastUpdated:"Ostatnio zaktualizowane {{val, datetime}}",externalJS:"zewnętrzny JS",archived:"archiwalny",dark:"ciemny",light:"jasny",sort:{label:"Sortuj po:",stars:"Ilość gwiazdek",newest:"Najnowsze",oldest:"Najstarsze",lastUpdated:"Ostatnio zaktualizowane",mostStale:"Najrzadziej aktualizowane",aToZ:"A-Z",zToA:"Z-A"}},readmePage:{title:"$t(grid.spicetifyMarketplace) - Readme",loading:"Ładowanie...",errorLoading:"Błąd podczas ładowania README"},github:"GitHub",install:"Zainstaluj",remove:"Odinstaluj",save:"Zapisz",colour_one:"kolor",colour_other:"kolory",favourite:"ulubione",notifications:{wrongLocalTheme:'Ustaw current_theme w config-xpui.ini na "marketplace", aby instalować motywy za pomocą Marketplace.',tooManyRequests:"Za dużo żądań, spokojnie",noCdnConnection:"Marketplace nie może połączyć się z CDN. Sprawdź swoją konfigurację internetową",markdownParsingError:"Błąd podczas parsowania markdownu (HTTP {{status}})",noReadmeFile:"Nie znaleziono README",themeInstallationError:"Wystąpił błąd podczas instalacji motywu",extensionInstallationError:"Wystąpił błąd podczas instalacji rozszerzenia"}}},"pt-BR":{translation:{settings:{title:"Opções do Marketplace",optionsHeading:"Opções",starCountLabel:"Quantidade de Estrelas",tagsLabel:"Tags",showArchived:"Exibir repositórios arquivados",devToolsLabel:"Ferramentas para desenvolvedores de temas",hideInstalledLabel:"Ocultar a instalação durante a navegação",colourShiftLabel:"Mudança de cores a cada minuto",albumArtBasedColors:"Alterar as cores com base na arte do álbum",albumArtBasedColorsMode:"Modo de esquema de cores (ColorApi)",albumArtBasedColorsVibrancy:"Cor extraída da arte do álbum",albumArtBasedColorsVibrancyToolTip:"Desaturada: A cor que é a mais proeminente, mas com muito menos brilho \n Vibrante claro: A cor mais vibrante, mas com o brilho um pouco mais intenso \n Proeminente: A cor que mais se destaca na arte do álbum \n Vibrante: A cor mais vibrante na arte do álbum",albumArtColorsModeToolTip:"Monocromático Escuro: um esquema de cores baseado diretamente na cor principal selecionada, usando diferentes tons da cor principal e misturando cinzas para criar um esquema de cores; esse é o inverso do Monocromático claro. \n Monocromática Leve: Um esquema de cores baseado diretamente na cor principal selecionada, usando diferentes tons da cor principal e misturando cinzas para criar um esquema de cores. O plano de fundo do monocromático claro seria o primeiro plano ou a cor do texto no Monocromático Escuro e vice-versa. \n Analogic: A colour scheme based on the main colour selected, using the colours adjacent to the main colour on the colour wheel. \n Analogic Complementary: A colour scheme based on the main colour selected, using the colours adjacent to the main colour on the colour wheel and the complementary colour. \n Triad: A colour scheme based on the main colour selected, using the colours on the colour wheel that are equidistant from the main colour. \n Quad: A colour scheme based on the main colour selected, using the colours on the colour wheel that are 90 degrees from the main colour.",tabsHeading:"Tabs",tabsDescription:"Arraste e solte para reordenar, clique para ativar/desativar",resetHeading:"Resetar",resetBtn:"$t(settings.resetHeading)",resetDescription:"Desinstale todas as extensões e temas e redefina as preferências",backupHeading:"Back up/Restaurar",backupLabel:"Faça backup ou restaure todos os dados do Marketplace. Isto não inclui definições para qualquer coisa instalada através do Marketplace.",backupBtn:"Abrir",versionHeading:"Versão",versionBtn:"Copiar",versionCopied:"Copiado"},tabs:{Extensions:"Extensões",Themes:"Temas",Snippets:"Snippets",Apps:"Aplicativos",Installed:"Instalados"},snippets:{addTitle:"Adicionar Snippet",duplicateName:"Esse nome já está sendo usado!",editTitle:"Editar Snippet",viewTitle:"Ver Snippet",customCSS:"Personalizar CSS",customCSSPlaceholder:"Insira seu próprio CSS personalizado aqui! Você pode encontrá-los na guia instalada para gerenciamento.",snippetName:"Nome do Snippet",snippetNamePlaceholder:"Digite um nome para seu snippet personalizado",snippetDesc:"Descrição do Snippet",snippetDescPlaceholder:"Digite uma descrição para seu snippet personalizado",snippetPreview:"Visualizar Snippets",optional:"Opcional",addImage:"Adicionar Imagem",changeImage:"Mudar imagem",saveCSS:"Salvar o CSS"},reloadModal:{title:"Recarregar",description:"É necessário recarregar a página para concluir essa operação.",reloadNow:"Recarregar agora",reloadLater:"Recarregar mais tarde"},backupModal:{title:"$t(settings.backupHeading)",settingsCopied:"Configurações copiadas para a área de transferência",noDataPasted:"Nenhum dado foi colado",invalidJSON:"JSON inválido",inputLabel:"Configurações do Marketplace",inputPlaceholder:"Copie/cole suas configurações aqui",exportBtn:"Exportar",importBtn:"Importar",fileImportBtn:"Importar de um arquivo"},devTools:{title:"Ferramentas de desenvolvimento de temas",noThemeInstalled:"Erro: Nenhum tema do mercado instalado",noThemeManifest:"Erro: Nenhum manifesto de tema encontrado",colorIniEditor:"Color.ini Editor",colorIniEditorPlaceholder:"[your-colour-scheme-name]",invalidCSS:"CSS inválido"},updateModal:{title:"Atualizar o Marketplace",description:"Atualize o Spicetify Marketplace para receber novos recursos e correções de bugs.",currentVersion:"Versão atual: {{version}}",latestVersion:"Versão mais recente: {{version}}",whatsChanged:"O que mudou",seeChangelog:"Veja o registro de mudanças",howToUpgrade:"Como atualizar",viewGuide:"Ver o guia"},grid:{spicetifyMarketplace:"Spicetify Marketplace",newUpdate:"Nova atualização",addCSS:"Adicionar CSS.",search:"Pesquisar",installed:"Instalador",lastUpdated:"Última atualização {{val, datetime}}",externalJS:"external JS",archived:"arquivado",dark:"dark",light:"light",sort:{label:"Classificar por:",stars:"Estrelas",newest:"Mais recente",oldest:"Mais antigo",lastUpdated:"Última atualização",mostStale:"Mais obsoleto",aToZ:"A-Z",zToA:"Z-A"}},readmePage:{title:"$t(grid.spicetifyMarketplace) - Readme",loading:"Carregando...",errorLoading:"Error loading README"},github:"GitHub",install:"Instalar",remove:"Remove-r",save:"Salvar",colour_one:"cor",colour_other:"cores",favourite:"favoritos",notifications:{wrongLocalTheme:"Por favor, defina current_theme em config-xpui.ini para 'marketplace' para instalar temas usando o Marketplace",tooManyRequests:"Muitas solicitações, acalme-se",noCdnConnection:"Marketplace não consegue se conectar à CDN. Verifique sua configuração de Internet",markdownParsingError:"Error parsing markdown (HTTP {{status}})",noReadmeFile:"No README was found",themeInstallationError:"Ocorreu um erro ao instalar o tema",extensionInstallationError:"Ocorreu um erro ao instalar a extensão"}}},ru:{translation:{settings:{title:"Настройки",optionsHeading:"Основные",starCountLabel:"Отображать количество звезд",tagsLabel:"Отображать теги",showArchived:"Отображать архивные репозитории",devToolsLabel:"Включить инструменты разработчика тем",hideInstalledLabel:"Скрывать установленное в других вкладках",colourShiftLabel:"Менять цвета каждую минуту",albumArtBasedColors:"Использовать цвета на основе обложки альбома",albumArtBasedColorsMode:"Тип цвета",albumArtBasedColorsVibrancy:"Тип цветовой схемы на основе обложки альбома",albumArtBasedColorsVibrancyToolTip:"Desaturated: наиболее часто встречаемый в обложке цвет с малой яркостью \n Light Vibrant: самый насыщенный цвет с повышенной яркостью \n Prominent: наиболее часто встречаемый цвет в обложке альбома \n Vibrant: самый насыщенный цвет в обложке альбома",albumArtColorsModeToolTip:"Monochrome Dark, Monochrome Light: основаны иcключительно на выбранном цвете, дополнительные цвета создаются путем изменения яркости основого. Противоположны друг другу: цвет, являющийся фоновым в Monochrome Light, в Monochrome Dark будет цветом переднего плана и наоборот. \n Analogic: палитра определяется выбранным и цветами, смежными с ним на цветовом круге. \n Analogic Complementary: схожа c Analogic, но сожержит также дополнительный цвет. \n Triad: палитра определяется основным цветом и цветами, равноудаленными от него. \n Quad: палитра определяется выбранным цветом и цветами, расположенных под углом 90 градусов к нему.",tabsHeading:"Вкладки",tabsDescription:"Перетаскивание для изменения порядка, щелчок для включения/выключения",resetHeading:"Сброс",resetBtn:"Сбросить",resetDescription:"Удалить все и сбросить настройки",backupHeading:"Резервное копирование и восстановление",backupLabel:"Сохранить или восстановить все данные Маркетплейса, за исключением настроек установленных тем и расширений.",backupBtn:"Открыть",versionHeading:"Версия",versionBtn:"Копировать",versionCopied:"Скопировано"},tabs:{Extensions:"Расширения",Themes:"Темы",Snippets:"Сниппеты",Apps:"Приложения",Installed:"Установленное"},snippets:{addTitle:"Добавление сниппета",duplicateName:"Сниппет с таким названием уже существует",editTitle:"Редактирование сниппета",viewTitle:"Просмотр сниппета",customCSS:"CSS",customCSSPlaceholder:"Вставьте сюда CSS вашего сниппета",snippetName:"Название",snippetNamePlaceholder:"Введите название для вашего сниппета",snippetDesc:"Описание",snippetDescPlaceholder:"Введите описание для вашего сниппета",snippetPreview:"Превью",optional:"необязательно",addImage:"Добавить изображение",changeImage:"Изменить изображение",saveCSS:"Сохранить"},reloadModal:{title:"Перезагрузка",description:"Необходима перезагрузка страницы для применения изменений",reloadNow:"Перезагрузить сейчас",reloadLater:"Перезагрузить позже"},backupModal:{title:"$t(settings.backupHeading)",settingsCopied:"Настройки скопированы в буфер обмена",noDataPasted:"Ничего не вставлено",invalidJSON:"Неверный JSON",inputLabel:"Настройки Маркетплейса",inputPlaceholder:"Вставьте ваши настройки сюда",exportBtn:"Экспортировать",importBtn:"Импортировать",fileImportBtn:"Импортировать из файла"},devTools:{title:"Инструменты разработчика тем",noThemeInstalled:"Ошибка: Не установлена тема из Маркетплейса",noThemeManifest:"Ошибка: Не найден манифест темы",colorIniEditor:"Редактор color.ini",colorIniEditorPlaceholder:"[название-вашей-цветовой-схемы]",invalidCSS:"Неверный CSS"},updateModal:{title:"Обновление Маркетплейса",description:"Обновите Маркетплейс для получения новых функций и исправлений.",currentVersion:"Текущая версия: {{version}}",latestVersion:"Последняя версия: {{version}}",whatsChanged:"Что нового",seeChangelog:"Посмотреть изменения",howToUpgrade:"Инструкция по обновлению",viewGuide:"Посмотреть инструкцию"},grid:{spicetifyMarketplace:"Маркетплейс Spicetify",newUpdate:"Доступно обновление",addCSS:"Добавить CSS",search:"Искать",installed:"Установлено",lastUpdated:"Обновлено: {{val, datetime}}",externalJS:"содержит JS",archived:"архивировано",dark:"темный",light:"светлый",sort:{label:"Сортировать:",stars:"по количеству звезд",newest:"сначала новые",oldest:"сначала старые",lastUpdated:"сначала недавно обновленные",mostStale:"сначала давно не обновлявшиеся",aToZ:"по названию (A-Z)",zToA:"по названию (Z-A)"}},readmePage:{title:"$t(grid.spicetifyMarketplace) - Readme",loading:"Загрузка...",errorLoading:"Ошибка загрузки README"},github:"GitHub",install:"Установить",remove:"Удалить",save:"Сохранить",colour_one:"цвет",colour_other:"цвета",favourite:"избранное",notifications:{wrongLocalTheme:"Пожалуйста, измените значение current_theme в config-xpui.ini на 'marketplace', чтобы использовать темы из Маркетплейса",tooManyRequests:"Слишком много запросов. Пожалуйста, попробуйте позже",noCdnConnection:"Маркетплейс не может подключиться к CDN. Пожалуйста, попробуйте позже",markdownParsingError:"Ошибка при парсинге Markdown (HTTP {{status}})",noReadmeFile:"README не найден",themeInstallationError:"Ошибка при установке темы",extensionInstallationError:"Ошибка при установке расширения"}}},uk:{translation:{settings:{title:"Налаштування Маркетплейсу",optionsHeading:"Налаштування",starCountLabel:"Кількість зірок",tagsLabel:"Теги",showArchived:"Показати заархівовані репозиторії",devToolsLabel:"Інструменти розробника тем",hideInstalledLabel:"Сховати встановлені",colourShiftLabel:"Змінювати колір кожну хвилину",albumArtBasedColors:"Змінювати колір в залежності від обкладинки альбому",albumArtBasedColorsMode:"Кольорова схема (ColorApi)",albumArtBasedColorsVibrancy:"Колір взято з обкладинки альбому",albumArtBasedColorsVibrancyToolTip:"Насичений: Колір, який є найбільш помітним, але з набагато меншою яскравістю \n Light Vibrant (Яскравий): Найяскравіший колір, але з дещо підвищеною яскравістю \n Виразний: Колір, який найбільше виділяється на обкладинці альбому \n Яскравий: Найяскравіший колір на обкладинці альбому",albumArtColorsModeToolTip:"Монохромний темний: кольорова схема, що базується безпосередньо на вибраному основному кольорі, з використанням різних відтінків основного кольору та змішуванням сірих кольорів для створення кольорової схеми, це протилежність монохромного світлого. \n Монохромний світлий: Кольорова схема, що базується безпосередньо на вибраному основному кольорі, з використанням різних відтінків основного кольору та змішуванням сірих кольорів для створення кольорової схеми. Тло монохромного світлого буде переднім планом або кольором тексту на монохромному темному, і навпаки. \n Аналоговий: Кольорова схема, заснована на вибраному основному кольорі з використанням кольорів, суміжних з основним кольором на колірному колі. \n Аналогово-доповнювальна: Кольорова схема на основі вибраного основного кольору з використанням сусідніх з ним кольорів на колірному колі та додаткового кольору. \n Тріада: Кольорова схема на основі вибраного основного кольору з використанням кольорів на колі кольорів, рівновіддалених від основного кольору. \n Квадрат: Кольорова схема на основі вибраного основного кольору з використанням кольорів на колі кольорів, розташованих під кутом 90 градусів до основного кольору.",tabsHeading:"Вкладки",tabsDescription:"Перетягніть, щоб змінити порядок, натисніть, щоб увімкнути/вимкнути",resetHeading:"Скинути",resetBtn:"$t(settings.resetHeading)",resetDescription:"Видалити усі розширення і теми, та скинути налаштування",backupHeading:"Резервне копіювання/Відновлення",backupLabel:"Копіювати або відновити всі дані Маркетплейсу. Це не включає в себе налаштування всього, що встановлено через Маркетплейс",backupBtn:"Відкрити",versionHeading:"Версія",versionBtn:"Копіювати",versionCopied:"Скопійовано"},tabs:{Extensions:"Розширення",Themes:"Теми",Snippets:"Фрагменти",Apps:"Застосунки",Installed:"Встановлено"},snippets:{addTitle:"Додати фрагмент",duplicateName:"Ця назва вже зайнята!",editTitle:"Редагувати фрагмент",viewTitle:"Переглянути фрагмент",customCSS:"Користувацький CSS",customCSSPlaceholder:"Введіть свій власний CSS тут! Ви можете знайти їх у вкладці управління встановленими файлами.",snippetName:"Назва фрагменту",snippetNamePlaceholder:"Введіть ім'я для вашого користувацького фрагменту",snippetDesc:"Опис фрагменту",snippetDescPlaceholder:"Введіть опис для вашого користувацького фрагменту",snippetPreview:"Перегляд фрагменту",optional:"Необов'язковий",addImage:"Додати світлину",changeImage:"Змінити світлину",saveCSS:"Зберегти CSS"},reloadModal:{title:"Перезавантажити",description:"Для завершення цієї операції потрібно перезавантажити сторінку",reloadNow:"Перезавантажити зараз",reloadLater:"Перезавантажити пізніше"},backupModal:{title:"$t(settings.backupHeading)",settingsCopied:"Налаштування скопійовано у буфер обміну",noDataPasted:"Дані не вставлено",invalidJSON:"Недійсний JSON",inputLabel:"Налаштування Маркетплейсу",inputPlaceholder:"Скопіювати/вставити свої налаштування сюди",exportBtn:"Експорт",importBtn:"Імпорт",fileImportBtn:"Імпортувати з файлу"},devTools:{title:"Інструменти розробника тем",noThemeInstalled:"Помилка: Тему Маркетплейсу не встановлено",noThemeManifest:"Помилка: маніфест теми не знайдено",colorIniEditor:"Редактор Color.ini",colorIniEditorPlaceholder:"[your-colour-scheme-name]",invalidCSS:"Недійсний CSS"},updateModal:{title:"Оновити Маркетплейс",description:"Оновіть Spicetify Marketplace щоб отримувати нові функції і багфікси.",currentVersion:"Поточна версія: {{version}}",latestVersion:"Остання версія: {{version}}",whatsChanged:"Що змінилося",seeChangelog:"Переглянути список змін",howToUpgrade:"Як оновлюватися",viewGuide:"Переглянути посібник"},grid:{spicetifyMarketplace:"Маркетплейс Spicetify",newUpdate:"Нове оновлення",addCSS:"Додати CSS",search:"Пошук",installed:"Встановлено",lastUpdated:"Востаннє оновлено {{val, datetime}}",externalJS:"зовнішній JS",archived:"заархівоване",dark:"темний",light:"світлий",sort:{label:"Сортувати за:",stars:"Зірки",newest:"Новіші",oldest:"Старіші",lastUpdated:"Останнє оновлене",mostStale:"Найнесвіжіший",aToZ:"A-Z",zToA:"Z-A"}},readmePage:{title:"$t(grid.spicetifyMarketplace) - Readme",loading:"Завантаження...",errorLoading:"Помилка завантаження README"},github:"GitHub",install:"Встановити",remove:"Видалити",save:"Зберегти",colour_one:"колір",colour_other:"кольори",favourite:"улюблене",notifications:{wrongLocalTheme:"Будь ласка, поставте 'marketplace' у змінну current_theme у файлі config-xpui.ini щоб встановлювати теми за допомогою Маркетплейсу",tooManyRequests:"Забагато запитів, зачекайте",noCdnConnection:"Маркетплейс не може зв'язатися з CDN. Будь ласка, перевірте вашу конфігурацію Інтернету",markdownParsingError:"Помилка розбору markdown (HTTP {{status}})",noReadmeFile:"README не знайдено",themeInstallationError:"Сталася помилка при встановленні теми",extensionInstallationError:"Сталася помилка при встановленні розширення"}}},"zh-CN":{translation:{settings:{title:"设置",optionsHeading:"选项",starCountLabel:"收藏数",tagsLabel:"标签",devToolsLabel:"主題开发者工具",hideInstalledLabel:"浏览时隐藏已安装项目",colourShiftLabel:"每分钟进行色调偏移",tabsHeading:"分页",tabsDescription:"拖放更改顺序，点击启用/禁用",resetHeading:"重置",resetBtn:"$t(settings.resetHeading)",resetDescription:"卸载所有扩展插件和主题，并重置设置"},tabs:{Extensions:"扩展插件",Themes:"主题",Snippets:"微调片段",Apps:"功能模组",Installed:"已安裝项目"},snippets:{addTitle:"加入微调片段",editTitle:"编辑微调片段",viewTitle:"检视微调片段",customCSS:"自定义 CSS",customCSSPlaceholder:"这里可以输入您的自定义 CSS！您可以在「已安裝项目」标签页中看到这些片段，进而进行管理。",snippetName:"微调片段名称",snippetNamePlaceholder:"输入自定义微调片段的名称",snippetDesc:"微调片段描述",snippetDescPlaceholder:"输入自定义微调片段的描述",snippetPreview:"微调片段预览图",optional:"非必要",addImage:"加入影像",changeImage:"更改影像",saveCSS:"保存 CSS"},reloadModal:{title:"重新加载",description:"需要重新加载页面，才能完成这个操作。",reloadNow:"立即重新加载",reloadLater:"稍后重新加载"},devTools:{title:"主題开发者工具",noThemeInstalled:"错误：未安装商场主题",noThemeManifest:"错误：找不到主题内容清单",colorIniEditor:"Color.ini 编辑器",colorIniEditorPlaceholder:"[您的色彩配置名称]",invalidCSS:"CSS 无效"},grid:{spicetifyMarketplace:"Spicetify 商场",newUpdate:"有更新",addCSS:"加入 CSS",search:"搜索",installed:"已安装",lastUpdated:"上次更新于 {{val, datetime}}",externalJS:"有外部 JS",dark:"暗色模式",light:"亮色模式"},readmePage:{title:"$t(grid.spicetifyMarketplace) – 说明",loading:"正在加载……",errorLoading:"加载 README 时发生错误"},github:"GitHub",install:"安裝",remove:"移除",save:"保存",colour_one:"色彩",colour_other:"色彩",favourite:"收藏"}},"zh-TW":{translation:{settings:{title:"設定",optionsHeading:"選項",starCountLabel:"收藏數",tagsLabel:"標籤",devToolsLabel:"主題開發者工具",hideInstalledLabel:"瀏覽時隱藏已安裝項目",colourShiftLabel:"每分鐘進行色調偏移",tabsHeading:"分頁",tabsDescription:"拖放更改顺序，点击启用/禁用",resetHeading:"重設",resetBtn:"$t(settings.resetHeading)",resetDescription:"解除安裝所有擴充套件和主題， 並重設偏好設定",backupHeading:"備份與還原",backupLabel:"備份或還原所有 Marketplace 中的資料（不包含從 Marketplace 安裝的擴充元件的設定）。",backupBtn:"開啟",albumArtBasedColors:"根據專輯封面選色",albumArtBasedColorsMode:"色彩方案 (ColorApi) 模式",albumArtBasedColorsVibrancy:"已從專輯封面抽取顏色",albumArtBasedColorsVibrancyToolTip:"Desaturated：最突出但亮度較低的顏色 \n Light Vibrant：最接近 Vibrant 的色彩，但亮度稍微提升一些 \n Prominent：專輯封面裡面出現最多的色彩 \n Vibrant：專輯中最明亮的色彩",albumArtColorsModeToolTip:"Monochrome Dark：這個色彩方案直接以選擇的主色彩為基礎，但使用比較不一樣的色調並且融入灰色。這和 Monochrome Light 正好相反。 \n Monochrome Light：這個色彩方案直接以選擇的主色彩為基礎，但使用比較不一樣的色調並且融入灰色。這和 Monochrome Light 正好相反。Monochrome Light 的背景色會是 Monochrome Dark 的前景或文字顏色，反之亦然。 \n Analogic：這個色彩方案以選擇的主色彩為基礎，使用色環上主色彩鄰近的色彩。 \n Analogic Complementary：這個色彩方案以選擇的主色彩為基礎，使用色環上主色彩鄰近的色彩以及互補色。 \n Triad：這個色彩方案以選擇的主色彩為基礎，使用色環上和主色彩距離相等的顏色。 \n Quad：這個色彩方案以選擇的主色彩為基礎，使用色環上和主色彩差 90 度的顏色。",versionHeading:"版本",versionBtn:"複製",versionCopied:"已複製"},tabs:{Extensions:"擴充套件",Themes:"主題",Snippets:"微調片段",Apps:"功能模組",Installed:"已安裝項目"},snippets:{addTitle:"加入微調片段",editTitle:"編輯微調片段",viewTitle:"檢視微調片段",customCSS:"自訂 CSS",customCSSPlaceholder:"這裡可以輸入您的自訂 CSS！您可以在「已安裝項目」分頁中看到這些片段，進而進行管理。",snippetName:"微調片段名稱",snippetNamePlaceholder:"輸入自訂微調片段的名稱",snippetDesc:"微調片段描述",snippetDescPlaceholder:"輸入自訂微調片段的描述",snippetPreview:"微調片段預覽圖",optional:"非必須",addImage:"加入影像",changeImage:"更改影像",saveCSS:"儲存 CSS"},reloadModal:{title:"重新載入",description:"需要重新載入頁面，才能完成這個操作。",reloadNow:"立即重新載入",reloadLater:"稍後重新載入"},backupModal:{title:"$t(settings.backupHeading)",settingsCopied:"已將設定複製至剪貼簿",noDataPasted:"沒有貼上資料",invalidJSON:"JSON 無效",inputLabel:"Marketplace 設定",inputPlaceholder:"在此複製或貼上設定",exportBtn:"匯出",importBtn:"匯入",fileImportBtn:"從檔案匯入"},devTools:{title:"主題開發者工具",noThemeInstalled:"錯誤：沒有安裝 Marketplace 主題",noThemeManifest:"錯誤：找不到主題資訊清單",colorIniEditor:"Color.ini 編輯器",colorIniEditorPlaceholder:"[您的色彩配置名稱]",invalidCSS:"CSS 無效"},grid:{spicetifyMarketplace:"Spicetify Marketplace",newUpdate:"有更新",addCSS:"加入 CSS",search:"搜尋",installed:"已經安裝",lastUpdated:"上次更新於 {{val, datetime}}",externalJS:"有外部 JS",dark:"暗色",light:"亮色"},readmePage:{title:"$t(grid.spicetifyMarketplace) – 說明",loading:"正在載入……",errorLoading:"載入 README 時發生錯誤"},github:"GitHub",install:"安裝",remove:"移除",save:"儲存",colour_one:"色彩",colour_other:"色彩",favourite:"收藏"}}};n.use(_).use(dr).init({resources:Mn,detection:{order:["navigator","htmlTag"]},fallbackLng:"en",interpolation:{escapeValue:!1}});var ih=_r()(class extends fr.default.Component{state={count:0,CONFIG:{}};CONFIG;constructor(e){super(e);e=N(x.tabs,null);let t=[];try{if(t=e,!Array.isArray(t))throw new Error("Could not parse marketplace tabs key");if(0===t.length)throw new Error("Empty marketplace tabs key");if(0<t.filter(e=>!e).length)throw new Error("Falsey marketplace tabs key")}catch{t=zr,localStorage.setItem(x.tabs,JSON.stringify(t))}let r={},a=null;try{var n=N(x.themeInstalled,null);if(n){var o=N(n,null);if(!o)throw new Error("No installed theme data");r=o.schemes,a=o.activeScheme}else console.debug("No theme set as installed")}catch(e){console.error(e)}this.CONFIG={visual:{stars:JSON.parse(N("marketplace:stars",!0)),tags:JSON.parse(N("marketplace:tags",!0)),showArchived:JSON.parse(N("marketplace:showArchived",!1)),hideInstalled:JSON.parse(N("marketplace:hideInstalled",!1)),colorShift:JSON.parse(N("marketplace:colorShift",!1)),themeDevTools:JSON.parse(N("marketplace:themeDevTools",!1)),albumArtBasedColors:JSON.parse(N("marketplace:albumArtBasedColors",!1)),albumArtBasedColorsMode:N("marketplace:albumArtBasedColorsMode")||"monochrome-light",albumArtBasedColorsVibrancy:N("marketplace:albumArtBasedColorsVibrancy")||"PROMINENT",type:JSON.parse(N("marketplace:type",!1)),followers:JSON.parse(N("marketplace:followers",!1))},tabs:t,activeTab:N(x.activeTab,t[0]),theme:{activeThemeKey:N(x.themeInstalled,null),schemes:r,activeScheme:a},sort:N(x.sort,"stars")},this.CONFIG.activeTab&&this.CONFIG.tabs.filter(e=>e.name===this.CONFIG.activeTab).length||(this.CONFIG.activeTab=this.CONFIG.tabs[0].name)}updateConfig=e=>{this.CONFIG={...e},console.debug("updated config",this.CONFIG),this.setState({CONFIG:{...e}})};render(){var{location:e,replace:t}=Spicetify.Platform.History;return e.pathname===Ur+"/readme"?e.state?.data?fr.default.createElement(oh,{title:y("readmePage.title"),data:e.state.data}):(t(Ur),null):fr.default.createElement(ah,{title:y("grid.spicetifyMarketplace"),CONFIG:this.CONFIG,updateAppConfig:this.updateConfig})}}),sh=t(r());return Jn=Ye,M(T({},"__esModule",{value:!0}),Jn)})();const render=()=>marketplace.default();